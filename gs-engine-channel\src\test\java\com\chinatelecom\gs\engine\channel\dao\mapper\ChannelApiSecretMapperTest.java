package com.chinatelecom.gs.engine.channel.dao.mapper;

import com.chinatelecom.gs.engine.channel.dao.po.ChannelApiSecretPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ChannelApiSecretMapperTest {
    @Mock
    private ChannelApiSecretMapper channelApiSecretMapper;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void testSelectById() {
        // Arrange
        long id = 1L;
        ChannelApiSecretPO expectedPo = new ChannelApiSecretPO();
        expectedPo.setId(id);
        expectedPo.setSecret("secret123");
        expectedPo.setChannelId("channel1");

        when(channelApiSecretMapper.selectById(id)).thenReturn(expectedPo);

        // Act
        ChannelApiSecretPO actualPo = channelApiSecretMapper.selectById(id);

        // Assert
        assertNotNull(actualPo);
        assertEquals(expectedPo.getId(), actualPo.getId());
        assertEquals(expectedPo.getSecret(), actualPo.getSecret());
        assertEquals(expectedPo.getChannelId(), actualPo.getChannelId());
        verify(channelApiSecretMapper).selectById(id);
    }
}