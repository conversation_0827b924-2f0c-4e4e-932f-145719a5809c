package com.chinatelecom.gs.engine.channel.manage;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelConfigPO;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelConfigRepository;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 *
 */
@Service
public class ChannelConfigService {
    @Resource
    private ChannelConfigRepository channelConfigRepository;

    @Resource
    private ChannelSecretManagerService channelSecretManagerService;

    @Resource
    private WebLinkConfigService webLinkConfigService;

    private static final String defaultSecretName = "未命名";


    /**
     * 查询渠道configvalue
     *
     * @return
     */
    public String getChannelConfigValue(String channelId, String configKey) {
        LambdaQueryWrapper<ChannelConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelConfigPO::getChannelId, channelId)
                .eq(ChannelConfigPO::getConfigKey, configKey);
        return channelConfigRepository.getOne(queryWrapper).getConfigValue();
    }

    public boolean isConfigExist(ChannelInfoDTO channelInfo) {
        if (ChannelTypeEnum.QYWX_APP.equals(channelInfo.getChannelType()) || ChannelTypeEnum.WEB_LINK.equals(channelInfo.getChannelType())) {
            return isChannelConfigExist(channelInfo.getChannelId());
        } else if (ChannelTypeEnum.API.equals(channelInfo.getChannelType())) {
            return channelSecretManagerService.isSecretExist(channelInfo.getAgentCode(), ApiSecretType.API);
        }
        throw new BizException("A0054", "渠道种类不存在");
    }


    /**
     * 判断渠道配置信息是否存在
     *
     * @param channelId
     * @return
     */
    private boolean isChannelConfigExist(String channelId) {
        if (CharSequenceUtil.isBlank(channelId)) {
            throw new BizException("A0053", "渠道id不能为空");
        }

        LambdaQueryWrapper<ChannelConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelConfigPO::getChannelId, channelId);
        List<ChannelConfigPO> channelConfigs = channelConfigRepository.list(queryWrapper);

        return !CollectionUtils.isEmpty(channelConfigs);
    }

    public String getConfigValueWithId(String id) {
        return channelConfigRepository.getById(id).getConfigValue();
    }
}
