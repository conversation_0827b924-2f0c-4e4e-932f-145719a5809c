package com.chinatelecom.gs.engine.channel.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @USER: pengmc1
 * @DATE: 2023/8/15 10:47
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "输入联想请求")
public class InputSearchReqParam extends CommonAuthParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 搜索词
     */
    @Schema(description = "搜索词")
    @NotBlank(message = "搜索词不能为空！")
    private String query;

    /**
     * 是否test模式
     */
    @Schema(description = "是否test模式")
    @NotNull(message = "当前环境是否test模式不能为空！")
    private Boolean test;



}
