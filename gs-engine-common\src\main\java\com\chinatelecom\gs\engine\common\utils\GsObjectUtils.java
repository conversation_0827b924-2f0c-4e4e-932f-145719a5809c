package com.chinatelecom.gs.engine.common.utils;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年07月28日
 */
public class GsObjectUtils {

    private GsObjectUtils() {
    }

    /**
     * 安全获取某项值
     * @param supplier
     * @return
     * @param <T>
     */
    public static <T> T safeGet(Supplier<T> supplier) {
        if (supplier == null) {
            return null;
        }
        try {
            return supplier.get();
        } catch (NullPointerException e) {
            return null;
        }
    }

}
