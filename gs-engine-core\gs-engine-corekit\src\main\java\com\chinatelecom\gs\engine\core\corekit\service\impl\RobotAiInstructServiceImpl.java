package com.chinatelecom.gs.engine.core.corekit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.infra.base.impl.BaseExtendServiceImpl;
import com.chinatelecom.gs.engine.core.corekit.domain.po.RobotAiInstructPO;
import com.chinatelecom.gs.engine.core.corekit.domain.request.RobotAiInstructPageRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.request.RobotAiInstructRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.response.RobotAiInstructResponse;
import com.chinatelecom.gs.engine.core.corekit.domain.vo.RobotAiInstructPageVO;
import com.chinatelecom.gs.engine.core.corekit.enums.InstructDefaultCodeEnum;
import com.chinatelecom.gs.engine.core.corekit.mapper.RobotAiInstructMapper;
import com.chinatelecom.gs.engine.core.corekit.service.RobotAiInstructService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;


/**
 * @author: Wei
 * @date: 2025-02-05 09:19
 */
@Slf4j
@Service
public class RobotAiInstructServiceImpl extends BaseExtendServiceImpl<RobotAiInstructMapper, RobotAiInstructPO> implements RobotAiInstructService {

    @PostConstruct
    public void init(){
        //初始化内置指令
        log.info("初始化系统指令");
        List<String> defaultCodes = Arrays.stream(InstructDefaultCodeEnum.values()).map(InstructDefaultCodeEnum::getCode).collect(Collectors.toList());
        Set<String> codes = this.list(Wrappers.<RobotAiInstructPO>lambdaQuery()
                .in(RobotAiInstructPO::getInstructCode, defaultCodes)
                .select(RobotAiInstructPO::getInstructCode)).
                stream()
                .map(RobotAiInstructPO::getInstructCode).collect(Collectors.toSet());
        List<RobotAiInstructPO> instructPOS = defaultCodes.stream()
                .filter(code -> !codes.contains(code))
                .map(code -> {
                    RobotAiInstructPO po = new RobotAiInstructPO();
                    po.setInstructCode(code);
                    po.setInstructDesc(InstructDefaultCodeEnum.getDescByCode(code));
                    po.setIsSystem(1);
                    return po;
                }).collect(Collectors.toList());
        this.saveBatch(instructPOS);
    }

    @Override
    public Boolean saveInstruct(RobotAiInstructRequest request) {
        saveCheck(request);
        RobotAiInstructPO robotAiInstructPO = BeanUtil.copyProperties(request, RobotAiInstructPO.class);
        robotAiInstructPO.setIsSystem(0);
        if(CollectionUtils.isNotEmpty(request.getInstructParams())){
            robotAiInstructPO.setInstructParam(JSON.toJSONString(request.getInstructParams()));
        }
        return saveOrUpdate(robotAiInstructPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(List<String> ids) {
        //判断是否系统指令
        List<Long> idList = ids.stream().map(Long::parseLong).collect(Collectors.toList());
        checkSysCode(idList);
        return this.removeBatchByIds(ids);
    }

    @Override
    public Page<RobotAiInstructPageVO> pageQuery(RobotAiInstructPageRequest request) {
        String appCode = RequestContext.getAppCode();
        IPage<RobotAiInstructPO> page = new PageDTO<>(request.getPageNum(), request.getPageSize());
        IPage<RobotAiInstructPO> pageRecord = this.page(page, Wrappers.<RobotAiInstructPO>lambdaQuery()
                .and(wrapper -> wrapper
                        .eq(RobotAiInstructPO::getAppCode, appCode)
                        .or()
                        .eq(RobotAiInstructPO::getIsSystem, 1)
                )
                .and(StringUtils.isNotEmpty(request.getKeyword()), wrapper -> wrapper
                        .like(RobotAiInstructPO::getInstructCode, request.getKeyword())
                        .or()
                        .like(RobotAiInstructPO::getInstructDesc, request.getKeyword()))
                .orderByDesc(RobotAiInstructPO::getUpdateTime));

        PageImpl pageVo = new PageImpl();
        pageVo.setPages(pageRecord.getPages());
        pageVo.setCurrent(pageRecord.getCurrent());
        pageVo.setSize(pageRecord.getSize());
        pageVo.setTotal(pageRecord.getTotal());
        List<RobotAiInstructPageVO> records = pageRecord.getRecords()
                .stream().map(record -> {
                    RobotAiInstructPageVO robotAiInstructPageVO = BeanUtil.copyProperties(record, RobotAiInstructPageVO.class);
                    if(StringUtils.isNotBlank(record.getInstructParam())){
                        robotAiInstructPageVO.setInstructParams(JSON.parseArray(record.getInstructParam(), RobotAiInstructResponse.InstructParam.class));
                    }
                    return robotAiInstructPageVO;
                }).collect(Collectors.toList());
        pageVo.setRecords(records);
        return pageVo;
    }

    @Override
    public List<RobotAiInstructPageVO> listQuery(RobotAiInstructPageRequest request) {
        List<RobotAiInstructPO> list = this.list(Wrappers.<RobotAiInstructPO>lambdaQuery()
                .and(wrapper -> wrapper
                        .eq(RobotAiInstructPO::getAppCode, RequestContext.getAppCode())
                        .or()
                        .eq(RobotAiInstructPO::getIsSystem, 1)
                )
                .and(StringUtils.isNotEmpty(request.getKeyword()), wrapper -> wrapper
                        .like(RobotAiInstructPO::getInstructCode, request.getKeyword())
                        .or()
                        .like(RobotAiInstructPO::getInstructDesc, request.getKeyword()))
                .orderByDesc(RobotAiInstructPO::getUpdateTime));
        List<RobotAiInstructPageVO> records = list
                .stream().map(record -> {
                    RobotAiInstructPageVO robotAiInstructPageVO = BeanUtil.copyProperties(record, RobotAiInstructPageVO.class);
                    if(StringUtils.isNotBlank(record.getInstructParam())){
                        robotAiInstructPageVO.setInstructParams(JSON.parseArray(record.getInstructParam(), RobotAiInstructResponse.InstructParam.class));
                    }
                    return robotAiInstructPageVO;
                }).collect(Collectors.toList());
        return records;
    }

    @Override
    public RobotAiInstructResponse detail(String instructCode) {
        RobotAiInstructPO instructPO = this.getOne(Wrappers.<RobotAiInstructPO>lambdaQuery()
                .eq(RobotAiInstructPO::getInstructCode, instructCode)
                .and(wrapper -> wrapper
                        .eq(RobotAiInstructPO::getAppCode, RequestContext.getAppCode())
                        .or()
                        .eq(RobotAiInstructPO::getIsSystem, 1)
                ).last("limit 1"));
        if(Objects.isNull(instructPO)){
            return null;
        }
        RobotAiInstructResponse robotAiInstructResponse = buildInstructResponse(instructPO);
        return robotAiInstructResponse;
    }

    private RobotAiInstructResponse buildInstructResponse(RobotAiInstructPO instructPO){
        RobotAiInstructResponse robotAiInstructResponse = BeanUtil.copyProperties(instructPO, RobotAiInstructResponse.class);
        if(StringUtils.isNotBlank(instructPO.getInstructParam())){
            robotAiInstructResponse.setInstructParams(JSON.parseArray(instructPO.getInstructParam(), RobotAiInstructResponse.InstructParam.class));
        }
        return robotAiInstructResponse;
    }

    private void saveCheck(RobotAiInstructRequest request){
        //判断是否系统指令
        checkSysCode(Lists.newArrayList(request.getId()));
        Long count = baseMapper.selectCount(Wrappers.<RobotAiInstructPO>lambdaQuery()
                .and(wrapper -> wrapper
                        .eq(RobotAiInstructPO::getAppCode, RequestContext.getAppCode())
                        .or()
                        .eq(RobotAiInstructPO::getIsSystem, 1)
                )
                .eq(RobotAiInstructPO::getInstructCode, request.getInstructCode())
                .ne(Objects.nonNull(request.getId()), RobotAiInstructPO::getId, request.getId()));
        if (count > 0) {
            throw new BizException("A0001","指令已存在");
        }
        if(CollectionUtils.isNotEmpty(request.getInstructParams())){
            Set<String> collect = request.getInstructParams().stream().map(RobotAiInstructRequest.InstructParam::getParamName).collect(Collectors.toSet());
            if (collect.size() != request.getInstructParams().size()) {
                throw new BizException("A0001","参数名称重复");
            }
        }
    }

    private void checkSysCode(List<Long> ids){
        //判断是否系统指令
        if(CollectionUtils.isNotEmpty(ids)){
            List<RobotAiInstructPO> pos = baseMapper.selectBatchIds(ids);
            pos.forEach(po ->{
                if(Objects.nonNull(po) && 1 == po.getIsSystem()){
                    throw new BizException("A0001","系统指令不允许操作");
                }
            });
        }
    }
}
