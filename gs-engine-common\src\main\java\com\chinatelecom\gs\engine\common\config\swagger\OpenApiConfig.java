package com.chinatelecom.gs.engine.common.config.swagger;


import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * SwaggerConfig
 * 传统swagger访问地址： http://localhost:8092/ais/swagger-ui/index.html
 * knife4j风格地址： http://localhost:8092/ais/doc.html
 *
 * <AUTHOR>
 * @date 2022-10-15 20:32
 */
@Configuration
public class OpenApiConfig {

    public static final String[] GS_PACKAGES = {"com.chinatelecom.gs.engine", "com.chinatelecom.gs.workflow", "com.chinatelecom.gs.plugin", "com.chinatelelcom.gs.engine.sdk", "com.telecom.ais.telephone"};

    /**
     * 定义全局的 API 信息
     *
     * @return
     */
    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("星海智文V2.0_OpenAPI设计文档")
                        .description("星海智文V2.0_OpenAPI设计文档")
                        .version("2.x.x")
                );
    }


    @Bean
    public GroupedOpenApi webApi() {
        return GroupedOpenApi.builder()
                .group("1.Web端接口")
                .pathsToMatch("/**/web/**")
                .packagesToScan(GS_PACKAGES)
                // Web端接口暴露所有参数，不使用任何定制器
                .build();
    }


    @Bean
    public GroupedOpenApi openApi() {
        return GroupedOpenApi.builder()
                .group("2.对外开放接口 (OpenAPI)")
                .pathsToMatch("/**/openapi/**")
                .packagesToScan(GS_PACKAGES)
                // 添加OpenAPI类型的定制器，用于隐藏标记的字段、接口和参数
                .addOpenApiCustomizer(new SmartApiTypeSchemaCustomizer(ApiType.OPENAPI))
                .addOperationCustomizer(new ApiTypeOperationCustomizer(ApiType.OPENAPI))
                .addOperationCustomizer(new ApiTypeParameterCustomizer(ApiType.OPENAPI))
                .addOperationCustomizer(new OpenApiHeaderParameterCustomizer(ApiType.OPENAPI))
                .build();
    }

    @Bean
    public GroupedOpenApi rpcApi() {
        return GroupedOpenApi.builder()
                .group("3.内部服务接口 (RPC)")
                .pathsToMatch("/**/rpc/**")
                .packagesToScan(GS_PACKAGES)
                // 添加RPC类型的定制器，用于隐藏标记的字段、接口和参数
                .addOpenApiCustomizer(new SmartApiTypeSchemaCustomizer(ApiType.RPC))
                .addOperationCustomizer(new ApiTypeOperationCustomizer(ApiType.RPC))
                .addOperationCustomizer(new ApiTypeParameterCustomizer(ApiType.RPC))
                .build();
    }

    /**
     * 其他接口分组
     * 匹配所有不属于其他分组的接口
     */
    @Bean
    public GroupedOpenApi otherApi() {
        return GroupedOpenApi.builder()
                .group("4.其他接口 (Other API)")
                .pathsToMatch("/**")
                .pathsToExclude("/**/rpc/**","/**/web/**","/**/openapi/**")
                .packagesToScan(GS_PACKAGES)
                // 其他接口不使用定制器，保持原样
                .build();
    }
}