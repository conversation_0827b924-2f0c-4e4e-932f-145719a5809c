package com.chinatelecom.gs.engine.common.enums;

import lombok.Getter;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */
@Getter
public enum BotTypeEnum {
    ONLINE_ROBOT(1, "在线机器人"),
    CALL_IN_ROBOT(2, "呼入机器人"),
    CALL_OUT_ROBOT(3, "呼出机器人"),
    APP_ROBOT(5, "应用机器人"),
    /**
     * 助手bot
     */
    ASSISTIVE_ROBOT(6, "辅助机器人")
    ;
    private Integer code;
    private String name;

    BotTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private static final LinkedHashMap<Integer, BotTypeEnum> map = new LinkedHashMap<>();

    public static BotTypeEnum getByCode(Integer code) {
        if (map.isEmpty()) {
            for (BotTypeEnum value : BotTypeEnum.values()) {
                map.put(value.getCode(), value);
            }
        }
        return map.get(code);
    }
}
