package com.chinatelecom.gs.engine.channel.foundation;

import com.chinatelecom.gs.engine.channel.service.dto.RobotConfigDTO;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.center.PlatformDialogCenterApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.ChatTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.FinalMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.MessageRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.BotAnswer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/12/22 17:18
 * @description
 */
@Slf4j
@Service
public class BotPlatformDialog {
    @Resource
    private PlatformDialogCenterApi platformDialogCenterApi;

    private static final String ENTRY = "default";

    public List<BotAnswer> chat(String userId, String sessionId, String messageId, String message, RobotConfigDTO robotConfig) {
        MessageRequest request = new MessageRequest();


        request.setChatType(ChatTypeEnum.CHAT.getCode());
        request.setAgentCode(robotConfig.getBotCode());
        request.setContent(message);
        request.setSessionId(sessionId);
        request.setMessageId(messageId);
        request.setUserId(userId);
        FinalMessageResponse dialogResponse = null;
        try {
            log.info("新机器人平台请求: {}", request);
            Result<FinalMessageResponse> result = platformDialogCenterApi.dialog(request);
            if (Boolean.FALSE.equals(result.isSuccess()) || result.getData() == null) {
                log.error("新机器人平台响应结果异常,result:{}", result);
                return Collections.emptyList();
            }

            dialogResponse = result.getData();
            log.info("新机器人平台响应结果:{}", dialogResponse);
        } catch (Exception e) {
            log.error("请求新机器人平台对话接口失败", e);
            return Collections.emptyList();
        }

        return dialogResponse.getAnswers();
    }
}
