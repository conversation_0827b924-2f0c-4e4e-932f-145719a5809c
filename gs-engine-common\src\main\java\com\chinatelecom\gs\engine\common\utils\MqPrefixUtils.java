package com.chinatelecom.gs.engine.common.utils;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年08月11日
 */
public class MqPrefixUtils {
    private static GsGlobalConfig gsGlobalConfig = null;

    private MqPrefixUtils() {
    }

    public static String buildByPrefix(String... key) {
        if (gsGlobalConfig == null) {
            gsGlobalConfig = SpringContextUtils.getBean(GsGlobalConfig.class);
        }
        return StringUtils.join(gsGlobalConfig.getSystem().getMqPrefix(), gsGlobalConfig.getSystem().getEnv(), StringUtils.join(key));
    }
}
