package com.chinatelecom.gs.engine.config.filter.interceptor;

import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.rpc.UserInfo;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelecom.gs.engine.common.utils.UserInfoUtils;
import com.chinatelecom.gs.engine.config.filter.BaseHandlerInterceptor;
import com.chinatelecom.gs.engine.utils.CheckIsAdminService;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
@Component
@Slf4j
public class OpenApiContextInterceptor implements BaseHandlerInterceptor {

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    @Resource
    private CheckIsAdminService checkIsAdminService;



    public boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        String userId = ((HttpServletRequest) request).getHeader(HeaderKeys.USER_ID);
        if (StringUtils.isBlank(userId)) {
            return InterceptorUtils.writeError((HttpServletResponse) response, "A0023", "未设置x-userid请求头");
        }
        String tenantId = ((HttpServletRequest) request).getHeader(HeaderKeys.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return InterceptorUtils.writeError((HttpServletResponse) response, "A0024", "未设置x-tenantid请求头");
        }
        UserInfo userInfo = UserInfoUtils.getUserInfo(tenantId, userId);
        if(userInfo == null) {
            return InterceptorUtils.writeError((HttpServletResponse) response, "A0025", "用户信息不存在");
        }

        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setTenant(true);
        requestInfo.setRequestSourceType(RequestSourceType.OPENAPI);
        RequestContext.set(requestInfo);
        requestInfo.setUserId(userId);
        requestInfo.setTenantId(tenantId);
        requestInfo.setUserName(userInfo.getName());
        requestInfo.setTeam(UserInfoUtils.getUserTeam());
        requestInfo.setIsSuperTenant(superTenant.equals(tenantId));
        requestInfo.setEntry("openApi");
        InterceptorUtils.setAppCode(((HttpServletRequest) request), requestInfo);

        PlatformUser platformUser = PlatformUser.builder()
                .userId(userId)
                .username(userInfo.getName())
                .corpCode(tenantId)
                .isAdmin(UserInfoUtils.isAdmin(tenantId, userId))
                .build();
        SsoUtil.add(platformUser);
        InterceptorUtils.setSourceTypeByRpc(((HttpServletRequest) request), requestInfo);
        requestInfo.setIsAdmin(checkIsAdminService.checkIsAdmin(requestInfo.getAppSourceType(), requestInfo.getUserId(), requestInfo.getTenantId()));
        return true;
    }

    /**
     * 完成后调用
     *
     * @param request
     * @param response
     * @param ex
     * @throws Exception
     */
    public void afterCompletion(ServletRequest request, ServletResponse response, Exception ex) throws Exception {
        RequestContext.remove();
    }

}
