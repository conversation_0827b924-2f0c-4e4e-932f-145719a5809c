package com.chinatelecom.gs.engine.common.config.swagger;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * API分组注解 - 知识库
 * 用于标记API属于知识库分组
 *
 * <AUTHOR>
 * @date 2025-08-20
 */

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Operation(extensions = {
        @Extension(
                name = "api",
                properties = {
                        @ExtensionProperty(name = "group", value = "数据建模")
                }
        )
})
public @interface ApiGroupKnowledgeBase {
    String summary() default "";
    String description() default "";
}