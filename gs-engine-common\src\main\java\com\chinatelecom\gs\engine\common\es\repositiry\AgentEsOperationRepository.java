package com.chinatelecom.gs.engine.common.es.repositiry;

//import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.data.annotation.Id;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Setting;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.ByQueryResponse;
import org.springframework.data.elasticsearch.core.query.Query;

import java.util.List;
import java.util.function.Function;


public interface AgentEsOperationRepository {

    /**
     * 创建索引
     *
     * @param index
     *         索引名称
     * @param entityClass
     *         PO实体类，需要带ES的注解 {@link Document}, {@link Setting}, {@link Id}等
     */
    boolean createIndex(String index, Class<?> entityClass);

    /**
     * 删除索引
     *
     * @param index
     *         索引名称
     */
    boolean deleteIndex(String index);

    /**
     * 单个保存
     *
     * @param index
     *         索引名称
     * @param poClass
     *         数据
     * @param <T>
     *         数据类型必须是PO实体类
     */
    <T> void save(String index, T poClass);

    /**
     * 批量保存或更新，根据ID判断如果文档不存在就创建，存在就覆盖
     *
     * @param index
     *         索引名称
     * @param dataList
     *         批量数据
     * @param <T>
     *         数据类型必须是PO实体类，需要带ES的注解 {@link Document}, {@link Id}等
     */
    <T> void bulkSaveOrUpdate(String index, List<T> dataList);

    /**
     * 批量保存或更新，根据ID判断如果文档不存在就创建，存在就覆盖
     *
     * @param index
     *         索引名称
     * @param dataList
     *         批量数据
     * @param idExtractor
     *         获取数据ID的方法
     * @param <T>
     *         数据类型必须是PO实体类或者Map类型，Map类型需提供获取数据ID的方法
     */
    <T> void bulkSaveOrUpdate(String index, List<T> dataList, Function<T, String> idExtractor);

    /**
     * 批量更新数据，如果ID不存在会报错，存在就部分更新（只覆盖有数据的字段）
     *
     * @param index
     *         索引名称
     * @param dataList
     *         批量数据
     * @param <T>
     *         数据类型必须是PO实体类，需要带ES的注解 {@link Document}, {@link Id}等
     */
    <T> void bulkUpdate(String index, List<T> dataList);

    /**
     * 批量更新数据，如果ID不存在会报错，存在就部分更新（只覆盖有数据的字段）
     *
     * @param index
     *         索引名称
     * @param dataList
     *         批量数据
     * @param idExtractor
     *         获取数据ID的方法
     * @param <T>
     *         数据类型必须是PO实体类或者Map类型，Map类型需提供获取数据ID的方法
     */
    <T> void bulkUpdate(String index, List<T> dataList, Function<T, String> idExtractor);

//    /**
//     * 搜索
//     *
//     * @param index
//     *         索引名称
//     * @param poClass
//     *         PO实体类
//     * @param queryBuilder
//     *         匹配query
//     * @param <T>
//     *         数据类型必须是PO实体类
//     */
//    <T> SearchHits<T> search(String index, Class<T> poClass, QueryBuilder queryBuilder);
//
//    /**
//     * 搜索
//     *
//     * @param index
//     *         索引名称
//     * @param poClass
//     *         PO实体类
//     * @param queryBuilder
//     *         匹配QueryBuilder
//     * @param pageable
//     *         分页
//     * @param <T>
//     *         数据类型必须是PO实体类
//     */
//    <T> SearchHits<T> search(String index, Class<T> poClass, QueryBuilder queryBuilder, Pageable pageable);

    /**
     * 搜索
     *
     * @param index
     *         索引名称
     * @param poClass
     *         PO实体类
     * @param query
     *         搜索query
     * @param <T>
     *         数据类型必须是PO实体类
     */
    <T> SearchHits<T> search(String index, Class<T> poClass, Query query);

    <T> SearchHit<T> searchOne(String index, Class<T> poClass, Query query);

    /**
     * 是否存在
     *
     * @param index
     *         索引名称
     */
    boolean exist(String index);

    /**
     * 删除 删除索引数据
     *
     * @param index
     *         索引名称
     * @param poClass
     *         对象
     * @param query
     *         查询条件
     * @param <T>
     *         对象类型
     *
     * @return 操作结果
     */
    <T> ByQueryResponse delete(String index, Class<T> poClass, Query query);
}
