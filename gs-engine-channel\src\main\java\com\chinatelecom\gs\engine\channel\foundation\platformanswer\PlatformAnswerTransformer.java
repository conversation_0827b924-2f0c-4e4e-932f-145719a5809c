package com.chinatelecom.gs.engine.channel.foundation.platformanswer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.channel.common.AccessTokenUtil;
import com.chinatelecom.gs.engine.channel.common.enums.QywxMessageTypeEnum;
import com.chinatelecom.gs.engine.channel.common.utils.HtmlUtils;
import com.chinatelecom.gs.engine.channel.common.utils.HttpUtils;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelMediaPO;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelMediaRepository;
import com.chinatelecom.gs.engine.channel.service.dto.*;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.BotAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.entry.RichTextCard;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.chinatelecom.gs.engine.channel.common.constants.ChatConstants.*;

/**
 * <AUTHOR>
 * @date 2023/12/25 10:48
 * @description
 */
@Slf4j
@Service
public class PlatformAnswerTransformer {

    @Resource
    private AccessTokenUtil tokenUtil;

    @Resource
    private ChannelMediaRepository channelMediaRepository;

    @Value("${weixin.upload.media-url:}")
    private String weixinUplpadMediaUrl;

    private static final String IMG_REGEX = "<img[^>]+src=\"([^\"]+)\"[^>]+>";

    private static final String VIDEO_REGEX = "<source[^>]+src=\"([^\"]+)\"[^>]+>";

    //过期时间2天
    private static final Integer EXPIRE_TIME = 1000 * 60 * 60 * 24 * 3 - 1000 * 60 * 5;

    private static final int TEXT_CONTENT_LENGTH = 650;

    public List<BaseSendMessageDTO> convertCsrobotAnswer(List<BotAnswer> answers, String userId, Integer agentId, String channelId, String corpId) {
        List<BaseSendMessageDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(answers)) {
            log.warn("答案列表为空");
            result.add(getTextMsg(userId, agentId, DEFAULT_ANSWER_CONTENT));
            return result;
        }
        for (BotAnswer answer : answers) {
            String value = null;
            if (AnswerTypeEnum.SIMPLE_RICH_TEXT.getCode().equals(answer.getAnswerType().getCode())) {
                List<String> urls = extractImg(JSON.toJSONString(answer.getContent()));
                if (!CollectionUtils.isEmpty(urls)) {
                    result.addAll(getImageList(urls, channelId, userId, agentId, corpId));
                }
                List<String> videoUrls = extractVideo(JSON.toJSONString(answer.getContent()));
                if (!CollectionUtils.isEmpty(videoUrls)) {
                    result.addAll(getVideoList(videoUrls, channelId, userId, agentId, corpId));
                }
                value = processRichText(answer);
//            } else if (AnswerTypeEnum.FOLD_CARD.getCode().equals(answer.getAnswerType().getCode())) {
//                value = processFoldCard(answer);
            } else if (AnswerTypeEnum.PLAIN_TEXT.getCode().equals(answer.getAnswerType().getCode())) {
                value = processPlainText(answer);
//            }else if (AnswerTypeEnum.OPTION_CARD.getCode().equals(answer.getAnswerType().getCode())) {
//                value = processOptionCard(answer);
//            } else if (AnswerTypeEnum.IMG_TEXT_CARD.getCode().equals(answer.getAnswerType().getCode())) {
//                List<String> urls = getImgTextCardUrls(answer);
//                if (CollectionUtils.isNotEmpty(urls)) {
//                    result.addAll(getImageList(urls, channelId, userId, agentId, corpId));
//                }
//                value = processImgTextCard(answer);
            }
            if (StringUtils.isNotBlank(value)) {
                result.addAll(getTextMsgList(userId, agentId, value));
            }
        }

        return result;
    }

    public String processRichTextTest(BotAnswer answer) {
        return processRichText(answer);
    }

    //处理富文本答案
    private String processRichText(BotAnswer answer) {
        if (answer == null || answer.getContent() == null) {
            return "";
        }

        RichTextCard richTextProtocol = null;
        if (answer.getContent() instanceof String) {
            richTextProtocol = JSON.parseObject(answer.getContent().toString(), RichTextCard.class);
        } else {
            richTextProtocol = JSON.parseObject(JSON.toJSONString(answer.getContent()), RichTextCard.class);
        }

        if (richTextProtocol == null) {
            return "";
        }

        String html = richTextProtocol.getHtml();
        if (html == null) {
            return "";
        }

        return HtmlUtils.delHTMLTag(html);
    }



    //处理富文本答案
    private String processPlainText(BotAnswer answer) {
        return answer.getContent().toString();
    }


    public List<String> extractImgTest(String content) {
        return extractImg(content);
    }

    private List<String> extractImg(String content) {
        List<String> result = new ArrayList<>();
        try {
            if (StringUtils.isEmpty(content)) {
                return result;
            }
            String unescaped = content.replaceAll("\\\\", "");
            Pattern pattern = Pattern.compile(IMG_REGEX);
            Matcher matcher = pattern.matcher(unescaped);
            while (matcher.find()) {
                String str = matcher.group(1);
                if (StringUtils.isNotEmpty(str)) {
                    result.add(matcher.group(1));
                }
            }
        } catch (Exception e) {
            log.error("从富文本提取图片失败", e);
        }
        log.info("从富文本提取图片结果：{}", JsonUtils.toJsonString(result));
        return result;
    }

    private List<String> extractVideo(String content) {
        List<String> result = new ArrayList<>();
        try {
            if (StringUtils.isEmpty(content)) {
                return result;
            }
            String unescaped = content.replaceAll("\\\\", "");
            Pattern pattern = Pattern.compile(VIDEO_REGEX);
            Matcher matcher = pattern.matcher(unescaped);
            while (matcher.find()) {
                String str = matcher.group(1);
                if (StringUtils.isNotEmpty(str)) {
                    result.add(matcher.group(1));
                }
            }
        } catch (Exception e) {
            log.error("从富文本提取视频失败", e);
        }
        log.info("从富文本提取视频结果：{}", JsonUtils.toJsonString(result));
        return result;
    }

    private  List<TextMessageDTO> getTextMsgList(String userId, Integer agentId, String content) {
        List<TextMessageDTO> textMessageDTOS = new ArrayList<>();

        int i =0;
        while (i < content.length()){
            String word = content.substring(i, Math.min(content.length(), i + TEXT_CONTENT_LENGTH));
            if(i > 0){
                word = "（接上文）"+word;
            }
            i += TEXT_CONTENT_LENGTH;
            if(i < content.length()){
                word = word+"（未完待续）";
            }
            textMessageDTOS.add(getTextMsg(userId,agentId,word));
        }

        return textMessageDTOS;
    }

    private  TextMessageDTO getTextMsg(String userId, Integer agentId, String content) {
        JSONObject text = new JSONObject();
        text.put(CONTENT, content);
        TextMessageDTO messageDTO = new TextMessageDTO();
        messageDTO.setText(text);
        messageDTO.setAgentid(agentId);
        messageDTO.setTouser(userId);
        messageDTO.setMsgtype(QywxMessageTypeEnum.TEXT.getCode());
        messageDTO.setSafe(SAFE);
        return messageDTO;
    }

    private ImageMessageDTO getImageMsg(String userId, Integer agentId, String mediaId) {
        JSONObject image = new JSONObject();
        image.put(MEDIA_ID, mediaId);
        ImageMessageDTO messageDTO = new ImageMessageDTO();
        messageDTO.setImage(image);
        messageDTO.setAgentid(agentId);
        messageDTO.setTouser(userId);
        messageDTO.setMsgtype(QywxMessageTypeEnum.IMAGE.getCode());
        messageDTO.setSafe(SAFE);
        return messageDTO;
    }

    private VoiceMessageDTO getVoiceMsg(String userId, Integer agentId, String mediaId) {
        JSONObject voice = new JSONObject();
        voice.put(MEDIA_ID, mediaId);
        VoiceMessageDTO messageDTO = new VoiceMessageDTO();
        messageDTO.setVoice(voice);
        messageDTO.setAgentid(agentId);
        messageDTO.setTouser(userId);
        messageDTO.setMsgtype(QywxMessageTypeEnum.VOICE.getCode());
        messageDTO.setSafe(SAFE);
        return messageDTO;
    }

    private VideoMessageDTO getVideoMsg(String userId, Integer agentId, String mediaId) {
        JSONObject video = new JSONObject();
        video.put(MEDIA_ID, mediaId);
        VideoMessageDTO messageDTO = new VideoMessageDTO();
        messageDTO.setVideo(video);
        messageDTO.setAgentid(agentId);
        messageDTO.setTouser(userId);
        messageDTO.setMsgtype(QywxMessageTypeEnum.VIDEO.getCode());
        messageDTO.setSafe(SAFE);
        return messageDTO;
    }

    private FileMessageDTO getFileMsg(String userId, Integer agentId, String mediaId) {
        JSONObject file = new JSONObject();
        file.put(MEDIA_ID, mediaId);
        FileMessageDTO messageDTO = new FileMessageDTO();
        messageDTO.setFile(file);
        messageDTO.setAgentid(agentId);
        messageDTO.setTouser(userId);
        messageDTO.setMsgtype(QywxMessageTypeEnum.FILE.getCode());
        messageDTO.setSafe(SAFE);
        return messageDTO;
    }

    private List<ImageMessageDTO> getImageList(List<String> urls, String channelId, String userId, Integer agentId, String corpId) {
        List<ImageMessageDTO> result = new ArrayList<>();
        for (String url : urls) {
            String mediaId = getMediaId(corpId, channelId, url, QywxMessageTypeEnum.IMAGE);
            if (StringUtils.isEmpty(mediaId)) {
                continue;
            }
            result.add(getImageMsg(userId, agentId, mediaId));
        }
        return result;
    }

    private List<VoiceMessageDTO> getVoiceList(List<String> urls, String channelId, String userId, Integer agentId, String corpId) {
        List<VoiceMessageDTO> result = new ArrayList<>();
        for (String url : urls) {
            String mediaId = getMediaId(corpId, channelId, url, QywxMessageTypeEnum.VOICE);
            if (StringUtils.isEmpty(mediaId)) {
                continue;
            }
            result.add(getVoiceMsg(userId, agentId, mediaId));
        }
        return result;
    }

    private List<VideoMessageDTO> getVideoList(List<String> urls, String channelId, String userId, Integer agentId, String corpId) {
        List<VideoMessageDTO> result = new ArrayList<>();
        for (String url : urls) {
            String mediaId = getMediaId(corpId, channelId, url, QywxMessageTypeEnum.VIDEO);
            if (StringUtils.isEmpty(mediaId)) {
                continue;
            }
            result.add(getVideoMsg(userId, agentId, mediaId));
        }
        return result;
    }

    private String getMediaId(String corpId, String channelId, String mediaUrl, QywxMessageTypeEnum qywxMessageType) {
        try {
            LambdaQueryWrapper<ChannelMediaPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ChannelMediaPO::getCorpId, corpId);
            queryWrapper.eq(ChannelMediaPO::getMediaUrl, mediaUrl);
            queryWrapper.ge(ChannelMediaPO::getExpireTime, System.currentTimeMillis());
            ChannelMediaPO channelMediaPO = channelMediaRepository.getOne(queryWrapper);
            if (channelMediaPO != null) {
                return channelMediaPO.getMediaId();
            }
            String mediaId = uploadMedia(qywxMessageType.getCode(), mediaUrl, new File(mediaUrl).getName(), channelId);
            if (StringUtils.isNotBlank(mediaId)) {
                channelMediaPO = new ChannelMediaPO();
                channelMediaPO.setCorpId(corpId);
                channelMediaPO.setMediaId(mediaId);
                channelMediaPO.setMediaType(qywxMessageType.getCode());
                channelMediaPO.setMediaUrl(mediaUrl);
                channelMediaPO.setExpireTime(System.currentTimeMillis() + EXPIRE_TIME);
                channelMediaRepository.save(channelMediaPO);
            }
            return mediaId;
        } catch (Exception e) {
            log.error("上传文件报错", e);
            return null;
        }
    }

    /**
     * 上传文件
     *
     * @return 返回mediaId
     */
    private String uploadMedia(String subType, String url, String fileName, String channelId) {
        String accessToken = tokenUtil.getAccessToken(channelId);
        if (StringUtils.isEmpty(accessToken)) {
            log.info("uploadMedia get token is null");
            return null;
        }
        try {
            File file = new File(fileName);
            try (InputStream inputStream = getFile(url)) {
                if (inputStream != null) {
                    OutputStream os = Files.newOutputStream(file.toPath());
                    IOUtils.copy(inputStream, os);
                }
            } catch (Exception e) {
                log.error("getFile error", e);
            }
            Map<String, File> fileMap = new HashMap<>();
            fileMap.put("media", file);
            String newUrl = weixinUplpadMediaUrl + "?access_token=" + accessToken + "&type=" + subType;
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Content-Type", "application/octet-stream");
            String disposition = "form-data; filename=" + URLEncoder.encode(fileName, "UTF-8");
            headerMap.put("Content-Disposition", disposition);
            String response = HttpUtils.doPostFile(newUrl, fileMap, headerMap);
            log.info("uploadMedia response:{}", response);
            if (StringUtils.isNotEmpty(response)) {
                WeixinUploadRespDTO respDTO = JSONObject.parseObject(response, WeixinUploadRespDTO.class);
                return respDTO.getMediaId();
            }
        } catch (Exception e) {
            log.error("uploadMedia error", e);
        }
        return null;
    }

    private InputStream getFile(String url) {
        Response response = HttpUtils.doGet(url, new HashMap<>());
        if (response == null || response.body() == null) {
            return null;
        }
        return response.body().byteStream();
    }


}
