package com.chinatelecom.gs.engine.config.filter.interceptor;

import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.rpc.UserInfo;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelecom.gs.engine.common.utils.UserInfoUtils;
import com.chinatelecom.gs.engine.config.filter.BaseHandlerInterceptor;
import com.chinatelecom.gs.engine.utils.CheckIsAdminService;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import static com.chinatelecom.gs.engine.common.constants.Constants.EMPTY_USERNAME;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
@Component
@Slf4j
public class RpcContextInterceptor  implements BaseHandlerInterceptor {

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    @Resource
    private CheckIsAdminService checkIsAdminService;

    public boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        RequestInfo requestInfo = new RequestInfo();
        RequestContext.set(requestInfo);
        requestInfo.setTenant(true);
        requestInfo.setRequestSourceType(RequestSourceType.RPC);
        requestInfo.setEntry("rpc");
        if (gsGlobalConfig.getMockRequest().getEnabled()) {
            requestInfo.setUserId(gsGlobalConfig.getMockRequest().getId());
            requestInfo.setUserName(gsGlobalConfig.getMockRequest().getName());
            requestInfo.setTenantId(gsGlobalConfig.getMockRequest().getTenantId());
            requestInfo.setAppCode(gsGlobalConfig.getMockRequest().getAppCode());
            requestInfo.setIsSuperTenant(false);
        } else {
            String tenantId = ((HttpServletRequest) request).getHeader(HeaderKeys.TENANT_ID);
            log.debug(" rpc tenantId:{}", tenantId);
            if (StringUtils.isBlank(tenantId)) {
                return InterceptorUtils.writeError((HttpServletResponse) response, "A0024", "未设置x-tenantid请求头");
            }
            requestInfo.setTenantId(tenantId);
            requestInfo.setIsSuperTenant(superTenant.equals(tenantId));
            String userId = ((HttpServletRequest) request).getHeader(HeaderKeys.USER_ID);
            String anonymousStr = ((HttpServletRequest) request).getHeader(HeaderKeys.ANONYMOUS);
            boolean anonymous = Boolean.valueOf(anonymousStr);
            InterceptorUtils.setCheckRole(((HttpServletRequest) request), requestInfo);
            InterceptorUtils.setAppCode(((HttpServletRequest) request), requestInfo);
            InterceptorUtils.setSourceTypeByRpc(((HttpServletRequest) request), requestInfo);
            if (userId == null) {
                String servletPath = ((HttpServletRequest) request).getServletPath();
                log.warn("rpc调用未设置用户ID,调用路径：{}", servletPath);
                userId = Constants.EMPTY_NODE;
                requestInfo.setUserId(userId);
                requestInfo.setUserName(EMPTY_USERNAME);
            } else {
                requestInfo.setUserId(userId);
                String userName = EMPTY_USERNAME;
                if (!anonymous) {
                    UserInfo userInfo = UserInfoUtils.getUserInfo(tenantId, userId);
                    if (userInfo != null) {
                        userName = userInfo.getName();
                    }
                }

                requestInfo.setUserName(userName);
                String sessionId = ((HttpServletRequest) request).getHeader(HeaderKeys.SESSION_ID);
                requestInfo.setSessionId(sessionId);
                String messageId = ((HttpServletRequest) request).getHeader(HeaderKeys.MESSAGE_ID);
                requestInfo.setMessageId(messageId);
                String logId = ((HttpServletRequest) request).getHeader(HeaderKeys.LOG_ID);
                requestInfo.setLogId(logId);
                requestInfo.setTeam(UserInfoUtils.getUserTeam());

                String admin = ((HttpServletRequest) request).getHeader(HeaderKeys.USER_ADMIN);
                boolean isAdmin = false;
                if (!anonymous) {
                    isAdmin = UserInfoUtils.isAdmin(tenantId, userId);
                }

                PlatformUser platformUser = PlatformUser.builder()
                        .userId(userId)
                        .username(userName)
                        .corpCode(tenantId)
                        .isAdmin(isAdmin)
                        .build();
                SsoUtil.add(platformUser);
                requestInfo.setIsAdmin(checkIsAdminService.checkIsAdmin(requestInfo.getAppSourceType(), requestInfo.getUserId(), requestInfo.getTenantId()));
            }
        }
        return true;
    }

    /**
     * 完成后调用
     *
     * @param request
     * @param response
     * @param ex
     * @throws Exception
     */
    public void afterCompletion(ServletRequest request, ServletResponse response, Exception ex) throws Exception {
        RequestContext.remove();
    }

}
