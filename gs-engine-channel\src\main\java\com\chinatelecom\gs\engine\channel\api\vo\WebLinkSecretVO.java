package com.chinatelecom.gs.engine.channel.api.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Schema(description = "网页链接渠道配置类")
@Data
public class WebLinkSecretVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "共享链接secret")
    @NotBlank(message = "共享链接密码不能为空")
    private String secret;

}