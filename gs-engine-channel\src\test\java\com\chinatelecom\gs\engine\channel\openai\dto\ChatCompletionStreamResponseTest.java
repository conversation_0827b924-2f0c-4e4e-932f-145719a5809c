package com.chinatelecom.gs.engine.channel.openai.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
public class ChatCompletionStreamResponseTest {
    private ChatCompletionStreamResponse chatCompletionStreamResponse;

    @BeforeEach
    public void setUp() {
        chatCompletionStreamResponse = new ChatCompletionStreamResponse();
    }

    @Test
    public void testSettersAndGetters() {
        // Arrange
        String id = "response123";
        long created = 1672531200L; // Example timestamp
        String model = "gpt-3.5";
        String systemFingerprint = "fingerprint123";
        ChatCompletionStreamResponse.ChatCompletionStreamChoice chatCompletionStreamChoice = new ChatCompletionStreamResponse.ChatCompletionStreamChoice();
        ChatCompletionStreamResponse.ChatCompletionStreamDelta chatCompletionStreamDelta = new ChatCompletionStreamResponse.ChatCompletionStreamDelta();
        chatCompletionStreamDelta.setContent("hello");
        chatCompletionStreamDelta.setRole("assistant");
        chatCompletionStreamDelta.setReasoning_content(null);
        chatCompletionStreamChoice.setDelta(chatCompletionStreamDelta);
        chatCompletionStreamChoice.setIndex(0);
        chatCompletionStreamChoice.setFinishReason("stop");

        ChatCompletionStreamResponse.ChatCompletionStreamChoice chatCompletionStreamChoice1 = new ChatCompletionStreamResponse.ChatCompletionStreamChoice();
        ChatCompletionStreamResponse.ChatCompletionStreamDelta chatCompletionStreamDelta1 = new ChatCompletionStreamResponse.ChatCompletionStreamDelta();
        chatCompletionStreamDelta1.setContent("hello");
        chatCompletionStreamDelta1.setRole("assistant");
        chatCompletionStreamDelta1.setReasoning_content(null);
        chatCompletionStreamChoice1.setDelta(chatCompletionStreamDelta1);
        chatCompletionStreamChoice1.setIndex(0);
        chatCompletionStreamChoice1.setFinishReason("stop");

        List<ChatCompletionStreamResponse.ChatCompletionStreamChoice> choices = Arrays.asList(
                chatCompletionStreamChoice,
                chatCompletionStreamChoice1
        );

        // Act
        chatCompletionStreamResponse.setId(id);
        chatCompletionStreamResponse.setCreated(created);
        chatCompletionStreamResponse.setModel(model);
        chatCompletionStreamResponse.setSystemFingerprint(systemFingerprint);
        chatCompletionStreamResponse.setChoices(choices);

        // Assert
        assertThat(chatCompletionStreamResponse.getId()).isEqualTo(id);
        assertThat(chatCompletionStreamResponse.getCreated()).isEqualTo(created);
        assertThat(chatCompletionStreamResponse.getModel()).isEqualTo(model);
        assertThat(chatCompletionStreamResponse.getSystemFingerprint()).isEqualTo(systemFingerprint);
        assertThat(chatCompletionStreamResponse.getChoices()).isEqualTo(choices);
    }
}