package com.chinatelecom.gs.engine.core.corekit.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.common.utils.VariableUtils;
import com.chinatelecom.gs.engine.core.corekit.convert.GlobalVariableConvert;
import com.chinatelecom.gs.engine.core.corekit.domain.po.GlobalVariablePO;
import com.chinatelecom.gs.engine.core.corekit.service.GlobalVariableAppService;
import com.chinatelecom.gs.engine.core.corekit.service.GlobalVariableService;
import com.chinatelecom.gs.engine.core.sdk.request.GlobalVariableAddRequest;
import com.chinatelecom.gs.engine.core.sdk.request.GlobalVariableDelRequest;
import com.chinatelecom.gs.engine.core.sdk.request.GlobalVariableEditRequest;
import com.chinatelecom.gs.engine.core.sdk.request.GlobalVariableQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.GlobalVariableDataDetailVO;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/02/06
 */
@Slf4j
@Service
public class GlobalVariableAppServiceImpl implements GlobalVariableAppService {

    private static final String SYS_VAR_PRE = "sys_";

    @Resource
    private GlobalVariableService globalVariableRepository;

    @Override
    public PageImpl<GlobalVariableDataDetailVO> page(GlobalVariableQueryRequest queryRequest) {
        //1.校验操作权限
        //2.入参校验
        Page<GlobalVariablePO> page = globalVariableRepository.page(queryRequest);
        List<GlobalVariableDataDetailVO> voList = GlobalVariableConvert.INSTANCE.convert(page.getRecords());
        return new PageImpl<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), voList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> add(GlobalVariableAddRequest addRequest) {
        //1.校验操作权限
        //2.入参校验
        Result<Boolean> checkParam = checkVariableName(null, addRequest.getVariableName());
        if (!checkParam.isSuccess()) {
            return checkParam;
        }
        Result<Boolean> checkDefaultValue = checkDefaultValue(addRequest.getDefaultValue(), addRequest.getDataType());
        if (!checkDefaultValue.isSuccess()) {
            return checkDefaultValue;
        }
        GlobalVariablePO globalVariablePO = GlobalVariableConvert.INSTANCE.convert(addRequest);
        String variableCode = IdGenerator.getId(Constants.VAR_ID_PREFIX);
        globalVariablePO.setVariableCode(variableCode);
        boolean operateSuccess = globalVariableRepository.save(globalVariablePO);
        return Result.success(operateSuccess);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> update(GlobalVariableEditRequest editRequest) {
        //1.校验操作权限
        //2.入参校验
        Result<Boolean> checkParam = checkVariableName(editRequest.getVariableCode(), editRequest.getVariableName());
        if (!checkParam.isSuccess()) {
            return checkParam;
        }
        Result<Boolean> checkDefaultValue = checkDefaultValue(editRequest.getDefaultValue(), editRequest.getDataType());
        if (!checkDefaultValue.isSuccess()) {
            return checkDefaultValue;
        }
        boolean operateSuccess = globalVariableRepository.updateVariableDetail(editRequest);
        return Result.success(operateSuccess);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> delete(GlobalVariableDelRequest delRequest) {
        //1.校验操作权限
        //2.系统变量不删除
        boolean operateSuccess = globalVariableRepository.deleteByVariableCodes(delRequest.getVariableCodes());
        return Result.success(operateSuccess);
    }

    @Override
    public GlobalVariableDataDetailVO getDetail(String variableCode) {
        GlobalVariablePO variablePO = globalVariableRepository.getByVariableCode(variableCode);
        return GlobalVariableConvert.INSTANCE.convert(variablePO);
    }

    @Override
    public GlobalVariableDataDetailVO getDetailByName(String variableName) {
        GlobalVariablePO variablePO = globalVariableRepository.getByVariableName(variableName);
        return GlobalVariableConvert.INSTANCE.convert(variablePO);
    }

    private Result<Boolean> checkVariableName(String variableCode, String variableName) {
        if (StringUtils.startsWith(variableName, SYS_VAR_PRE)) {
            throw new BizException("AC045", "不可操作系统变量");
        }
        GlobalVariablePO variablePO = globalVariableRepository.getByVariableName(variableName);
        if (variablePO != null && !StringUtils.equalsIgnoreCase(variablePO.getVariableCode(), variableCode)) {
            throw new BizException("AC046", "变量名重复");
        }
        return Result.success();
    }

    private Result<Boolean> checkDefaultValue(String defaultValue, Integer dataType) {
        try {
            if (StringUtils.isNotEmpty(defaultValue)) {
                VariableUtils.parseValue(defaultValue, dataType);
            }
        } catch (Exception e) {
            log.error("变量默认值不匹配，默认值:{},变量类型:{}", defaultValue, dataType, e);
            throw new BizException("AC047", "默认值格式不匹配");
        }
        return Result.success();
    }
}
