package com.chinatelecom.gs.engine.common.utils;

import com.alibaba.ttl.spi.TtlWrapper;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.executor.PriorityTask;

import java.util.Comparator;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadUtils {

    public static ThreadFactory newGenericThreadFactory(String processName) {
        return newGenericThreadFactory(processName, false);
    }

    public static ThreadFactory newGenericThreadFactory(final String processName, final boolean isDaemon) {
        return new ThreadFactory() {
            private AtomicInteger threadIndex = new AtomicInteger(0);

            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, "%s-%d".formatted(processName, this.threadIndex.incrementAndGet()));
                thread.setDaemon(isDaemon);
                return thread;
            }
        };
    }

    /**
     * 创建一个新的线程池
     * @param config
     * @return
     */
    public static ExecutorService newPool(GsGlobalConfig.ThreadPoolConfig config) {
        BlockingQueue<Runnable> queue;
        if (config.getQueueSize() < 0) {
            queue = new LinkedBlockingQueue<>();
        } else if (config.getQueueSize() == 0) {
            queue = new SynchronousQueue<>();
        } else {
            queue = new ArrayBlockingQueue<>(config.getQueueSize());
        }
        ExecutorService service =  new ThreadPoolExecutor(config.getCoreSize(), config.getMaxSize(), 60, TimeUnit.SECONDS,
                queue, ThreadUtils.newGenericThreadFactory(config.getName()));
        return TtlExecutors.getTtlExecutorService(service);
    }

    /**
     * 创建一个新的具有优先级队列的线程池
     * @param config
     * @return
     */
    public static ExecutorService newPriorityPool(GsGlobalConfig.ThreadPoolConfig config) {
        PriorityBlockingQueue queue = new PriorityBlockingQueue<>(11, new Comparator<Runnable>() {

            @Override
            public int compare(Runnable o1, Runnable o2) {
                if (o1 instanceof TtlWrapper wrapper) {
                    o1 = (Runnable) wrapper.unwrap();
                }
                if (o2 instanceof TtlWrapper wrapper) {
                    o2 = (Runnable) wrapper.unwrap();
                }
                if (o1 instanceof PriorityTask p1 && o2 instanceof PriorityTask p2) {
                    return p1.compareTo(p2);
                }
                throw new IllegalArgumentException("不支持的任务类型");
            }
        });
        ExecutorService service = new ThreadPoolExecutor(config.getCoreSize(), config.getMaxSize(), 60, TimeUnit.SECONDS,
                queue, ThreadUtils.newGenericThreadFactory(config.getName()));
        return TtlExecutors.getTtlExecutorService(service);
    }

}
