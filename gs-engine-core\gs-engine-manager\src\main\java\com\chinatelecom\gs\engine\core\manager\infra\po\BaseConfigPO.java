package com.chinatelecom.gs.engine.core.manager.infra.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.common.infra.base.BaseCodeEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月28日
 */
@Getter
@Setter
@TableName("gs_base_config")
public class BaseConfigPO  extends BaseCodeEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务配置名称
     */
    @TableField("name")
    private String name;

    /**
     * 配置维度
     */
    @TableField("`dimension`")
    private DimensionEnum dimension;
    /**
     * 业务配置场景
     */
    @TableField("config_type")
    private String configType;

    /**
     * 业务配置场景说明
     */
    @TableField("description")
    private String description;

    /**
     * 业务场景配置唯一标识
     */
    @TableField("business_no")
    private String businessNo;

    /**
     * 配置json格式value值
     */
    @TableField("`config_data`")
    private String configData;

    /**
     * 配置json的class对象
     */
    @TableField("obj_class")
    private String objClass;


}
