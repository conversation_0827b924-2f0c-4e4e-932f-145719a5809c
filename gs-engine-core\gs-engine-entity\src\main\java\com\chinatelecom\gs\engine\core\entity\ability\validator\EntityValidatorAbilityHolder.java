package com.chinatelecom.gs.engine.core.entity.ability.validator;

import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @USER: pengmc1
 * @DATE: 2025/1/21 8:45
 */

@Component
public class EntityValidatorAbilityHolder {

    @Resource
    private List<EntityValidatorAbilityService> entityValidatorAbilityServices;

    @Resource
    private Map<String, EntityValidatorAbilityService> entityValidatorAbilityServiceMap;

    @PostConstruct
    private void init(){
        if(!CollectionUtils.isEmpty(entityValidatorAbilityServices)){
            for(EntityValidatorAbilityService entityValidatorAbilityService : entityValidatorAbilityServices){
                entityValidatorAbilityServiceMap.put(entityValidatorAbilityService.validatorCode(), entityValidatorAbilityService);
            }
        }
    }

    /**
     * 根据指令编码获取指令服务
     * @param validatorCode
     * @return
     */
    public EntityValidatorAbilityService getValidatorAbilityService(String validatorCode){
        if(!entityValidatorAbilityServiceMap.containsKey(validatorCode)){
            throw new BizException("未找到实体校验处理器" + validatorCode);
        }
        return entityValidatorAbilityServiceMap.get(validatorCode);
    }
}
