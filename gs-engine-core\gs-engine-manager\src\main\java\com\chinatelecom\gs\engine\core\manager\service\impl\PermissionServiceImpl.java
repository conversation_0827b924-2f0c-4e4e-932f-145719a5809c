package com.chinatelecom.gs.engine.core.manager.service.impl;

import com.chinatelecom.cloud.platform.client.rpc.Menu;
import com.chinatelecom.cloud.platform.client.rpc.Permission;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.core.manager.convert.ConfigConvert;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.manager.service.PermissionService;
import com.chinatelecom.gs.engine.core.manager.vo.config.PublishConfig;
import com.chinatelecom.gs.engine.core.manager.vo.config.SpeechConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月28日
 */
@Service
@Validated
@Slf4j
public class PermissionServiceImpl implements PermissionService {

    @Resource
    private BaseConfigAppService configService;
    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Override
    public List<Permission> filterResource(List<Permission> data) {
        Set<String> enabledModes = getFilteredCodes(RequestContext.getTenantId());
        return applyResourceFilters(data, enabledModes);
    }

    @Override
    public List<Menu> filterMenu(List<Menu> data) {
        Set<String> enabledModes = getFilteredCodes(RequestContext.getTenantId());
        return applyMenuFilters(data, enabledModes);
    }

    private Set<String> getFilteredCodes(String tenantId) {
        //发布配置
        PublishConfig publishConfig = configService.getConfigOrDefault(ConfigConvert.PUBLISH_CONFIG, tenantId);
        Set<String> enabledModes = new HashSet<>();
        if (!publishConfig.getPublishSwitch()) {
            enabledModes.add("publishFilter");
        }
        if (!publishConfig.getAuditSwitch()) {
            enabledModes.add("auditFilter");
        }
        //语音配置
        SpeechConfig speechConfig = configService.getConfigOrDefault(ConfigConvert.SPEECH_CONFIG, tenantId);
        if(!speechConfig.getAsrSwitch()){
            enabledModes.add("asrFilter");
        }
        if(!speechConfig.getTtsSwitch()){
            enabledModes.add("ttsFilter");
        }
        return enabledModes;
    }

    public List<Permission> applyResourceFilters(List<Permission> permissions, Set<String> enabledModes) {
        if (CollectionUtils.isEmpty(enabledModes)){
            return permissions;
        }
        Set<String> allFilteredCodes = getAllFilteredCodes(enabledModes);
        // 应用过滤
        return permissions.stream()
                .filter(permission -> !allFilteredCodes.contains(permission.getPermissionCode()))
                .collect(Collectors.toList());
    }

    public List<Menu> applyMenuFilters(List<Menu> permissions, Set<String> enabledModes) {
        if (CollectionUtils.isEmpty(enabledModes)){
            return permissions;
        }
        Set<String> allFilteredCodes = getAllFilteredCodes(enabledModes);
        return filterMenu(permissions, allFilteredCodes);
    }

    public List<Menu> filterMenu(List<Menu> menus, Set<String> allFilteredCodes) {
        if (CollectionUtils.isEmpty(allFilteredCodes) || CollectionUtils.isEmpty(menus)) {
            return menus;
        }

        List<Menu> filteredMenus = menus.stream()
                .filter(menu -> !allFilteredCodes.contains(menu.getUrlAddress()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filteredMenus)) {
            for (Menu menu : filteredMenus) {
                if (CollectionUtils.isNotEmpty(menu.getChildren())) {
                    List<Menu> menusChildren = filterMenu(menu.getChildren(), allFilteredCodes);
                    menu.setChildren(menusChildren);
                }
            }
        }
        return filteredMenus;
    }

    private Set<String> getAllFilteredCodes(Set<String> enabledModes) {
        // 获取所有启用的模式的过滤代码
        Set<String> allFilteredCodes = enabledModes.stream()
                .map(mode -> gsGlobalConfig.getFilterModes().get(mode))
                .filter(Objects::nonNull)
                .flatMap(config -> config.getFilteredCodes().stream())
                .collect(Collectors.toSet());
        return allFilteredCodes;
    }
}

