package com.chinatelecom.gs.engine.common.mq;

import com.alibaba.ttl.threadpool.TtlExecutors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Component
public class DelayExecutor {

    @Resource
    @Qualifier("delayMqTaskThreadPool")
    private ExecutorService delayMqTaskThreadPool;

    ExecutorService singleThreadExecutor = TtlExecutors.getTtlExecutorService(Executors.newSingleThreadExecutor());

    private final DelayQueue<DelayTask> delayQueue = new DelayQueue<>();

    @PostConstruct
    public void init() {
        singleThreadExecutor.execute(() -> {
            while (true) {
                try {
                    // take会阻塞，直到有任务到期
                    DelayTask task = delayQueue.take();
                    delayMqTaskThreadPool.execute(task);
                } catch (InterruptedException e) {
                    log.error("延迟任务队列提交任务异常", e);
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
    }

    public void execute(DelayTask task) {
        delayQueue.put(task);
    }

}
