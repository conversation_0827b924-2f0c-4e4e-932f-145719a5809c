package com.chinatelecom.gs.engine.core.entity.service;

import com.chinatelecom.gs.engine.common.infra.base.BaseExtendService;
import com.chinatelecom.gs.engine.core.entity.domain.po.EntityDataPO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;

import java.util.List;

public interface EntityDataService extends BaseExtendService<EntityDataPO> {
    Boolean saveEntityData(EntityDetailVO query);

    Boolean delete(List<String> entityCodes);

    List<EntityDataPO> getEntityDataList(String entityCode, String tenantId);
}
