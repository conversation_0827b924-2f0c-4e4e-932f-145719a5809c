package com.chinatelecom.gs.engine.common.constants;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月04日
 */
public interface Constants {

    String EMPTY_NODE = "_empty_";

    Long EMPTY_NODE_ID = 0L;

    String COMMON_APP_NAME = "公共空间";
    String TEAM_APP_NAME = "的团队空间";
    String PRI_APP_NAME = "的空间";

    String DEFAULT = "DEFAULT";

    /**
     * prompt参数
     */
    //内容
    String PROMPT_PARAM_CONTENT = "content";
    // 大纲
    String PROMPT_PARAM_OUTLINE = "outline";
    //关键字
    String PROMPT_PARAM_KEYWORDS = "keywords";
    // 附加提示词
    String PROMPT_PARAM_ADDITIONAL_KEYWORDS = "additionalKeywords";
    // 参考内容
    String PROMPT_PARAM_EXAMPLE = "example";
    // 大纲标题
    String PROMPT_PARAM_TITLE = "title";
    //  摘要
    String PROMPT_PARAM_INTRODUCTION = "introduction";
    // 用户原始主题
    String PROMPT_PARAM_THEME_TEXT = "themeText";
    // 上次生成的文章内容
    String PROMPT_PARAM_LAST_CONTENT  = "lastContent";
    // 文章总结内容
    String PROMPT_PARAM_SUMMARY  = "summary";




    String WEB_PREFIX = "/web";

    String PLUGIN_API = "/plugin";

    String RPC_PREFIX = "/rpc";

    String API_PREFIX = "/openapi";

    String BASE_PREFIX = "/base";


    String EMPTY_USERNAME = "未知用户";

    String EXTRACT_PREFIX = "EXTRACT";
    /**
     * 肯否实体编码
     */
    String KENFOU_ENTITY_CODE = "kenfou";


    String FILE_TYPE = "workflow";


    String VAR_ID_PREFIX = "var";

    int CANVAS_MAX_LENGTH = 60000;

    /**
     * 控制多模态文件上传的资源编码
     */
    String MULTIMODAL_UPLOAD = "manage_multimodal_uploadfile";

    /**
     * 控制多模态问答的资源编码
     */
    String MULTIMODAL_RECALL = "manage_multimodal_recall";

}
