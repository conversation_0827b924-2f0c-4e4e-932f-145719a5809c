package com.chinatelecom.gs.engine.common.log.ttl;

import com.alibaba.ttl.TtlCallable;
import com.chinatelecom.gs.engine.common.log.utils.MDCTraceUtils;
import lombok.AllArgsConstructor;

import java.util.concurrent.Callable;

@AllArgsConstructor
public class TraceTtlCallable<V> implements Callable<V> {

    private final TtlCallable<V> delegate;

    @Override
    public V call() throws Exception {
        return delegate.call();
    }

    public static <T> TraceTtlCallable<T> get(Callable<T> callable) {
        return get(callable, false);
    }

    public static <T> TraceTtlCallable<T> get(Callable<T> callable, boolean releaseTtlValueReferenceAfterCall) {
        if (callable == null) {
            return null;
        } else if (callable instanceof TraceTtlCallable ttlCallable) {
            return ttlCallable;
        } else if (callable instanceof TtlCallable) {
            callable = ((TtlCallable<T>) callable).getCallable();
        }
        Callable<T> finalCallable = callable;
        return new TraceTtlCallable(TtlCallable.get(() -> {
            MDCTraceUtils.putTrace(MDCTraceUtils.getTraceId(), MDCTraceUtils.getSpanId());
            return finalCallable.call();
        }, releaseTtlValueReferenceAfterCall));
    }
}
