package com.chinatelecom.gs.engine.core.corekit.domain.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinatelecom.gs.engine.common.infra.base.BaseEntity;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2025/02/06
 */
@Data
@TableName("gs_variable")
public class GlobalVariablePO extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 794929822735841791L;

    @TableField("var_code")
    private String variableCode;

    @TableField("var_name")
    private String variableName;

    @TableField("description")
    private String variableDesc;

    @TableField("var_type")
    private String variableType;

    @TableField("data_type")
    private int dataType;

    @TableField("default_value")
    private String defaultValue;

    @TableField("is_system")
    private Integer isSystem = 0;
}
