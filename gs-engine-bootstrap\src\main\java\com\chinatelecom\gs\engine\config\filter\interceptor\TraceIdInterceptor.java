package com.chinatelecom.gs.engine.config.filter.interceptor;

import io.opentelemetry.api.trace.Span;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * trance id 拦截器
 */
@Component
public class TraceIdInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String traceId = Span.current().getSpanContext().getTraceId();
        response.setHeader("X-Trace-Id", traceId);
        return true;
    }
}