package com.chinatelecom.gs.engine.common.cache.customized;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.utils.RedisPrefixUtils;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCacheManager;

import jakarta.annotation.Resource;

/**
 * 自定义的CaffeineCacheManager,统一添加租户隔离
 *
 * <AUTHOR>
 * @date 2023-05-26 11:59
 */
public class CustomizedCaffeineCacheManager extends CaffeineCacheManager {
    @Resource
    private GsGlobalConfig gsGlobalConfig;

    /**
     * 统一设置租户隔离
     * @param name the cache identifier (must not be {@code null})
     * @return
     */
    @Override
    public Cache getCache(String name) {
        String nameKey = RedisPrefixUtils.buildByPrefix(name);
        return super.getCache(nameKey);
    }

    protected Cache adaptCaffeineCache(String name, com.github.benmanes.caffeine.cache.Cache<Object, Object> cache) {
        return new CustomizedCaffeineCache(name, cache, isAllowNullValues());
    }

}
