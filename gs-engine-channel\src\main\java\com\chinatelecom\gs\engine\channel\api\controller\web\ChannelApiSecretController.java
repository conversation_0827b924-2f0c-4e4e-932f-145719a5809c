package com.chinatelecom.gs.engine.channel.api.controller.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.channel.api.param.ChannelApiSecretAddParam;
import com.chinatelecom.gs.engine.channel.api.param.ChannelApiSecretBaseUpdateParam;
import com.chinatelecom.gs.engine.channel.api.param.ChannelApiSecretUpdateParam;
import com.chinatelecom.gs.engine.channel.manage.impl.ChannelSecretManagerServiceImpl;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.auth.PermissionTypeEnum;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;

/**
 * @Description: API密钥管理
 * @Author: qingx
 * @Time: 2023/08/29
 * @Version: 1.0
 */
@Slf4j
@Tag(name = "API密钥管理", description = "ChannelApiSecret")
@PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
@RestController
@RequestMapping(Constants.ROBOT_PREFIX + Constants.WEB_PREFIX + "/channel/secret")
public class ChannelApiSecretController {

    @Resource
    private ChannelSecretManagerServiceImpl channelSecretManagerService;

    @GetMapping("/list")
    @PlatformRestApi(name = "查询列表", groupName = "API密钥管理")
    @AuditLog(businessType = "API密钥管理", operType = "查询列表", operDesc = "查询列表", objId="#appId")
    public Result<Page<ChannelApiSecretDTO>> list(String appId, Integer pageNo, Integer pageSize) {
        return Result.success(channelSecretManagerService.list(appId, ApiSecretType.API, pageNo, pageSize));
    }

    @PostMapping("/create")
    @PlatformRestApi(name = "创建密钥", groupName = "API密钥管理")
    @AuditLog(businessType = "API密钥管理", operType = "创建密钥", operDesc = "创建密钥", objId="#request.appId")
    public Result<Boolean> create(@Validated @RequestBody ChannelApiSecretAddParam request) {
        return Result.success(channelSecretManagerService.create(request.getAppId(), null, request.getSecretName(), ApiSecretType.API));
    }

    @PostMapping("/modify")
    @PlatformRestApi(name = "更新密钥", groupName = "API密钥管理")
    @AuditLog(businessType = "API密钥管理", operType = "更新密钥", operDesc = "更新密钥", objId="#request.appId")
    public Result<Boolean> modify(@Validated @RequestBody ChannelApiSecretUpdateParam request) {
        return Result.success(channelSecretManagerService.modify(request.getAppId(), request.getSecretId(), request.getSecretName()));
    }

    @PostMapping("/remove")
    @PlatformRestApi(name = "删除密钥", groupName = "API密钥管理")
    @AuditLog(businessType = "API密钥管理", operType = "删除密钥", operDesc = "删除密钥", objId="#param.appId")
    public Result<Boolean> remove(@RequestBody ChannelApiSecretBaseUpdateParam param) {
        return Result.success(channelSecretManagerService.remove(param.getAppId(), param.getSecretId()));
    }

    @PostMapping("/regenerate")
    @PlatformRestApi(name = "重新生成密钥", groupName = "API密钥管理")
    @AuditLog(businessType = "API密钥管理", operType = "重新生成密钥", operDesc = "重新生成密钥", objId="#param.appId")
    public Result<String> regenerate(@RequestBody ChannelApiSecretBaseUpdateParam param) {
        return Result.success(channelSecretManagerService.regenerate(param.getAppId(), param.getSecretId()));
    }

    /**
     * API使用指南下载接口
     */
    @PlatformRestApi(name = "API使用指南下载接口", groupName = "API密钥管理")
    @GetMapping("/guidelines")
    @AuditLog(businessType = "API密钥管理", operType = "API使用指南下载", operDesc = "API使用指南下载", objId="null")
    public void usageGuidelines(HttpServletResponse response) {
        try (FileInputStream is = new FileInputStream("/usr/src/app/telecom/largeModelAPIAccessManual.pdf")) {
            response.setContentType("application/pdf");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("大模型API接入手册", "utf-8");
            response.setHeader("Content-disposition", "attachment;filename=%s.pdf".formatted(fileName));
            OutputStream os = new BufferedOutputStream(response.getOutputStream());
            response.setHeader("Content-Transfer-Encoding", "binary");
            byte[] bytes;
            while (is != null && is.read(bytes = new byte[1024]) != -1) {
                os.write(bytes);
            }
            os.flush();
            os.close();
        } catch (IOException ioe) {
            log.error("下载API接入手册异常", ioe);
        }
    }
//
//    private static void initTestUser() {
//        UserInfo userInfo = RequestContext.get();
//        if (userInfo == null){
//            SsoUserInfo ssoUserInfo = new SsoUserInfo();
//            ssoUserInfo.setTenantId("0");
//            ssoUserInfo.setUserId("system");
//            ssoUserInfo.setUserName("system");
//            UserInfoContext.setUserInfo(ssoUserInfo);
//        }
//    }
}
