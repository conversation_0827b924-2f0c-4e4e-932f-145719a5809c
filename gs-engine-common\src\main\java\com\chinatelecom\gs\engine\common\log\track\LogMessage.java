package com.chinatelecom.gs.engine.common.log.track;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinatelecom.gs.engine.robot.sdk.constant.CommonConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @USER: pengmc1
 * @DATE: 2025/5/6 17:39
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(indexName = "#{@environment.getProperty('gs.system.esPrefix') + @environment.getProperty('gs.system.env')}" + CommonConstant.ES_LOG_MESSAGE_INDEX_NAME)
public class LogMessage implements Serializable {
    /**
     * 父级日志ID
     */
    @Field(type = FieldType.Keyword)
    @TableField("p_log_id")
    protected String pLogId;
    /**
     * 日志ID
     */
    @Field(type = FieldType.Keyword)
    protected String logId;
    /**
     * 会话ID
     */
    @Field(type = FieldType.Keyword)
    protected String sessionId;
    /**
     * 消息ID
     */
    @Field(type = FieldType.Keyword)
    protected String messageId;
    /**
     * 租户ID
     */
    @Field(type = FieldType.Keyword)
    protected String tenantId;
    /**
     * 天眼TraceId
     */
    @Field(type = FieldType.Keyword)
    protected String traceId;
    /**
     * 用户ID
     */
    @Field(type = FieldType.Keyword)
    protected String userId;
    /**
     * 机器人编码
     */
    @Field(type = FieldType.Keyword)
    protected String agentCode;
    /**
     * 工作流ID
     */
    @Field(type = FieldType.Keyword)
    protected String workflowId;
    /**
     * 节点ID
     */
    @Field(type = FieldType.Keyword)
    protected String nodeId;
    /**
     * 日志名称
     */
    @Field(type = FieldType.Keyword)
    protected String name;
    /**
     * 日志类型
     * 详见 com.chinatelecom.gs.engine.common.enums.LogTypeEnum
     */
    @Field(type = FieldType.Keyword)
    protected String logType;
    /**
     * 配置信息
     */
    @Field(type = FieldType.Text)
    protected String config;
    /**
     * 请求地址
     */
    @Field(type = FieldType.Keyword)
    protected String url;
    /**
     * 请求参数JSON
     */
    @Field(type = FieldType.Text)
    protected String inputData;
    /**
     * 返回结果JSON
     */
    @Field(type = FieldType.Text)
    protected String outputData;
    /**
     * 扩展数据
     */
    @Field(type = FieldType.Text)
    protected String extraData;
    /**
     * 日志级别
     * 详见 com.chinatelecom.gs.engine.common.enums.LogLevelEnum
     */
    @Field(type = FieldType.Keyword)
    protected String logLevel;
    /**
     * 状态
     * 详见 com.chinatelecom.gs.engine.common.enums.LogStatusEnum
     */
    @Field(type = FieldType.Keyword)
    protected String status;
    /**
     * 消息
     */
    @Field(type = FieldType.Keyword)
    protected String message;

    /**
     * 环境
     */
    @Field(type = FieldType.Keyword)
    protected String env;
    /**
     * 输入token数
     */
    @Field(type = FieldType.Keyword)
    protected Integer promptTokens;
    /**
     * 输出token数
     */
    @Field(type = FieldType.Keyword)
    protected Integer completionTokens;
    /**
     * 开始时间
     */
    @Field(type = FieldType.Date,  pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    protected LocalDateTime startTime;
    /**
     * 结束时间
     */
    @Field(type = FieldType.Date,  pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    protected LocalDateTime endTime;

    /**
     * 生成Log时间
     */
    @Field(type = FieldType.Date,  pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    protected LocalDateTime sendTime;
    /**
     * 入库时间
     */
    @Field(type = FieldType.Date,  pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    protected LocalDateTime addTime;
    /**
     * 首token时间
     */
    @Field(type = FieldType.Date,  pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    protected LocalDateTime firstTokenTime;
}
