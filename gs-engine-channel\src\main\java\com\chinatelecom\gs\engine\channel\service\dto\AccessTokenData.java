package com.chinatelecom.gs.engine.channel.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * DTO for storing data associated with an access token in Redis.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccessTokenData implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L; // Important for Serializable

    private String accessToken; // 新增字段
    private String secretId;
    private String channelId;
    private String secretName;
    private String tenantId;
    private String appId;
    private String appCode;
} 