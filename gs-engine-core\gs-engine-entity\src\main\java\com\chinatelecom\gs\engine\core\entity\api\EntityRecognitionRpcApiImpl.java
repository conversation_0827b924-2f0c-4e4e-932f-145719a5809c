package com.chinatelecom.gs.engine.core.entity.api;

import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.core.entity.service.EntityRecognitionService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionResponse;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * @USER: pengmc1
 * @DATE: 2025/8/5 10:32
 */

@Slf4j
@RestController
@RequestMapping(Apis.BASE_PREFIX + Apis.RPC_PREFIX + "/entityRecognition")
public class EntityRecognitionRpcApiImpl {

    @Resource
    private EntityRecognitionService entityRecognitionService;

    /**
     * 实体识别
     * @param request
     * @return
     */
    @PostMapping("/predict")
    Result<EntityRecognitionResponse> predict(@Valid @RequestBody EntityRecognitionRequest request){
        return Result.success(entityRecognitionService.predict(request));
    }

}
