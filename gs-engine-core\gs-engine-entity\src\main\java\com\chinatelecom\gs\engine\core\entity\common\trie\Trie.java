package com.chinatelecom.gs.engine.core.entity.common.trie;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;

/**
 * 前缀树
 *
 * @USER: pengmc1
 * @DATE: 2023/5/9 18:12
 */

@Slf4j
@Data
public class Trie<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 8376949839145745746L;

    private TrieNode<T> root; //根节点

    /**
     * 添加节点数据
     * @param word
     */
    public void addNodeData(String word, String dataKey, T data) {
        if (StringUtils.isEmpty(word)) {
            return;
        }
        //拆分为字符列表
        List<Character> chars = cut(word);
        TrieNode<T> start = root;
        //遍历字符列表
        for (Character c : chars) {
            //如果已经存该字符，则继续匹配
            if (start.getNext().containsKey(c)) {
                start = start.getNext().get(c);
            } else {
                //如果不存在，则构造Node节点，并写入到Tree中
                TrieNode<T> tmp = new TrieNode<>(c);
                start.getNext().put(c, tmp);
                start = tmp;
            }
        }
        start.setWord(word); //设置文本内容
        start.setLevel(chars.size()); //设置深度
        if(null == start.getDataMap()){
            start.setDataMap(Maps.newHashMap());
        }
        start.getDataMap().put(dataKey,data); //在节点中存放数据
    }

    /**
     * 匹配单词位置
     * @param content
     * @return
     */
    public List<WordPosition<T>> matchWordPositions(String content){
        List<WordPosition<T>> resultList = Lists.newArrayList();
        TrieNode<T> nextNode = null;
        List<Character> chars = cut(content);
        List<TrieNode<T>> nodes = Lists.newArrayList();
        int index = 0;
        for (Character c : chars) {
            //从已匹配的节点中开始匹配
            List<TrieNode<T>> tmpnodes = Lists.newArrayList();
            for (TrieNode<T> node : nodes) {
                //匹配成功
                TrieNode<T> nextStartNode = null;
                if(node.getNext().containsKey(c)) {
                    nextStartNode = node.getNext().get(c);
                    //看是否有数据
                    if( null != nextStartNode.getWord() && null != nextStartNode.getDataMap() ){
                        String word = nextStartNode.getWord();
                        Map<String, T> dataMap = nextStartNode.getDataMap();
                        for(T t : dataMap.values()){
                            WordPosition<T> wordPosition = new WordPosition<>();
                            T nt = (T)JSON.parseObject(JSON.toJSONString(t), t.getClass());
                            wordPosition.setData(nt);
                            wordPosition.setWord(word);
                            wordPosition.setStart(index+1-nextStartNode.getLevel());
                            wordPosition.setEnd(index);
                            resultList.add(wordPosition);
                        }
                    }
                }
                nextNode = nextStartNode;
                if (Objects.nonNull(nextNode)) {
                    tmpnodes.add(nextNode);
                }
            }
            nodes = tmpnodes;
            //从前缀树root开始匹配
            TrieNode<T> nextStartNode = null;
            if(root.getNext().containsKey(c)) {
                nextStartNode = root.getNext().get(c);
                if( null != nextStartNode.getWord() && null != nextStartNode.getDataMap() ){
                    String word = nextStartNode.getWord();
                    Map<String, T> dataMap = nextStartNode.getDataMap();
                    for(T t : dataMap.values()){
                        WordPosition<T> wordPosition = new WordPosition<>();
                        T nt = (T)JSON.parseObject(JSON.toJSONString(t), t.getClass());
                        wordPosition.setData(nt);
                        wordPosition.setWord(word);
                        wordPosition.setStart(index+1-nextStartNode.getLevel());
                        wordPosition.setEnd(index+1);
                        resultList.add(wordPosition);
                    }
                }
                nextNode = nextStartNode;
                if (Objects.nonNull(nextNode)) {
                    nodes.add(nextNode);
                }
            }
            index++;
        }
        return resultList;
    }
    /**
     * 分词
     * @param word
     * @return
     */
    private List<Character> cut(String word) {
        if (StringUtils.isEmpty(word)) {
            return Collections.emptyList();
        }
        char[] cs = word.toUpperCase().toCharArray();
        List<Character> result = new ArrayList<>();
        for (char c : cs) {
            result.add(c);
        }
        return result;
    }


    public Trie() {
        this.root = new TrieNode<>();
    }
}
