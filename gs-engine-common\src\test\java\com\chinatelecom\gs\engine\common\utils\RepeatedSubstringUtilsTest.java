package com.chinatelecom.gs.engine.common.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RepeatedSubstringUtils单元测试类
 * <p>
 * 测试目标：验证detectRepeater方法在不同输入情况下的行为是否符合预期
 */
public class RepeatedSubstringUtilsTest {

    /**
     * 测试用例TC001：空字符串输入
     * 预期结果：返回false
     */
    @Test
    public void testdetectRepeater_EmptyInput() {
        assertFalse(RepeatedSubstringUtils.detectRepeater("", 2));
    }

    /**
     * 测试用例TC002：无重复子串的情况
     * 预期结果：返回false
     */
    @Test
    public void testdetectRepeater_NoRepeatedSubstring() {
        assertFalse(RepeatedSubstringUtils.detectRepeater("abcdefg", 2));
    }

    /**
     * 测试用例TC003：存在重复子串且次数大于n
     * 预期结果：返回true
     */
    @Test
    public void testdetectRepeater_RepeatedSubstringGreaterThanN() {
        assertTrue(RepeatedSubstringUtils.detectRepeater("abcabcabc", 2));
    }

    /**
     * 测试用例TC004：存在重复子串但次数等于n
     * 预期结果：返回false
     */
    @Test
    public void testdetectRepeater_RepeatedSubstringEqualTon() {
        assertTrue(RepeatedSubstringUtils.detectRepeater("abcabcabc", 2));
    }


    @Test
    public void testdetectRepeater_MinimumLengthRepeat() {
        assertFalse(RepeatedSubstringUtils.detectRepeater("aaa", 2));
    }

    /**
     * 测试用例TC006：包含中文字符的重复
     * 预期结果：返回true
     */
    @Test
    public void testdetectRepeater_ChineseCharacters() {
        assertTrue(RepeatedSubstringUtils.detectRepeater("你好啊你好啊你好啊", 2));
    }


    /**
     * 测试用例TC008：重复子串出现在字符串末尾
     * 预期结果：返回true
     */
    @Test
    public void testdetectRepeater_RepeatAtEnd() {
        assertTrue(RepeatedSubstringUtils.detectRepeater("xyzabcabcabc", 2));
    }

    /**
     * 测试用例TC009：重复子串长度为输入字符串长度的一半
     * 预期结果：返回true
     */
    @Test
    public void testdetectRepeater_SubstringHalfLength() {
        assertTrue(RepeatedSubstringUtils.detectRepeater("abcdabcdabcd", 2));
    }

    /**
     * 测试用例TC010：重复次数正好等于n+1的情况
     * 预期结果：返回true
     */
    @Test
    public void testdetectRepeater_ExactCountPlusOne() {
        assertTrue(RepeatedSubstringUtils.detectRepeater("xyzxyzxyz", 2)); // 3次重复，n=2
    }

    @Test
    @DisplayName("测试基本重复检测")
    public void testBasicRepeaterDetection() {
        // 正常重复内容
        String content1 = "这是一个测试这是一个测试这是一个测试";
        assertTrue(RepeatedSubstringUtils.detectRepeater(content1, 2), "应该检测到重复");
        assertFalse(RepeatedSubstringUtils.detectRepeater(content1, 3), "重复3次，阈值为3不应该检测到");

        String content2 = "Hello World Hello World Hello World";
        assertTrue(RepeatedSubstringUtils.detectRepeater(content2, 2), "应该检测到重复");

        String content3 = "123456123456123456";
        assertTrue(RepeatedSubstringUtils.detectRepeater(content3, 2), "应该检测到重复");
    }

    @Test
    @DisplayName("测试边界情况")
    public void testEdgeCases() {
        // 空值和边界情况
        assertFalse(RepeatedSubstringUtils.detectRepeater(null, 2), "null应该返回false");
        assertFalse(RepeatedSubstringUtils.detectRepeater("", 2), "空字符串应该返回false");
        assertFalse(RepeatedSubstringUtils.detectRepeater("a", 2), "单字符应该返回false");
        assertFalse(RepeatedSubstringUtils.detectRepeater("ab", 2), "两个字符应该返回false");
        assertFalse(RepeatedSubstringUtils.detectRepeater("abc", 1), "n=1应该返回false");
    }

    @Test
    @DisplayName("测试表格分隔符不被误判")
    public void testTableSeparators() {
        // 表格分隔符应该不被识别为重复
        String tableSeparator1 = "|-------|----------|-------|";
        String tableSeparator2 = "| --- | --- | --- |";
        String tableSeparator3 = "|=====|=====|=====|";
        String tableSeparator4 = "|+++++|+++++|+++++|";

        assertFalse(RepeatedSubstringUtils.detectRepeater(tableSeparator1, 2),
                "表格分隔符不应该被识别为重复");
        assertFalse(RepeatedSubstringUtils.detectRepeater(tableSeparator2, 2),
                "表格分隔符不应该被识别为重复");
        assertFalse(RepeatedSubstringUtils.detectRepeater(tableSeparator3, 2),
                "表格分隔符不应该被识别为重复");
        assertFalse(RepeatedSubstringUtils.detectRepeater(tableSeparator4, 2),
                "表格分隔符不应该被识别为重复");
    }

    @Test
    @DisplayName("测试纯分隔符不被误判")
    public void testPureSeparators() {
        // 纯分隔符应该不被识别为重复
        String dashes = "----------";
        String equals = "==========";
        String stars = "**********";
        String underscores = "__________";
        String hashes = "##########";
        String dots = "..........";

        assertFalse(RepeatedSubstringUtils.detectRepeater(dashes, 2),
                "连续短横线不应该被识别为重复");
        assertFalse(RepeatedSubstringUtils.detectRepeater(equals, 2),
                "连续等号不应该被识别为重复");
        assertFalse(RepeatedSubstringUtils.detectRepeater(stars, 2),
                "连续星号不应该被识别为重复");
        assertFalse(RepeatedSubstringUtils.detectRepeater(underscores, 2),
                "连续下划线不应该被识别为重复");
        assertFalse(RepeatedSubstringUtils.detectRepeater(hashes, 2),
                "连续井号不应该被识别为重复");
        assertFalse(RepeatedSubstringUtils.detectRepeater(dots, 2),
                "连续点号不应该被识别为重复");
    }

    @Test
    @DisplayName("测试包含表格的复合文本")
    public void testComplexTableText() {
        // 包含表格的文本，但表格分隔符应该被过滤
        String tableText = """
                这是一个表格示例：
                | 列1 | 列2 | 列3 |
                |-----|-----|-----|
                | 值1 | 值2 | 值3 |
                | 值4 | 值5 | 值6 |
                表格结束。""";

        assertFalse(RepeatedSubstringUtils.detectRepeater(tableText, 2),
                "包含表格的文本不应该被误判");

        // 但如果有真正的重复内容，应该能检测到
        String tableWithRepeat = """
                重复内容重复内容重复内容
                | 列1 | 列2 |
                |-----|-----|
                | 值1 | 值2 |""";

        assertTrue(RepeatedSubstringUtils.detectRepeater(tableWithRepeat, 2),
                "表格中的真正重复内容应该被检测到");

        String tableWithRepeat2 = """
                这是一个表格内容给，表格有重复
                | 列1 | 列1 |
                | 列1 | 列1 |
                | 列1 | 列1 |
                |-----|-----|
                | 值1 | 值2 |""";

        assertTrue(RepeatedSubstringUtils.detectRepeater(tableWithRepeat2, 2),
                "表格内容有重复应该被检测到");
    }

    @Test
    @DisplayName("测试重复次数阈值")
    public void testRepeatThreshold() {
        String text = "abcabcabc";  // "abc"重复3次

        assertTrue(RepeatedSubstringUtils.detectRepeater(text, 2),
                "重复3次，阈值为2，应该检测到");
        assertFalse(RepeatedSubstringUtils.detectRepeater(text, 3),
                "重复3次，阈值为3，不应该检测到");
        assertFalse(RepeatedSubstringUtils.detectRepeater(text, 4),
                "重复3次，阈值为4，不应该检测到");
    }

    @Test
    @DisplayName("测试单字符重复")
    public void testSingleCharacterRepeat() {
        // 单字符重复不应该被检测到
        String letterRepeat = "aaaaaaa";
        String numberRepeat = "1111111";
        String spaceRepeat = "       ";

        assertFalse(RepeatedSubstringUtils.detectRepeater(letterRepeat, 2),
                "单字符重复不应该被检测到");
        assertFalse(RepeatedSubstringUtils.detectRepeater(numberRepeat, 2),
                "单字符重复不应该被检测到");
        assertFalse(RepeatedSubstringUtils.detectRepeater(spaceRepeat, 2),
                "空格重复不应该被检测到");
    }

    @Test
    @DisplayName("测试最大重复次数计算")
    public void testMaxRepeatCount() {
        String text1 = "abcabcabc";  // "abc"重复3次
        String text2 = "123123";     // "123"重复2次
        String text3 = "正常文本无重复";  // 无重复
        String text4 = "||||||||";   // 符号重复，应该被过滤

        assertEquals(3, RepeatedSubstringUtils.getMaxRepeatCount(text1),
                "abc重复3次");
        assertEquals(2, RepeatedSubstringUtils.getMaxRepeatCount(text2),
                "123重复2次");
        assertEquals(0, RepeatedSubstringUtils.getMaxRepeatCount(text3),
                "无重复文本");
        assertEquals(0, RepeatedSubstringUtils.getMaxRepeatCount(text4),
                "符号重复被过滤");
    }

    @Test
    @DisplayName("测试中英文混合重复")
    public void testMixedLanguageRepeat() {
        String mixedRepeat = "测试test测试test测试test";
        assertTrue(RepeatedSubstringUtils.detectRepeater(mixedRepeat, 2),
                "中英文混合重复应该被检测到");

        String mixedRepeat2 = "Hello世界Hello世界Hello世界";
        assertTrue(RepeatedSubstringUtils.detectRepeater(mixedRepeat2, 2),
                "中英文混合重复应该被检测到");
    }

    @Test
    @DisplayName("测试复杂markdown格式")
    public void testComplexMarkdown() {
        String markdownText = """
                # 标题1
                ---
                这是内容
                ## 标题2
                ***
                更多内容
                ### 标题3
                ___
                最后内容""";

        assertFalse(RepeatedSubstringUtils.detectRepeater(markdownText, 2),
                "markdown格式不应该被误判为重复");

        // 但如果有真正的重复内容
        String markdownWithRepeat = """
                # 标题
                ---
                重复内容重复内容重复内容
                ## 子标题
                ***
                更多重复内容重复内容""";

        assertTrue(RepeatedSubstringUtils.detectRepeater(markdownWithRepeat, 2),
                "markdown中的真正重复内容应该被检测到");
    }

    @Test
    @DisplayName("测试长文本性能")
    public void testLongTextPerformance() {
        // 构造一个较长的字符串但没有重复
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("这是第").append(i).append("行内容。");
        }
        String longText = sb.toString();

        // 应该能正常处理而不抛出异常
        assertFalse(RepeatedSubstringUtils.detectRepeater(longText, 2),
                "长文本应该能正常处理");

        // 构造一个有重复的长文本
        StringBuilder sb2 = new StringBuilder();
        String repeatPattern = "重复模式重复模式重复模式";
        for (int i = 0; i < 10; i++) {
            sb2.append(repeatPattern);
        }
        String longRepeatText = sb2.toString();

        assertTrue(RepeatedSubstringUtils.detectRepeater(longRepeatText, 5),
                "长文本中的重复应该被检测到");
    }


    @Test
    @DisplayName("额外的10个长文本测试场景")
    public void testAdditionalLongTextScenarios() {
        System.out.println("\n--- 开始额外10个长文本测试场景 ---");

        // 场景1: 开头重复
        assertTimeoutPreemptively(Duration.ofMillis(30), () -> {
            String unit = "start-repeat-";
            StringBuilder sb = new StringBuilder(2000);
            sb.append(new String(new char[10]).replace("\0", unit));
            for (int i = 0; i < 100; i++) sb.append("unique-part-").append(i);
            String text = sb.toString();
            long s = System.nanoTime();
            assertTrue(RepeatedSubstringUtils.detectRepeater(text, 9), "场景1: 开头重复");
            System.out.println("场景1 (开头重复) 耗时: " + (System.nanoTime() - s) / 1_000_000 + " ms");
        });

        // 场景2: 中间重复
        assertTimeoutPreemptively(Duration.ofMillis(30), () -> {
            String unit = "middle-repeat-";
            StringBuilder sb = new StringBuilder(2000);
            for (int i = 0; i < 50; i++) sb.append("unique-A-").append(i);
            sb.append(new String(new char[10]).replace("\0", unit));
            for (int i = 0; i < 50; i++) sb.append("unique-B-").append(i);
            String text = sb.toString();
            long s = System.nanoTime();
            assertTrue(RepeatedSubstringUtils.detectRepeater(text, 9), "场景2: 中间重复");
            System.out.println("场景2 (中间重复) 耗时: " + (System.nanoTime() - s) / 1_000_000 + " ms");
        });

        // 场景3: 多组不同重复
        assertTimeoutPreemptively(Duration.ofMillis(30), () -> {
            StringBuilder sb = new StringBuilder(2000);
            sb.append(new String(new char[5]).replace("\0", "repeat-A-"));
            sb.append("---filler---");
            sb.append(new String(new char[5]).replace("\0", "repeat-B-"));
            String text = sb.toString();
            long s = System.nanoTime();
            assertTrue(RepeatedSubstringUtils.detectRepeater(text, 4), "场景3: 多组不同重复");
            System.out.println("场景3 (多组不同重复) 耗时: " + (System.nanoTime() - s) / 1_000_000 + " ms");
        });

        // 场景4: 嵌套重复
        assertTimeoutPreemptively(Duration.ofMillis(30), () -> {
            String nestedUnit = new String(new char[3]).replace("\0", "ab");
            String outerUnit = nestedUnit + "c"; // "abababc"
            String text = new String(new char[5]).replace("\0", outerUnit);
            long s = System.nanoTime();
            assertTrue(RepeatedSubstringUtils.detectRepeater(text, 4), "场景4: 嵌套重复");
            System.out.println("场景4 (嵌套重复) 耗时: " + (System.nanoTime() - s) / 1_000_000 + " ms");
        });

        // 场景5: 重叠模式
        assertTimeoutPreemptively(Duration.ofMillis(30), () -> {
            String text = new String(new char[50]).replace("\0", "ab");
            long s = System.nanoTime();
            assertTrue(RepeatedSubstringUtils.detectRepeater(text, 3), "场景5: 重叠模式");
            System.out.println("场景5 (重叠模式) 耗时: " + (System.nanoTime() - s) / 1_000_000 + " ms");
        });

        // 场景6: 隐藏在大量Markdown/符号中的重复
        assertTimeoutPreemptively(Duration.ofMillis(30), () -> {
            StringBuilder sb = new StringBuilder(3000);
            sb.append("# Title\n---\n");
            for (int i = 0; i < 100; i++) sb.append("|--*--|--^--|\n");
            sb.append(new String(new char[4]).replace("\0", "hidden-repeat"));
            for (int i = 0; i < 100; i++) sb.append("~~~...~~~\n");
            String text = sb.toString();
            long s = System.nanoTime();
            assertTrue(RepeatedSubstringUtils.detectRepeater(text, 3), "场景6: 隐藏重复");
            System.out.println("场景6 (隐藏重复) 耗时: " + (System.nanoTime() - s) / 1_000_000 + " ms");
        });

        // 场景7: 超长文本（5000+字符）
        assertTimeoutPreemptively(Duration.ofMillis(100), () -> {
            StringBuilder sb = new StringBuilder(6000);
            for (int i = 0; i < 1000; i++) sb.append("char").append(i);
            sb.append(new String(new char[5]).replace("\0", "long-text-repeat-unit"));
            String text = sb.toString();
            long s = System.nanoTime();
            assertTrue(RepeatedSubstringUtils.detectRepeater(text, 4), "场景7: 超长文本");
            System.out.println("场景7 (超长文本) 耗时: " + (System.nanoTime() - s) / 1_000_000 + " ms");
        });

        // 场景8: 接近最大长度限制的重复模式
        assertTimeoutPreemptively(Duration.ofMillis(30), () -> {
            String unit = new String(new char[23]).replace("\0", "a") + "b" + new String(new char[23]).replace("\0", "a"); // length 47, 包含不同字符
            String text = new String(new char[4]).replace("\0", unit);
            long s = System.nanoTime();
            assertTrue(RepeatedSubstringUtils.detectRepeater(text, 3), "场景8: 接近maxLength的模式");
            System.out.println("场景8 (接近maxLength的模式) 耗时: " + (System.nanoTime() - s) / 1_000_000 + " ms");
        });

        // 场景9: "几乎"重复的模式
        assertTimeoutPreemptively(Duration.ofMillis(30), () -> {
            StringBuilder sb = new StringBuilder(2000);
            for (int i = 0; i < 10; i++) {
                sb.append("almost-repeat-").append(i).append("-");
            }
            String text = sb.toString();
            long s = System.nanoTime();
            assertFalse(RepeatedSubstringUtils.detectRepeater(text, 3), "场景9: '几乎'重复的模式");
            System.out.println("场景9 ('几乎'重复的模式) 耗时: " + (System.nanoTime() - s) / 1_000_000 + " ms");
        });

        // 场景10: 混合短重复和长重复
        assertTimeoutPreemptively(Duration.ofMillis(30), () -> {
            StringBuilder sb = new StringBuilder(2000);
            sb.append(new String(new char[2]).replace("\0", "abc"));
            sb.append(new String(new char[2]).replace("\0", "def"));
            sb.append(new String(new char[4]).replace("\0", "trigger"));
            sb.append(new String(new char[2]).replace("\0", "jkl"));
            String text = sb.toString();
            long s = System.nanoTime();
            assertTrue(RepeatedSubstringUtils.detectRepeater(text, 3), "场景10: 混合短重复和长重复");
            System.out.println("场景10 (混合重复) 耗时: " + (System.nanoTime() - s) / 1_000_000 + " ms");
        });

        System.out.println("--- 额外10个长文本测试场景结束 ---\n");
    }
}
