package com.chinatelecom.gs.engine.channel.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.channel.common.enums.ConfigKeyEnums;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelConfigPO;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelConfigRepository;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.channel.service.dto.QywxConfigDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/25 15:33
 * @description
 */
@Slf4j
@Service
public class QywxConfigService {

    @Resource
    private ChannelConfigRepository configRepository;
    @Resource
    private ChannelInfoRepository channelInfoRepository;

    /**
     * 添加企微渠道配置
     *
     * @param channelId     String
     * @param robotCode     String
     * @param qywxConfigDTO QywxConfigDTO
     * @return Boolean
     */
//    @AgentCodeCheck
    public Boolean addConfig(String channelId, String robotCode, QywxConfigDTO qywxConfigDTO) {
        RequestInfo userInfo = RequestContext.get();
//        checkParam(robotCode, qywxConfigDTO);
        if (StringUtils.isEmpty(qywxConfigDTO.getToken())) {
            String token = RandomStringUtils.randomAlphanumeric(32);
            qywxConfigDTO.setToken(token);
        }
        if (StringUtils.isEmpty(qywxConfigDTO.getEncodingAESKey())) {
            String encodedAes = RandomStringUtils.randomAlphanumeric(43);
            qywxConfigDTO.setEncodingAESKey(encodedAes);
        }
        ChannelInfoDTO channelInfo = channelInfoRepository.getChannelInfo(channelId, robotCode);
        if (channelInfo == null) {
            throw new BizException("A0049", "渠道信息不正确，请校验参数");
        }
        ChannelConfigPO channelConfigPO = getChannelConfigPO(channelId);
        qywxConfigDTO.setUpdateName(userInfo.getUserName());
        qywxConfigDTO.setCreateTime(LocalDateTime.now());
        qywxConfigDTO.setAgentCode(robotCode);
        if (channelConfigPO != null) {
            // 修改
            channelConfigPO.setConfigValue(JsonUtils.toJsonString(qywxConfigDTO));
            return configRepository.updateById(channelConfigPO);
        }
        // 创建
        ChannelConfigPO configPO = new ChannelConfigPO();
        configPO.setChannelId(channelId);
        configPO.setConfigKey(ConfigKeyEnums.QYWX_APP_CONFIG.getCode());
        configPO.setConfigValue(JsonUtils.toJsonString(qywxConfigDTO));
        return configRepository.save(configPO);
    }

//    /**
//     * 校验robotCode 、secret、corpId
//     *
//     * @param robotCode     String
//     * @param qywxConfigDTO QywxConfigDTO
//     */
//    private void checkParam(String robotCode, QywxConfigDTO qywxConfigDTO) {
//        userAuthorizationCheckService.valid(robotCode, true);
//        if (qywxConfigDTO.getSecret().length() > 120) {
//            throw new BizException(ResultCode.PARAM_EXCEPTION.getCode(), "secret超过长度，请控制在120个字符");
//        }
//        if (qywxConfigDTO.getCorpId().length() > 120) {
//            throw new BizException(ResultCode.PARAM_EXCEPTION.getCode(), "corpId超过长度，请控制在120个字符");
//        }
//    }

    /**
     * 查询企微渠道配置
     *
     * @param channelId String
     * @param robotCode String
     * @return QywxConfigDTO
     */
    public QywxConfigDTO getQywxConfig(String channelId, String robotCode) {
//        userAuthorizationCheckService.valid(robotCode, false);
        ChannelInfoDTO channelInfo = channelInfoRepository.getChannelInfo(channelId, robotCode);
        if (channelInfo == null) {
            throw new BizException("A0049", "渠道信息不正确，请校验参数");
        }
        ChannelConfigPO configPO = getChannelConfigPO(channelId);
        if (configPO == null) {
            return null;
        }
        return JsonUtils.parseObject(configPO.getConfigValue(), QywxConfigDTO.class);
    }

    /**
     * 查询企业微信渠道配置
     *
     * @param channelId String
     * @return ChannelConfigPO
     */
    private ChannelConfigPO getChannelConfigPO(String channelId) {
        LambdaQueryWrapper<ChannelConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelConfigPO::getChannelId, channelId)
                .eq(ChannelConfigPO::getConfigKey, ConfigKeyEnums.QYWX_APP_CONFIG.getCode());
        return configRepository.getOne(queryWrapper);
    }
}
