package com.chinatelecom.gs.engine.common.utils;

import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.platform.client.rpc.Permission;
import com.chinatelecom.cloud.platform.client.rpc.Team;
import com.chinatelecom.cloud.platform.client.rpc.UserInfo;
import com.chinatelecom.cloud.platform.client.util.AuthUtils;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.TeamInfo;
import com.chinatelecom.gs.engine.common.platform.PlatformAuthService;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月30日
 */
@Slf4j
public class UserInfoUtils {

    private static final Cache<String, List<TeamInfo>> CACHE = Caffeine.newBuilder().expireAfterWrite(10, TimeUnit.SECONDS).maximumSize(1000).build();

    private static final Cache<String, UserInfo> USER_CACHE = Caffeine.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS).maximumSize(1000).build();

    private static final Cache<String, Boolean> USER_ADMIN_CACHE = Caffeine.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS).maximumSize(1000).build();
    private static final Cache<String, Boolean> USER_IS_ADMIN_CACHE = Caffeine.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS).maximumSize(1000).build();

    private static final Cache<String, List<Permission>> RESOURCE_LIST_CACHE = Caffeine.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS).maximumSize(1000).build();


    /**
     * 获取用户团队信息
     *
     * @return
     */
    public static List<TeamInfo> getUserTeam() {
        String key = StringUtils.join(RequestContext.getUserId(), "_", RequestContext.getTenantId());

        List<TeamInfo> result = CACHE.getIfPresent(key);
        if (result == null) {
            List<TeamInfo> teamList = Lists.newArrayList();
            try {
                BaseResult<List<Team>> baseResult = AuthUtils.getTeamByUser(RequestContext.getUserId(), RequestContext.getTenantId());
                if (baseResult != null && baseResult.ifSuccess()) {
                    List<Team> teams = baseResult.getData();
                    if (CollectionUtils.isNotEmpty(teams)) {
                        teamList.addAll(teams.stream().map(team -> TeamInfo.builder()
                                .teamCode(team.getTeamCode()).name(team.getName()).build()).collect(Collectors.toList()));
                        CACHE.put(key, teamList);
                    }
                } else {
                    log.warn("查询用户团队信息失败，result:{}", JsonUtils.toJsonString(baseResult));
                }
            } catch (Exception e) {
                log.error("查询用户团队信息异常", e);
//            throw new BizException(e, "00023", "查询用户团队信息异常");
            }
            return teamList;
        } else {
            return result;
        }
    }

    public static UserInfo getUserInfo(String tenantId, String userId) {
        String key = StringUtils.join(userId, "_", tenantId);
        UserInfo result = USER_CACHE.getIfPresent(key);
        if (result == null) {
            BaseResult<UserInfo> baseResult = AuthUtils.getUserById(tenantId, userId);
            if (baseResult != null && baseResult.ifSuccess() && baseResult.getData() != null) {
                result = baseResult.getData();
                USER_CACHE.put(key, result);
            }
        }
        return result;
    }

    public static boolean isAdmin(String tenantId, String userId) {
        String key = StringUtils.join(userId, "_", tenantId);
        Boolean result = USER_IS_ADMIN_CACHE.getIfPresent(key);
        if (result == null) {
            try {
                Boolean isAdmin = null;
                BaseResult<Boolean> appOwner = AuthUtils.isAppOwner(userId, tenantId);
                if (appOwner != null && appOwner.ifSuccess() && appOwner.getData() != null && appOwner.getData()) {
                    isAdmin = appOwner.getData();
                } else {
                    isAdmin = false;
                }
                result = isAdmin;
                USER_IS_ADMIN_CACHE.put(key, isAdmin);
            } catch (Exception e) {
                log.error("查询用户是否管理员失败, tenantId: {}， userId:{}", tenantId, userId);
                result = false;
            }
        }
        return result;
    }


    public static Boolean checkUserAdmin(String tenantId, String userId, AppSourceType source) {
        String key = StringUtils.join(userId, "_", tenantId, "_", source.name());
        Boolean result = USER_ADMIN_CACHE.getIfPresent(key);
        if (result == null) {
            BaseResult<Boolean> appOwner = AuthUtils.isAppOwner(userId, tenantId);
            if (appOwner != null && appOwner.ifSuccess() && appOwner.getData() != null) {
                result = appOwner.getData();
                USER_ADMIN_CACHE.put(key, result);
            }
        }
        return result;
    }


    /**
     * 检查是否拥有某个资源权限
     *
     * @param resourceCode
     * @return
     */
    public static boolean checkResource(String resourceCode) {
        boolean result = false;
        PlatformAuthService platformAuthService = SpringContextUtils.getBean(PlatformAuthService.class);
        List<String> resourceList = platformAuthService.getResourceList();
        if (CollectionUtils.isNotEmpty(resourceList)) {
            result = resourceList.contains(resourceCode);
        }
        return result;
    }


}
