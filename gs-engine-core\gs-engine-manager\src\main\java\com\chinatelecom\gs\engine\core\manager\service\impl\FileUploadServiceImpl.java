package com.chinatelecom.gs.engine.core.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.amazonaws.services.s3.model.PartETag;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.UploadStatus;
import com.chinatelecom.gs.engine.common.infra.base.impl.BaseExtendServiceImpl;
import com.chinatelecom.gs.engine.common.s3.CloudStorageDao;
import com.chinatelecom.gs.engine.common.utils.FileNameUtils;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.core.manager.infra.mapper.FileUploadMapper;
import com.chinatelecom.gs.engine.core.manager.infra.po.UploadPO;
import com.chinatelecom.gs.engine.core.manager.param.UploadPartParam;
import com.chinatelecom.gs.engine.core.manager.param.UploadStartParam;
import com.chinatelecom.gs.engine.core.manager.service.FileUploadService;
import com.chinatelecom.gs.engine.core.manager.vo.UploadCompleteResult;
import com.chinatelecom.gs.engine.core.manager.vo.UploadStartResult;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: Wei
 * @date: 2025-04-22 10:34
 */
@Service
public class FileUploadServiceImpl extends BaseExtendServiceImpl<FileUploadMapper, UploadPO> implements FileUploadService {

    @Resource
    private CloudStorageDao cloudStorageDao;


    @Override
    public UploadStartResult startUpload(UploadStartParam param) {
        KnowledgeType type = KnowledgeType.fromFileName(param.getName());
        if (type == null) {
            throw new BizException("AA003", "文件类型不支持");
        }
        UploadStartResult result = new UploadStartResult();
        UploadPO uploadPO = baseMapper.selectOne(Wrappers.<UploadPO>lambdaQuery()
                .eq(UploadPO::getMd5, param.getMd5())
                .eq(UploadPO::getStatus, UploadStatus.uploading.name())
                .eq(UploadPO::getName, param.getName())
                .eq(UploadPO::getCreateId, RequestContext.getUserId())
                .last("limit 1"));
        if(Objects.nonNull(uploadPO)){
            if(!uploadPO.getPartSize().equals(param.getPartSize())){
                throw new BizException("AA064", "分片大小不一致");
            }
            //上传中 获取已上传完成分片信息
            result.setStatus(UploadStatus.uploading.name());
            result.setUploadId(uploadPO.getUploadId());
            List<Integer> parts = cloudStorageDao.listParts(uploadPO.getUploadId(), uploadPO.getFileKey())
                    .stream().map(PartETag::getPartNumber).collect(Collectors.toList());
            result.setUploadedParts(parts);
            return result;
        }

        String fileKey = "upload/%s/%s%s".formatted(type.name().toLowerCase(), IdGenerator.id(), FileNameUtils.getFileSuffixWithPoint(param.getName()));
        String uploadId = cloudStorageDao.startPartUpload(fileKey);
        UploadPO po = new UploadPO();
        po.setCode(fileKey);
        po.setUploadId(uploadId);
        po.setName(param.getName());
        po.setFileKey(fileKey);
        po.setPartSize(param.getPartSize());
        po.setTotalPartNum(param.getTotalPartNum());
        po.setStatus(UploadStatus.uploading.name());
        po.setMd5(param.getMd5());
        po.setTotalSize(param.getTotalSize());
        po.setTenantId(RequestContext.getTenantId());
        this.save(po);
        result.setStatus(UploadStatus.unUpload.name());
        result.setUploadId(uploadId);
        result.setUploadedParts(CollUtil.newArrayList());
        return result;
    }

    @Override
    public String uploadPart(UploadPartParam param) throws IOException {
        UploadPO uploadPO = getUploadPO(param.getUploadId());
        InputStream inputStream = param.getPart().getInputStream();
        long size = param.getPart().getSize();
        return cloudStorageDao.uploadPart(uploadPO.getFileKey(), param.getUploadId(), inputStream, size, param.getPartNumber());
    }

    @Override
    public UploadCompleteResult completeUpload(String uploadId) {
        UploadPO uploadPO = getUploadPO(uploadId);
        if(!UploadStatus.uploading.name().equals(uploadPO.getStatus())){
            throw new BizException("AA071", "完成上传失败，文件已上传完成或已取消上传");
        }
        List<PartETag> partETags = cloudStorageDao.listParts(uploadId, uploadPO.getFileKey());
        if(CollUtil.isEmpty(partETags)){
            updateStatus(uploadPO, UploadStatus.abort);
            throw new BizException("AA067", "文件分片不存在，请重新上传");
        }
        String fileKey = cloudStorageDao.completePartUpload(uploadId, uploadPO.getFileKey(), partETags);
        if (StrUtil.isBlank(fileKey)){
            updateStatus(uploadPO, UploadStatus.abort);
            throw new BizException("AA068", "文件分片合并失败，请重新上传");
        }
        long size = cloudStorageDao.getObjectMetadata(uploadPO.getFileKey()).getInstanceLength();
        if (!uploadPO.getTotalSize().equals(size)) {
            updateStatus(uploadPO, UploadStatus.abort);
            cloudStorageDao.remove(uploadPO.getFileKey());
            throw new BizException("AA065", "上传文件大小不匹配，请重新上传");
        }
        updateStatus(uploadPO, UploadStatus.finished);
        UploadCompleteResult result = new UploadCompleteResult();
        result.setFileKey(uploadPO.getFileKey());
        return result;
    }

    private void updateStatus(UploadPO uploadPO, UploadStatus status){
        uploadPO.setStatus(status.name());
        this.updateById(uploadPO);
    }

    @Override
    public void abortUpload(String uploadId) {
        UploadPO uploadPO = getUploadPO(uploadId);
        if(!UploadStatus.uploading.name().equals(uploadPO.getStatus())){
            throw new BizException("AA069", "取消上传失败，文件已上传或取消成功");
        }
        cloudStorageDao.abortMultipartUpload(uploadId, uploadPO.getFileKey());
        uploadPO.setStatus(UploadStatus.abort.name());
        updateById(uploadPO);
    }

    private UploadPO getUploadPO(String uploadId){
        UploadPO uploadPO = baseMapper.selectOne(Wrappers.<UploadPO>lambdaQuery()
                .eq(UploadPO::getUploadId,uploadId)
                .eq(UploadPO::getCreateId, RequestContext.getUserId())
                .last("limit 1"));
        if(Objects.isNull(uploadPO)){
            throw new BizException("AA066", "上传文件不存在");
        }
        return uploadPO;
    }

}
