package com.chinatelecom.gs.engine.core.corekit.common.core;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.chinatelecom.gs.engine.core.corekit.common.core.builder.AnswerBuilder;
import com.chinatelecom.gs.engine.core.corekit.common.core.util.HtmlUtils;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.utils.IdGenerator;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.Answer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.entry.RichTextCard;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.entry.RichTextOptionCard;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.option.OptionEntry;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.request.AnswerBuildRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class AnswerService {

    @Autowired
    private List<AnswerBuilder> answerBuilders;

    public String getContent(String answerType, Object answerContent) {
        if (answerContent instanceof String content) {
            if (answerType.equals(AnswerTypeEnum.SIMPLE_RICH_TEXT.getCode())) {
                return delHTMLTag(content);
            }
            return content;
        }

        if (answerContent instanceof JSONObject jsonObject) {
            if (answerType.equalsIgnoreCase(AnswerTypeEnum.RICH_TEXT_OPTION.getCode())) {
                RichTextOptionCard richTextOptionCard = JSON.parseObject(jsonObject.toJSONString(), RichTextOptionCard.class);
                String content = joinCardOptionContent(richTextOptionCard);
                return delHTMLTag(content);
            } if (answerType.equalsIgnoreCase(AnswerTypeEnum.CARD_OPTION.getCode())){
                RichTextOptionCard richTextOptionCard = JSON.parseObject(jsonObject.toJSONString(), RichTextOptionCard.class);
                String content = joinCardOptionContent(richTextOptionCard);
                return delHTMLTag(content);
            }else {
                // 处理其他类型的 JSONObject
                // 这里可以根据具体需求进行处理
                return jsonObject.toJSONString();
            }
        }
        if(answerContent instanceof RichTextOptionCard richTextOptionCard){
            String content = joinCardOptionContent(richTextOptionCard);
            return delHTMLTag(content);
        }
        if (Objects.nonNull(answerContent)) {
            return JSON.toJSONString(answerContent);
        }

        // 如果 answerContent 既不是 String 也不是 JSONObject，返回 null 或抛出异常
        return null;
    }

    private String delHTMLTag(String content) {
        return HtmlUtils.delHTMLTag(content);
    }

    public Answer buildAnswer(AnswerBuildRequest answerBuildRequest) {
        AnswerBuilder answerBuilder = this.answerBuilders.stream().filter(builder -> builder.valid(answerBuildRequest)).findFirst().orElse(null);

        if (Objects.nonNull(answerBuilder)) {
            Answer answer = answerBuilder.build(answerBuildRequest);
            answer.setMessageId(IdGenerator.getMessageId());
            return answer;
        }
        return null;
    }

    public String joinCardOptionContent(RichTextOptionCard richTextOptionCard){
        if(Objects.isNull(richTextOptionCard)){
            return null;
        }
        StringBuilder contentBuilder = new StringBuilder();
        contentBuilder.append(richTextOptionCard.getShowCard().getHtml());
        if(Objects.nonNull(richTextOptionCard.getOption())){
            for(OptionEntry<RichTextCard> optionEntry : richTextOptionCard.getOption().getEntries()){
                contentBuilder.append("\n").append(optionEntry.getShowCard().getHtml());
            }
        }
        return contentBuilder.toString();
    }

}
