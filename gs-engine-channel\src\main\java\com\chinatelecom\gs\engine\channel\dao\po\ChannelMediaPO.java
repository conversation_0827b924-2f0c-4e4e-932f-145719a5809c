package com.chinatelecom.gs.engine.channel.dao.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/17 17:16
 * @desc
 */
@Setter
@Getter
@TableName("channel_media")
public class ChannelMediaPO extends BasePO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableField("corp_id")
    private String corpId;

    @TableField("media_type")
    private String mediaType;

    @TableField("media_url")
    private String mediaUrl;

    @TableField("media_id")
    private String mediaId;

    @TableField("expire_time")
    private Long expireTime;

}
