package com.chinatelecom.gs.engine.core.manager.model;

import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.common.model.base.RootResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月28日
 */
@Getter
@Setter
public class BaseConfigResponse extends RootResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    private DimensionEnum dimension;

    /**
     * 业务配置场景
     */
    private String scene;

    /**
     * 业务配置场景说明
     */
    private String sceneDesc;

    /**
     * 业务场景配置唯一标识
     */
    private String businessNo;

    /**
     * 配置json格式value值
     */
    private String objValue;

    /**
     * 配置json的class对象
     */
    private String objClass;

}
