//package com.chinatelecom.gs.engine.core.entity.service.impl;
//
//import com.chinatelecom.gs.engine.core.entity.ability.recognition.EntityRecognitionAbilityHolder;
//import com.chinatelecom.gs.engine.core.entity.ability.recognition.EntityRecognitionAbilityService;
//import com.chinatelecom.gs.engine.core.entity.ability.validator.EntityValidatorAbilityHolder;
//import com.chinatelecom.gs.engine.core.entity.ability.validator.EntityValidatorAbilityService;
//import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
//import com.chinatelecom.gs.engine.core.entity.domain.request.EntityValidatorRequest;
//import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionResponse;
//import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityVO;
//import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
//import com.chinatelecom.gs.engine.core.entity.service.EntityService;
//import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
//import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityTypeEnum;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//
//
//import java.util.*;
//import java.util.concurrent.*;
//
//import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.when;
//
//
//public class EntityRecognitionServiceImplTest {
//
//    @InjectMocks
//    private EntityRecognitionServiceImpl recognitionService;
//
//    @Mock
//    private EntityService entityService;
//
//    @Mock
//    private EntityRecognitionAbilityHolder entityRecognitionAbilityHolder;
//
//    @Mock
//    private EntityValidatorAbilityHolder entityValidatorAbilityHolder;
//
//    @Mock
//    private ExecutorService commonExecutorService;
//
//    @BeforeEach
//    public void setUp() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//    @Test
//    public void testPredict_EntityListEmpty_ReturnsEmptyResponse() {
//        // Arrange
//        EntityRecognitionRequest request = new EntityRecognitionRequest();
//        request.setQuery("test query");
//        request.setEntityCodes(Arrays.asList("code1", "code2"));
//
//        when(entityService.getEntityList(anyList())).thenReturn(Collections.emptyList());
//
//        // Act
//        EntityRecognitionResponse response = recognitionService.predict(request);
//
//        // Assert
//        assertNotNull(response);
//        assertTrue(response.getEntities().isEmpty());
//    }
//
//    @Test
//    public void testPredict_RecognitionSuccessAndValidationPasses() throws Exception {
//        // Arrange
//        EntityRecognitionRequest request = new EntityRecognitionRequest();
//        request.setQuery("test query");
//        request.setSessionId("session1");
//        request.setMessageId("msg1");
//        request.setTenantId("tenant1");
//        request.setEntityCodes(Arrays.asList("code1", "code2"));
//
//        EntityVO vo1 = new EntityVO();
//        vo1.setEntityCode("code1");
//        vo1.setAbility(EntityAbilityEnum.MODEL_RECOGNITION);
//        vo1.setValidatorSwitch(true);
//        vo1.setValidatorType("regex_validator");
//
//        EntityVO vo2 = new EntityVO();
//        vo2.setEntityCode("code2");
//        vo2.setAbility(EntityAbilityEnum.REGEX_MATCH);
//        vo2.setValidatorSwitch(false);
//        vo2.setValidatorType("regex_validator");
//
//        List<EntityVO> vos = Arrays.asList(vo1, vo2);
//        when(entityService.getEntityList(anyList())).thenReturn(vos);
//
//        // 模拟模型识别服务
//        EntityRecognitionAbilityService modelService = mock(EntityRecognitionAbilityService.class);
//        Entity entity = new Entity();
//        entity.setEntityCode("code1");
//        entity.setEntityType(EntityTypeEnum.NORMAL_ENTITY);
//
//        when(modelService.predict(any())).thenReturn(Collections.singletonList(entity));
//
//        EntityRecognitionAbilityService regexService = mock(EntityRecognitionAbilityService.class);
//        Entity entity1 = new Entity();
//        entity1.setEntityCode("code1");
//        entity1.setEntityType(EntityTypeEnum.NORMAL_ENTITY);
//
//        when(modelService.predict(any())).thenReturn(Collections.singletonList(entity1));
//
//        when(entityRecognitionAbilityHolder.getAbilityService(eq(EntityAbilityEnum.MODEL_RECOGNITION.getCode() + ":default"))).thenReturn(modelService);
//        when(entityRecognitionAbilityHolder.getAbilityService(eq(EntityAbilityEnum.REGEX_MATCH.getCode()))).thenReturn(regexService);
//
//        // 模拟线程池
//        when(commonExecutorService.invokeAll(anyCollection())).thenAnswer(invocation -> {
//            Collection<Callable<List<Entity>>> tasks = invocation.getArgument(0);
//            List<Future<List<Entity>>> futures = new ArrayList<>();
//            for (Callable<List<Entity>> task : tasks) {
//                List<Entity> result = task.call();
//                Future<List<Entity>> future = mock(Future.class);
//                when(future.get()).thenReturn(result);
//                futures.add(future);
//            }
//            return futures;
//        });
//
//        // 模拟校验服务
//        EntityValidatorAbilityService validatorService = mock(EntityValidatorAbilityService.class);
//        when(validatorService.validator(any(EntityValidatorRequest.class))).thenReturn(true);
//        when(entityValidatorAbilityHolder.getValidatorAbilityService(anyString())).thenReturn(validatorService);
//
//        // Act
//        EntityRecognitionResponse response = recognitionService.predict(request);
//
//        // Assert
//        assertNotNull(response);
//        assertEquals(0, response.getEntities().size());
//    }
//
//    @Test
//    public void testPredict_ValidationFails() throws Exception {
//        // Arrange
//        EntityRecognitionRequest request = new EntityRecognitionRequest();
//        request.setQuery("test query");
//        request.setSessionId("session1");
//        request.setMessageId("msg1");
//        request.setTenantId("tenant1");
//        request.setEntityCodes(Collections.singletonList("code1"));
//
//        EntityVO vo1 = mock(EntityVO.class);
//        when(vo1.getEntityCode()).thenReturn("code1");
//        when(vo1.getAbility()).thenReturn(EntityAbilityEnum.MODEL_RECOGNITION);
//        when(vo1.getValidatorSwitch()).thenReturn(true);
//        when(vo1.getValidatorType()).thenReturn("validatorType");
//
//        when(entityService.getEntityList(anyList())).thenReturn(Collections.singletonList(vo1));
//
//        EntityRecognitionAbilityService service = mock(EntityRecognitionAbilityService.class);
//        Entity entity1 = new Entity();
//        entity1.setEntityCode("code1");
//        entity1.setEntityType(EntityTypeEnum.NORMAL_ENTITY);
//        when(service.predict(any())).thenReturn(Collections.singletonList(entity1));
//        when(entityRecognitionAbilityHolder.getAbilityService(anyString())).thenReturn(service);
//
//        when(commonExecutorService.invokeAll(anyCollection())).thenAnswer(invocation -> {
//            Collection<Callable<List<Entity>>> tasks = invocation.getArgument(0);
//            List<Future<List<Entity>>> futures = new ArrayList<>();
//            for (Callable<List<Entity>> task : tasks) {
//                List<Entity> result = task.call();
//                Future<List<Entity>> future = mock(Future.class);
//                when(future.get()).thenReturn(result);
//                futures.add(future);
//            }
//            return futures;
//        });
//
//        EntityValidatorAbilityService validatorService = mock(EntityValidatorAbilityService.class);
//        when(validatorService.validator(any(EntityValidatorRequest.class))).thenReturn(false);
//        when(entityValidatorAbilityHolder.getValidatorAbilityService(anyString())).thenReturn(validatorService);
//
//        // Act
//        EntityRecognitionResponse response = recognitionService.predict(request);
//
//        // Assert
//        assertNotNull(response);
//        assertTrue(response.getEntities().isEmpty());
//    }
//}
