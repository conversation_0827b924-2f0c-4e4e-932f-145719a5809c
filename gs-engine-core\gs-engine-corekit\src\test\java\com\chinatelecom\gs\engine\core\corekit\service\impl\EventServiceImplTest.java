package com.chinatelecom.gs.engine.core.corekit.service.impl;

import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.mq.impl.KafkaMQService;
import com.chinatelecom.gs.engine.core.corekit.domain.request.EventRequest;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

public class EventServiceImplTest {

    @InjectMocks
    private EventServiceImpl eventService;

    @Mock
    private KafkaMQService kafkaMQService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testSendEvent_SendPageMsgFalse_ReturnsFalse() {
        // Arrange
        ReflectionTestUtils.setField(eventService, "sendPageMsg", false);

        // Act
        Boolean result = eventService.sendEvent(new EventRequest());

        // Assert
        assertFalse(result);
    }

    @Test
    public void testSendEvent_EventRequestIsNull_ReturnsFalse() {
        // Arrange
        ReflectionTestUtils.setField(eventService, "sendPageMsg", true);

        // Act
        Boolean result = eventService.sendEvent(null);

        // Assert
        assertFalse(result);
    }

    @Test
    public void testSendEvent_ValidRequest_SendsMessageSuccessfully() {
        // Arrange
        ReflectionTestUtils.setField(eventService, "sendPageMsg", true);
        try (MockedStatic<RequestContext> mockRequestContext1 = mockStatic(RequestContext.class)) {
            mockRequestContext1.when(() -> RequestContext.getAppSourceType()).thenReturn(AppSourceType.KS);
            try (MockedStatic<RequestContext> mockRequestContext2 = mockStatic(RequestContext.class)) {
                mockRequestContext2.when(() -> RequestContext.getTenantId()).thenReturn("tenant123");
                try (MockedStatic<RequestContext> mockRequestContext3 = mockStatic(RequestContext.class)) {
                    mockRequestContext3.when(() -> RequestContext.getUserId()).thenReturn("user123");

                    EventRequest request = createSampleEventRequest();

                    doNothing().when(kafkaMQService).sendMessage(anyString(), anyString());

                    // Act
                    Boolean result = eventService.sendEvent(request);

                    // Assert
                    assertTrue(result);
                    verify(kafkaMQService, times(1)).sendMessage(anyString(), anyString());
                }
            }
        }
    }

    private EventRequest createSampleEventRequest() {
        EventRequest request = new EventRequest();
        request.setLogId("log123");
        request.setAppCode("app123");
        request.setEventType("click");
        request.setTerminal("pc");
        request.setBrowser("chrome");
        request.setRefer("index.html");
        request.setUrl("detail.html");
        request.setTimestamp(System.currentTimeMillis());

        EventRequest.Meta meta = new EventRequest.Meta();
        meta.setA("Home Page");
        meta.setB("Header");
        meta.setC("Login Button");
        meta.setParams("{\"key\":\"value\"}");
        request.setMeta(meta);

        return request;
    }
}
