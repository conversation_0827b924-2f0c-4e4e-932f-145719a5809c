package com.chinatelecom.gs.engine.common.utils;

import java.util.List;

/**
 * 滚动查询处理
 * 主要用于需要批量处理一些任务时,但是一次性全部从数据库查出可能导致OOM,所以添加指定批量大小的功能
 */
public class ScrollQueryProcess {

    private ScrollQueryProcess() {
    }

    /**
     * 默认每批次处理的数量
     */
    public static final Integer DEFAULT_SCROLL_SIZE = 1000;
    /**
     * 默认的起始ID
     */
    public static final Long DEFAULT_START_ID = 0L;

    /**
     * 一些批量处理的数据可能会非常大，为避免一次性查询处理导致内存爆炸，所以添加滚动查询
     *  注意条件正确，避免形成死循环
     * @param scrollBase
     * @param <T>
     */
    public static <T> void scrollSubmit(ScrollBase<T> scrollBase) {
        scrollSubmit(scrollBase, DEFAULT_START_ID, DEFAULT_SCROLL_SIZE);
    }

    /**
     * 一些批量处理的数据可能会非常大，为避免一次性查询处理导致内存爆炸，所以添加滚动查询
     * 注意条件正确，避免形成死循环
     * @param scrollBase
     * @param startId
     * @param scrollSize
     * @param <T>
     */
    public static <T> void scrollSubmit(ScrollBase<T> scrollBase, Long startId, Integer scrollSize) {
        while (true) {
            List<T> scrollResult = scrollBase.scrollFind(startId, scrollSize);
            if (scrollResult != null && scrollResult.size() > 0) {
                scrollBase.scrollProcess(scrollResult);
                T lastT = scrollResult.get(scrollResult.size() - 1);
                startId = scrollBase.getId(lastT);
            }

            if (scrollResult == null || scrollResult.size() == 0 || scrollResult.size() < scrollSize) {
                break;
            }
        }
    }

    public interface ScrollBase<T> {

        /**
         * 滚动查询逻辑,查询需要根据ID进行升序排序
         *
         * @param startId 查询条件 > startId
         * @param scrollSize 查询条件 limit scrollSize
         * @return
         */
        List<T> scrollFind(Long startId, Integer scrollSize);

        /**
         * 获取id值
         *
         * @param t
         * @return
         */
        Long getId(T t);

        /**
         * 滚动处理逻辑
         *
         * @param scrollFindResult
         * @return 最后一条的ID
         */
        void scrollProcess(List<T> scrollFindResult);

    }
}
