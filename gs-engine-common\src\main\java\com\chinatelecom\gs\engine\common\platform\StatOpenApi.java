package com.chinatelecom.gs.engine.common.platform;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @description openapi埋点统计
 * @date 2025年04月10日
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface StatOpenApi {


    /**
     * 接口名称
     */
    String name();

    /**
     * 分组名称
     */
    String groupName();

}
