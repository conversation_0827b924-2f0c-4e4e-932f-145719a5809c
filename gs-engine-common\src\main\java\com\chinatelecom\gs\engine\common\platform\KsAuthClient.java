package com.chinatelecom.gs.engine.common.platform;

import com.chinatelecom.cloud.platform.client.rpc.Menu;
import com.chinatelecom.cloud.platform.client.rpc.Permission;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 政务壳子查询权限client
 * @date 2025年07月29日
 */

@FeignClient(name = "ksAuthClient", url = "${platform.client.ksHost:}")
public interface KsAuthClient {
    /**
     * 查询是否管理员
     *
     * @return Boolean
     */
    @GetMapping("/ais/ks/rpc/platform/isAppOwner")
    Result<Boolean> isAppOwner();

    /**
     * 查询用户授权菜单
     *
     * @return List<String>
     */
    @GetMapping("/ais/ks/rpc/platform/menu")
    Result<List<Menu>> getMenuList();

    /**
     * 查询用户授权资源
     *
     * @return List<String>
     */
    @GetMapping("/ais/ks/rpc/platform/resource")
    Result<List<Permission>> getResourceList();
}
