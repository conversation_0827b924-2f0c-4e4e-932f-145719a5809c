package com.chinatelecom.gs.engine.common.config.swagger;

import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.parameters.HeaderParameter;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.web.method.HandlerMethod;

import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 为OpenAPI接口添加必需的Header参数
 *
 * <AUTHOR>
 */
public class OpenApiHeaderParameterCustomizer implements OperationCustomizer {

    private final ApiType currentApiType;

    public OpenApiHeaderParameterCustomizer(ApiType currentApiType) {
        this.currentApiType = currentApiType;
    }

    @Override
    public Operation customize(Operation operation, HandlerMethod handlerMethod) {
        // 检查接口是否应该在当前API类型中隐藏
        Method method = handlerMethod.getMethod();

        // 检查方法级别的@HideFromApiTypes注解
        HideFromApiTypes hideAnnotation = method.getAnnotation(HideFromApiTypes.class);

        // 如果方法上没有注解，检查类级别的注解
        if (hideAnnotation == null) {
            hideAnnotation = method.getDeclaringClass().getAnnotation(HideFromApiTypes.class);
        }

        // 如果找到了隐藏注解，检查是否需要在当前API类型中隐藏
        if (hideAnnotation != null) {
            ApiType[] hiddenTypes = hideAnnotation.value();
            if (Arrays.asList(hiddenTypes).contains(currentApiType)) {
                // 如果需要隐藏，则直接返回null，不添加任何Header参数
                return operation;
            }
        }

        // 只对OpenAPI类型的接口添加Header参数
        if (currentApiType == ApiType.OPENAPI) {
            // 添加x-sign参数
            HeaderParameter signParameter = new HeaderParameter();
            signParameter.setName("Authorization");
            signParameter.setDescription("请求签名，签名逻辑见接口鉴权");
            signParameter.setRequired(true);
            StringSchema signSchema = new StringSchema();
            signSchema.setExample("abc123def456");
            signParameter.setSchema(signSchema);

            // 添加x-userid参数
            HeaderParameter userIdParameter = new HeaderParameter();
            userIdParameter.setName("x-userid");
            userIdParameter.setDescription("用户id");
            userIdParameter.setRequired(true);
            StringSchema userIdSchema = new StringSchema();
            userIdSchema.setExample("123456");
            userIdParameter.setSchema(userIdSchema);

            // 添加x-tenantid参数
            HeaderParameter tenantIdParameter = new HeaderParameter();
            tenantIdParameter.setName("x-tenantid");
            tenantIdParameter.setDescription("租户id");
            tenantIdParameter.setRequired(true);
            StringSchema tenantIdSchema = new StringSchema();
            tenantIdSchema.setExample("ks");
            tenantIdParameter.setSchema(tenantIdSchema);

            // 添加x-source参数
            HeaderParameter sourceParameter = new HeaderParameter();
            sourceParameter.setName("x-source");
            sourceParameter.setDescription("操作来源系统 一般情况默认KS 类型：KS:智文;BOT:机器人;OUTBOUND:外呼;");
            sourceParameter.setRequired(true);
            StringSchema sourceSchema = new StringSchema();
            sourceSchema.setExample("KS");
            sourceParameter.setSchema(sourceSchema);

            // 确保operation不为null
            if (operation == null) {
                operation = new Operation();
            }

            // 将参数添加到操作中
            if (operation.getParameters() == null) {
                operation.setParameters(Arrays.asList(signParameter, userIdParameter, tenantIdParameter, sourceParameter));
            } else {
                operation.getParameters().add(signParameter);
                operation.getParameters().add(userIdParameter);
                operation.getParameters().add(tenantIdParameter);
                operation.getParameters().add(sourceParameter);
            }
        }

        return operation;
    }
}