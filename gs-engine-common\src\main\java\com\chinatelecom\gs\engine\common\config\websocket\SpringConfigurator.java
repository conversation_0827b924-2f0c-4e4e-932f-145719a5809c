package com.chinatelecom.gs.engine.common.config.websocket;

import com.chinatelecom.gs.engine.common.context.RequestContext;
import jakarta.websocket.HandshakeResponse;
import jakarta.websocket.server.HandshakeRequest;
import jakarta.websocket.server.ServerEndpointConfig;
import org.springframework.web.context.WebApplicationContext;



public class SpringConfigurator extends ServerEndpointConfig.Configurator {

    private static WebApplicationContext webApplicationContext;

    public static void setWebApplicationContext(WebApplicationContext wac) {
        webApplicationContext = wac;
    }

    @Override
    public <T> T getEndpointInstance(Class<T> endpointClass) throws InstantiationException {
        return (T) webApplicationContext.getBean(endpointClass);
    }

    @Override
    public void modifyHandshake(ServerEndpointConfig sec, HandshakeRequest request, HandshakeResponse response) {
        super.modifyHandshake(sec, request, response);
        sec.getUserProperties().put("RequestInfo", RequestContext.get());

    }
}
