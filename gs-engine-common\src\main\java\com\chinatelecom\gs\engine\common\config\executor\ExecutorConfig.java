package com.chinatelecom.gs.engine.common.config.executor;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.utils.ThreadUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月06日
 */
@Configuration
@Slf4j
public class ExecutorConfig {

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    /**
     * 默认线程池
     * @return
     */
    @Bean("defaultPoolExecutor")
    public ExecutorService delayMqTaskThreadPool() {
        return ThreadUtils.newPool(gsGlobalConfig.getPool().getDefaultPool());
    }

    /**
     * 切分线程池
     * @return
     */
    @Bean("splitPoolExecutor")
    public ExecutorService splitPoolExecutor() {
        return ThreadUtils.newPriorityPool(gsGlobalConfig.getPool().getSplitPool());
    }

    /**
     * 切分线程池
     * @return
     */
    @Bean("splitImgPoolExecutor")
    public ExecutorService splitImgPoolExecutor() {
        return ThreadUtils.newPool(gsGlobalConfig.getPool().getSplitImgPool());
    }

    /**
     * 切分线程池
     * @return
     */
    @Bean("searchFlowPoolExecutor")
    public ExecutorService searchFlowPoolExecutor() {
        return ThreadUtils.newPool(gsGlobalConfig.getPool().getSearchFlowPool());
    }

    /**
     * 聊天窗解析文件的线程池
     * @return
     */
    @Bean("chatParserPoolExecutor")
    public ExecutorService chatParserPoolExecutor() {
        return ThreadUtils.newPool(gsGlobalConfig.getPool().getChatParserPool());
    }

    /**
     * 解析扩展流程线程池
     * @return
     */
    @Bean("extraFlowPoolExecutor")
    public ExecutorService extraFlowPoolExecutor() {
        return ThreadUtils.newPool(gsGlobalConfig.getPool().getExtraFlowPool());
    }

    @Bean("apiChatExecutorPool")
    public ExecutorService apiChatExecutorPool() {
        return ThreadUtils.newPool(gsGlobalConfig.getPool().getApiChatExecutorPool());
    }

    @Bean("newSseApiChatExecutorPool")
    public ExecutorService newSseApiChatExecutorPool() {
        return ThreadUtils.newPool(gsGlobalConfig.getPool().getApiChatExecutorPool());
    }

    @Bean("safeCheckThreadPool")
    public ExecutorService safeCheckThreadPool() {
        return ThreadUtils.newPool(gsGlobalConfig.getPool().getSafeCheckThreadPool());
    }

    @Bean("reportSseThreadPool")
    public ExecutorService reportSseThreadPool() {
        return ThreadUtils.newPool(gsGlobalConfig.getPool().getReportSseThreadPool());
    }

    /**
     * 抽取线程池
     * @return
     */
    @Bean("extractExecutorService")
    public ExecutorService extractExecutorService(){
        return ThreadUtils.newPool(gsGlobalConfig.getPool().getExtractPool());
    }

    /**
     * 公共线程池，大线程池
     * @return
     */
    @Bean("commonExecutorService")
    public ExecutorService commonExecutorService(){
        return ThreadUtils.newPool(gsGlobalConfig.getPool().getCommonPool());
    }

    /**
     * 大文件切分切分线程池
     * @return
     */
    @Bean("largeFileSplitPoolExecutor")
    public ExecutorService largeFileSplitPoolExecutor() {
        return ThreadUtils.newPriorityPool(gsGlobalConfig.getPool().getLargeFileSplitPool());
    }

}
