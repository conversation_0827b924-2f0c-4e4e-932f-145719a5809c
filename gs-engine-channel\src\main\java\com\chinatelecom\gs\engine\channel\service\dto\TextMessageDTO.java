package com.chinatelecom.gs.engine.channel.service.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 文本消息
 *
 * <AUTHOR>
 * @date 2024/1/16 14:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TextMessageDTO extends BaseSendMessageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -2376901709702290727L;

    private JSONObject text;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
