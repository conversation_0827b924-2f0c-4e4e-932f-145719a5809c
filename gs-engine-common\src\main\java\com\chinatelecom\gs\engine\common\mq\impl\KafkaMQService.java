package com.chinatelecom.gs.engine.common.mq.impl;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.mq.DelayExecutor;
import com.chinatelecom.gs.engine.common.mq.DelayTask;
import com.chinatelecom.gs.engine.common.mq.MQService;
import com.chinatelecom.gs.engine.common.utils.MqPrefixUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * MqService
 *
 * <AUTHOR>
 * @date 2023-05-17 13:52
 */
@Service
@Slf4j
public class KafkaMQService implements MQService {

    @Resource(name = "kafkaTemplate")
    private KafkaTemplate kafkaTemplate;

    @Autowired
    private DelayExecutor delayExecutor;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    private String buildTopic(String topic) {
        return MqPrefixUtils.buildByPrefix(topic);
    }


    @Override
    public void send(String topic, String key, String data) throws MessagingException {
        kafkaTemplate.send(buildTopic(topic), key, data);
    }


    @Override
    public void sendMessage(String topic, String data) throws MessagingException{
        String buildTopic = buildTopic(topic);
        kafkaTemplate.send(buildTopic, data);
        log.debug("kafka send topic:{}, message:{}", buildTopic, data);
    }

    /**
     * 使用sessionId作为key, 防止消息消费乱序
     * @param topic
     * @param sessionId
     * @param data
     * @throws MessagingException
     */
    @Override
    public void sendMessage(String topic, String sessionId, String data) throws MessagingException{
        String buildTopic = buildTopic(topic);
        kafkaTemplate.send(buildTopic, sessionId, data);
        log.debug("kafka send topic:{}, message:{}", buildTopic, data);
    }

    /**
     * todo 临时使用延迟队列支持延迟类消息, 在重启时可能存在一定概率的丢消息,后续优化
     *
     * @param topic
     *         the target destination
     * @param key
     *         the message to send
     * @param data
     * @param delayLevel
     *         delayLevel  seconds
     *
     * @throws MessagingException
     */
    @Override
    public void syncSendDelayTimeSeconds(String topic, String key, String data, int delayLevel)
            throws MessagingException{
        DelayTask delayTask = new DelayTask(delayLevel, ()->{
            log.info("发送延迟消息:{}", data);
            kafkaTemplate.send(topic, key, data);
            log.debug("kafka send topic:{},key:{}, message:{}", topic, key, data);
        });
        delayExecutor.execute(delayTask);
    }

    @Override
    public void syncSendDelayTimeSeconds(String topic, String data, int delayLevel) throws MessagingException{
        DelayTask delayTask = new DelayTask(delayLevel, ()->{
            log.info("发送延迟消息:{}", data);
            kafkaTemplate.send(topic, data);
            log.debug("kafka send topic:{}, message:{}", topic, data);
        });
        delayExecutor.execute(delayTask);
    }


}

