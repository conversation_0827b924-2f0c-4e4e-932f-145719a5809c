package com.chinatelecom.gs.engine.channel.openai.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
public class ChatCompletionResponseTest {
    private ChatCompletionResponse chatCompletionResponse;

    @BeforeEach
    public void setUp() {
        chatCompletionResponse = new ChatCompletionResponse();
    }



    @Test
    public void testSettersAndGetters() {
        // Arrange
        String id = "response123";
        long created = 1672531200L; // Example timestamp
        String model = "gpt-3.5";
        String systemFingerprint = "fingerprint123";
        ChatCompletionResponse.ChatCompletionChoice chatCompletionChoice = new ChatCompletionResponse.ChatCompletionChoice();
        chatCompletionChoice.setIndex(0);
        chatCompletionChoice.setMessage(new ChatMessage());
        chatCompletionChoice.setFinish_reason("stop");
        ChatCompletionResponse.ChatCompletionChoice chatCompletionChoice1 = new ChatCompletionResponse.ChatCompletionChoice();
        chatCompletionChoice1.setIndex(1);
        chatCompletionChoice1.setMessage(new ChatMessage());
        chatCompletionChoice1.setFinish_reason("length");
        List<ChatCompletionResponse.ChatCompletionChoice> choices = Arrays.asList(
                chatCompletionChoice, chatCompletionChoice1
        );
        ChatCompletionResponse.Usage usage = new ChatCompletionResponse.Usage();
        usage.setPrompt_tokens(10);
        usage.setCompletion_tokens(20);
        usage.setTotal_tokens(30);

        // Act
        chatCompletionResponse.setId(id);
        chatCompletionResponse.setCreated(created);
        chatCompletionResponse.setModel(model);
        chatCompletionResponse.setSystem_fingerprint(systemFingerprint);
        chatCompletionResponse.setChoices(choices);
        chatCompletionResponse.setUsage(usage);

        // Assert
        assertThat(chatCompletionResponse.getId()).isEqualTo(id);
        assertThat(chatCompletionResponse.getCreated()).isEqualTo(created);
        assertThat(chatCompletionResponse.getModel()).isEqualTo(model);
        assertThat(chatCompletionResponse.getSystem_fingerprint()).isEqualTo(systemFingerprint);
        assertThat(chatCompletionResponse.getChoices()).isEqualTo(choices);
        assertThat(chatCompletionResponse.getUsage()).isEqualTo(usage);
    }
}