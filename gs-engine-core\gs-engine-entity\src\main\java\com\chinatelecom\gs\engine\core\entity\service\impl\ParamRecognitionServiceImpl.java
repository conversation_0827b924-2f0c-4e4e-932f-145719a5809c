package com.chinatelecom.gs.engine.core.entity.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.chinatelecom.gs.engine.common.utils.VariableParseUtils;
import com.chinatelecom.gs.engine.core.entity.common.utils.ParallelUtils;
import com.chinatelecom.gs.engine.core.entity.service.ParamRecognitionService;
import com.chinatelecom.gs.engine.core.model.toolkit.StreamingChatLanguageModel;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.LLMRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.ModelProviderEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptTemplateVO;
import com.chinatelecom.gs.engine.kms.service.PromptTemplateAppService;
import com.chinatelecom.gs.engine.robot.sdk.dto.SimpleChatLog;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.ParamRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.ParamRecognitionResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.ParamValueVO;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 * 参数识别
 * @USER: pengmc1
 * @DATE: 2025/8/5 11:07
 */
@Slf4j
@Service
public class ParamRecognitionServiceImpl implements ParamRecognitionService {

    //回复内容识别prompt模板
    private static final String REPLY_RECOGNIZE_PROMPT_TEMPLATE = "REPLY_RECOGNIZE_PROMPT_TEMPLATE";
    //回复内容识别fc prompt模板
    private static final String REPLY_RECOGNIZE_FUNCTION_CALL_PROMPT_TEMPLATE = "REPLY_RECOGNIZE_FUNCTION_CALL_PROMPT_TEMPLATE";

    @Resource
    protected PromptTemplateAppService promptTemplateAppService;

    @Resource
    protected ModelServiceClient remoteServiceClient;

    @Resource
    protected StreamingChatLanguageModel streamingChatLanguageModel;

    @Resource
    private ExecutorService commonExecutorService;
    /**
     * 参数识别
     *
     * @param request
     * @return
     */
    @Override
    public ParamRecognitionResponse predict(ParamRecognitionRequest request) {
        log.info("进入参数识别，请求为：{}", JSON.toJSONString(request));
        List<ParamValueVO> paramValues = Lists.newArrayList();
        if (CollectionUtils.isEmpty(request.getParams())) {
            //需要识别的回复槽位列表为空，直接返回
            return null;
        }
        if(Boolean.TRUE.equals(request.getFunctionCall())){
            //基于大模型function call进行参数识别
            paramValues = doLLMFunctionRecognize(request);
        }else{
            //基于大模型提示词进行参数识别
            paramValues = doLLMPromptRecognize(request);
        }
        return new ParamRecognitionResponse(paramValues);
    }

    /**
     * 基于大模型function call进行参数识别
     * @param request
     * @return
     */
    protected List<ParamValueVO> doLLMFunctionRecognize(ParamRecognitionRequest request) {
        try {
            PromptTemplateVO promptTemplate = promptTemplateAppService.get(REPLY_RECOGNIZE_FUNCTION_CALL_PROMPT_TEMPLATE);
            if (Objects.isNull(promptTemplate)) {
                throw new BizException("BA010", "未找到参数识别FunctionCall prompt配置");
            }
            String query = request.getQuery();
            Map<String, Object> variableMap = Maps.newHashMap();
            variableMap.put("query", query); //用户query
            String llmPrompt = VariableParseUtils.parseVariable(promptTemplate.getContent(), variableMap);
            Tool tool = slotsToFunction(request);

            LLMRequest<?> llmRequest = buildLLMRequest(request.getModelCode(), llmPrompt, null, null, null, false);
            llmRequest.setTools(Collections.singletonList(tool));
            llmRequest.setHistories(convertHistories(request.getHistoryMessages()));
            WrapLLMMessage llmMessage = llmExecuteMessage(llmRequest);

            List<ParamValueVO> paramValues = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(llmMessage.getToolCalls())) {
                //槽位填满
                ToolCall tc = llmMessage.getToolCalls().get(0);
                com.chinatelecom.gs.engine.core.sdk.vo.llm.Function fc = tc.getFunction();
                JSONObject argsObj = new JSONObject();
                String args = fc.getArguments();
                if (StringUtils.isNotBlank(args)) {
                    argsObj = JSON.parseObject(args);
                }
                paramValues = jsonToSlotValue(argsObj);
            } else if (StringUtils.isNotBlank(llmMessage.getContent())) {
                JSONObject jsonObject = JSONObject.parseObject(llmMessage.getContent());
                JSONObject params = jsonObject.getJSONObject("params");
                paramValues = jsonToSlotValue(params);
            }
            return paramValues;
        } catch (Exception e) {
            log.error("LLM function recognize error: ", e);
        }
        return Collections.emptyList();

    }
    /**
     * 基于大模型提示词进行参数识别
     * @param request
     * @return
     */
    protected List<ParamValueVO> doLLMPromptRecognize(ParamRecognitionRequest request){
        return ParallelUtils.parallelExecute(commonExecutorService, request.getParams() , 120000L, param -> {
            //调用大模型进行回复识别
            ParamValueVO paramValue = doLlmReplyRecognize(request, param);
            //大模型校验通过
            if(Objects.nonNull(paramValue)){
                return Arrays.asList(paramValue);
            }
            return null;
        });
    }

    /**
     * 执行大模型回复内容识别， 大模型prompt,支持如下变量
     * {{guideAsk}}: 采集变量的引导话术
     * {{paramName}}: 参数名称
     * {{paramDesc}}: 参数描述
     * {{query}}: 用户query
     * @param request
     * @return
     */
    protected ParamValueVO doLlmReplyRecognize(ParamRecognitionRequest request, ParamRecognitionRequest.Param param) {
        PromptTemplateVO promptTemplate = promptTemplateAppService.get(REPLY_RECOGNIZE_PROMPT_TEMPLATE);
        if (Objects.isNull(promptTemplate)) {
            throw new BizException("BA010", "未找到采集回复识别大模型prompt配置");
        }
        Map<String, Object> variableMap = Maps.newHashMap();
        variableMap.put("question", param.getDescription());//引导话术
        variableMap.put("reply", request.getQuery()); //用户query
        String replyRecognizePrompt = VariableParseUtils.parseVariable(promptTemplate.getContent(), variableMap);
        String llmResult = llmExecute(request.getModelCode(), replyRecognizePrompt);
        if (StringUtils.isNotBlank(llmResult) && "是".equals(llmResult.trim())) {
            ParamValueVO paramValue = new ParamValueVO();
            paramValue.setName(param.getName());
            paramValue.setValue(request.getQuery());
            return paramValue;
        }
        return null;
    }

    /**
     * 解析大模型返回
     * @param jsonObject
     * @return
     */
    private List<ParamValueVO> jsonToSlotValue(JSONObject jsonObject) {
        List<ParamValueVO> paramValues = new ArrayList<>();
        for (String key : jsonObject.keySet()) {
            ParamValueVO paramValue = new ParamValueVO();
            paramValue.setName(key);
            paramValue.setValue(jsonObject.getString(key));
            paramValues.add(paramValue);
        }
        return paramValues;
    }

    /**
     * 将槽位转换为funciton
     * @param request
     * @return
     */
    private Tool slotsToFunction(ParamRecognitionRequest request) {
        Tool tool = new Tool();
        tool.setType("function");
        ToolFunction toolFunction = new ToolFunction();
        toolFunction.setName(request.getMessageId());
        toolFunction.setDescription(request.getMessageId());
        ToolFunctionParam toolParam = new ToolFunctionParam();
        toolParam.setType("object");
        Map<String, ToolFunctionParam> properties = new HashMap<>();
        for (ParamRecognitionRequest.Param param : request.getParams()) {
            ToolFunctionParam toolFunctionParam = new ToolFunctionParam();
            toolFunctionParam.setType("String");
            String desc = param.getDescription();
            toolFunctionParam.setDescription(desc);
            properties.put(param.getName(), toolFunctionParam);
        }
        toolParam.setProperties(properties);
        toolFunction.setParameters(toolParam);
        tool.setFunction(toolFunction);

        return tool;

    }
    /**
     * 构造大模型请求
     * @param modelCode
     * @param prompt
     * @param temperature
     * @param topP
     * @param maxTokens
     * @param stream
     * @return
     */
    protected LLMRequest buildLLMRequest(String modelCode, String prompt,Double temperature, Double topP, Integer maxTokens, Boolean stream){
        ModelPageListParam modelParam = remoteServiceClient.queryByModelCode(modelCode);
        if (modelParam == null) {
            log.error("未找到模型：{}", modelCode);
            return null;
        }
        LLMRequest llmRequest = new LLMRequest();
        llmRequest.getLlmModelInfo().setProvider(ModelProviderEnum.from(modelParam.getModelProvider()));
        llmRequest.getLlmModelInfo().setModelApi(modelParam.getApiKey());
        llmRequest.getLlmModelInfo().setModelSecret(modelParam.getModelSecret());
        llmRequest.getLlmModelInfo().setModelName(modelParam.getModelCallName());
        llmRequest.getLlmModelInfo().setModelUrl(modelParam.getExternalModelUrl());
        llmRequest.setStreaming(Boolean.TRUE.equals(stream) ? 1 : 0);
        llmRequest.setText(prompt);
        llmRequest.setEnableThinking(false);
        llmRequest.setUserId(IdUtil.fastSimpleUUID());
        if(Objects.nonNull(temperature)){
            llmRequest.setTemperature(temperature);
        }
        if(Objects.nonNull(topP)){
            llmRequest.setTopp(topP.floatValue());
        }
        if(Objects.nonNull(maxTokens)){
            llmRequest.setMaxTokens(maxTokens);
        }
        if(Objects.nonNull(modelParam.getExtraDataVO())){
            llmRequest.getLlmModelInfo().setMaxToken(modelParam.getExtraDataVO().getMaxInputToken());
            llmRequest.getLlmModelInfo().setTransformerType(modelParam.getExtraDataVO().getTransformerType());
            llmRequest.getLlmModelInfo().setNativeCall(modelParam.getExtraDataVO().getNativeCall());
            llmRequest.getLlmModelInfo().setNativeCallUrl(modelParam.getExtraDataVO().getNativeCallUrl());
        }
        return llmRequest;
    }

    protected WrapLLMMessage llmExecuteMessage(LLMRequest<?> llmRequest) {
        try {
            Response<WrapLLMMessage> llmResponse = streamingChatLanguageModel.syncGenerateMessage(llmRequest);
            return llmResponse.content();
        } catch (Exception e) {
            log.error("大模型请求异常", e);
        }
        return null;
    }
    /**
     * 大模型同步执行
     * @param modelCode
     * @param prompt
     * @return
     */
    protected String llmExecute(String modelCode, String prompt){
        try {
            LLMRequest llmRequest = buildLLMRequest(modelCode, prompt, null, null, null, false);
            Response<String> llmResponse = streamingChatLanguageModel.syncGenerate(llmRequest);
            return llmResponse.content();
        }catch (BizException be) {
            //业务异常，需要往上抛
            throw be;
        }catch (Exception e) {
            log.error("大模型请求异常", e);
        }
        return null;
    }

    /**
     * 转换历史消息
     * @param historyMessages
     * @return
     */
    protected List<WrapLLMMessage> convertHistories(List<SimpleChatLog> historyMessages){
        List<WrapLLMMessage> messages = Lists.newArrayList();
        if(CollectionUtils.isEmpty(historyMessages)){
            return messages;
        }
        for(SimpleChatLog chatLog : historyMessages){
            WrapLLMMessage message = new WrapLLMMessage();
            message.setContent(chatLog.getContent());
            message.setRole(chatLog.getRole());
            messages.add(message);
        }
        return messages;
    }
}
