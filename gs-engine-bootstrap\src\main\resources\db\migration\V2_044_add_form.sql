CREATE TABLE gs_form (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
    form_code VARCHAR(128) NOT NULL COMMENT '表单编码',
    form_name VARCHAR(128) DEFAULT NULL COMMENT '表单名称',
    form_desc VARCHAR(512) DEFAULT NULL COMMENT '表单描述',
    form_data MEDIUMTEXT DEFAULT NULL COMMENT '表单数据',
    status VARCHAR(64) DEFAULT 'UNPUBLISHED' COMMENT '表单状态',
    source VARCHAR(64) NOT NULL COMMENT '表单来源',
    version INT NOT NULL DEFAULT 1 COMMENT '表单版本',
    yn TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    tenant_id VARCHAR(128) NOT NULL COMMENT '租户id',
    create_id VARCHAR(100) NOT NULL DEFAULT '1' COMMENT '配置人员id',
    create_name VARCHAR(256) DEFAULT NULL COMMENT '配置人员名称',
    create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    update_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    update_id VARCHAR(100) DEFAULT NULL COMMENT '更新人员id',
    update_name VARCHAR(256) DEFAULT NULL COMMENT '更新人员名称',
    PRIMARY KEY (id),
    INDEX idx_form_code (form_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表单设计表';