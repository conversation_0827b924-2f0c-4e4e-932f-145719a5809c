package com.chinatelecom.gs.engine.common.s3;


import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.s3.impl.CephCloudDao;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import jakarta.annotation.Resource;

@Slf4j
@Configuration
public class CloudStorageConfig {

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Lazy
    @Bean
    public CloudStorageDao cephCloudDao() {
        GsGlobalConfig.S3Config s3 = gsGlobalConfig.getS3();
        CloudStorageDao result = null;
        switch (s3.getType()) {
            case CEPH:
            case MINIO:
            case LOCAL:
                result = new CephCloudDao();
                break;
            default:
                throw new BizException("A0003", "未配置存储类型");
        }
        return result;
    }


}
