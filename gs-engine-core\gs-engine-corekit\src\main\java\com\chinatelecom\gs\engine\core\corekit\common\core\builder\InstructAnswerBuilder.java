package com.chinatelecom.gs.engine.core.corekit.common.core.builder;

import com.chinatelecom.gs.engine.core.corekit.common.core.tts.TextSpeechService;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.ContentTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.Answer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.request.AnswerBuildRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.speech.Speech;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Objects;

@Component
public class InstructAnswerBuilder implements AnswerBuilder {

    @Resource
    private TextSpeechService textSpeechService;

    @Override
    public boolean valid(AnswerBuildRequest answerBuildRequest) {
        return answerBuildRequest.getAnswerTypeEnum().equals(AnswerTypeEnum.INSTRUCT);
    }

    @Override
    public Answer build(AnswerBuildRequest answerBuildRequest) {
        Answer answer = new Answer();
        answer.setAnswerType(AnswerTypeEnum.INSTRUCT);
        answer.setContentType(ContentTypeEnum.ALL.getCode());
        String content = Objects.isNull(answerBuildRequest.getContent()) ? "" : answerBuildRequest.getContent().toString();
        answer.setContent(content);
        Speech speech = this.textSpeechService.genSpeech(content);
        if(Objects.nonNull(speech)){
            speech.setEnableSmartInterruption(answerBuildRequest.getEnableSmartInterruption());
        }
        answer.setSpeech(speech);
        answer.setVersion(answer.getAnswerType().getVersion());
        answer.setNamespace("com.instruct");
        answer.setInstructions(answerBuildRequest.getInstructions());
        return answer;
    }
}
