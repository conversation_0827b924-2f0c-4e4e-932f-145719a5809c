package com.chinatelecom.gs.engine.common.platform;

import cn.hutool.core.text.CharSequenceUtil;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.log.track.DialogResultMessage;
import com.chinatelecom.gs.engine.common.log.track.LogMqProducer;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 使用 Spring AOP 对标记了 @StatOpenApi 的方法进行拦截，在方法执行前后埋点统计调用次数。
 * @date 2025年08月15日
 */
@Aspect
@Component
public class StatOpenApiStatisticsAspect {

    @Resource
    private LogMqProducer logMqProducer;

    @Around("@annotation(statOpenApi)")
    public Object statistics(ProceedingJoinPoint joinPoint, StatOpenApi statOpenApi) throws Throwable {
        if (CharSequenceUtil.isNotBlank(RequestContext.getEntry()) && !"openApi".equals(RequestContext.getEntry())) {
            return joinPoint.proceed();
        }
        String interfaceName = statOpenApi.groupName() + "-" + statOpenApi.name();
        DialogResultMessage dialogResultMessage = new DialogResultMessage();
        dialogResultMessage.setLogId(UUID.randomUUID().toString());
        dialogResultMessage.setSessionId(UUID.randomUUID().toString());
        dialogResultMessage.setMessageId(UUID.randomUUID().toString());
        dialogResultMessage.setTenantId(RequestContext.getTenantId());
        dialogResultMessage.setUserId(RequestContext.getUserId());
        dialogResultMessage.setEntry("api");
        dialogResultMessage.setBizName(interfaceName);
        dialogResultMessage.setInputToken(0);
        dialogResultMessage.setOutputToken(0);
        LocalDateTime start = LocalDateTime.now();
        dialogResultMessage.setStartTime(start);
        long startTimestampMillis = start.atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli();
        dialogResultMessage.setNoAnswer(false);
        dialogResultMessage.setIsCommon(false);
        Object proceed = joinPoint.proceed();
        LocalDateTime endTime = LocalDateTime.now();
        dialogResultMessage.setEndTime(endTime);
        dialogResultMessage.setSendTime(endTime);
        long endTimestampMillis = endTime.atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli();
        dialogResultMessage.setCostTime(endTimestampMillis - startTimestampMillis);
        logMqProducer.asyncSendDialogResultMessage(dialogResultMessage);
        return proceed;
    }
}