package com.chinatelecom.gs.engine.core.manager.param;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;

/**
 * 直接上传
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileManagerUploadParam {

    public FileManagerUploadParam(MultipartFile file) {
        this.file = file;
    }

    @NotNull(message = "文件不能为空")
    @Schema(description = "文件")
    private MultipartFile file;

    /**
     * 最大文件大小，单位MB(为空，使用默认配置 gsGlobalConfig.getKmsBizConfig().getFileSizeMaxLength())
     */
    @Schema(description = "文件大小限制", hidden = true)
    @JsonIgnore
    private transient Long maxFileSize;
}
