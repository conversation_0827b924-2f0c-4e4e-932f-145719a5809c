package com.chinatelecom.gs.engine.core.corekit.common.core.builder;

import com.chinatelecom.gs.engine.core.corekit.common.core.tts.TextSpeechService;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.ContentTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.Answer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.reason.Reason;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.request.AnswerBuildRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.speech.Speech;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Objects;

@Component
public class BICardAnswerBuilder implements AnswerBuilder {

    @Resource
    private TextSpeechService textSpeechService;

    @Override
    public boolean valid(AnswerBuildRequest answerBuildRequest) {
        return answerBuildRequest.getAnswerTypeEnum().equals(AnswerTypeEnum.NL2SQL);
    }

    @Override
    public Answer build(AnswerBuildRequest answerBuildRequest) {
        Answer answer = new Answer();
        answer.setAnswerType(AnswerTypeEnum.NL2SQL);
        answer.setContentType(ContentTypeEnum.PART.getCode());
        if (Objects.nonNull(answerBuildRequest.getContent())) {
            String content = answerBuildRequest.getContent().toString();
            answer.setContent(content);
            Speech speech = this.textSpeechService.genSpeech(content);
            if(Objects.nonNull(speech)){
                speech.setEnableSmartInterruption(answerBuildRequest.getEnableSmartInterruption());
            }
            answer.setSpeech(speech);
        }
        if (!StringUtils.isEmpty(answerBuildRequest.getReasoningContent())) {
            Reason reason = new Reason();
            reason.setReasoningContent(answerBuildRequest.getReasoningContent());
            answer.setReasoning(reason);
        }
        answer.setVersion(answer.getAnswerType().getVersion());
        answer.setNamespace("com.nl2sql.plugin");
        answer.setInstructions(answerBuildRequest.getInstructions());
        return answer;
    }
}
