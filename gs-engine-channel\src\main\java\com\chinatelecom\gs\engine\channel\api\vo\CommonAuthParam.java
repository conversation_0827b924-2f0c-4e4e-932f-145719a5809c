package com.chinatelecom.gs.engine.channel.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class CommonAuthParam implements Serializable {
    /**
     * 机器人编码
     */
    @Schema(description = "机器人编码")
    @NotBlank(message = "机器人编码不能为空！")
    private String robotCode;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @NotBlank(message = "用户唯一标识不能为空！")
    private String userId;


    /**
     * 请求时间
     */
    @Schema(description = "请求时间")
    @NotNull(message = "请求时间不能为空！")
    private Long requestTime;
    /**
     * 鉴权信息
     */
    private AuthInfo authInfo;

    @Schema(hidden = true)
    private String sign;
}
