package com.chinatelecom.gs.engine.core.corekit.controller.rpc;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.core.corekit.service.FormService;
import com.chinatelecom.gs.engine.core.sdk.request.FormDelRequest;
import com.chinatelecom.gs.engine.core.sdk.request.FormQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.request.FormSaveRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.FormVO;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * @USER: pengmc1
 * @DATE: 2025/7/21 11:16
 */

@Slf4j
@RestController
@RequestMapping(Apis.BASE_PREFIX + Apis.RPC_PREFIX + Apis.FORM)
public class FormRpcController{

    @Resource
    private FormService formService;

    /**
     * 保存表单
     *
     * @param request
     * @return
     */
    @PostMapping("/save")
    @AuditLog(businessType = "表单管理", operType = "表单新增/编辑", operDesc = "表单新增/编辑", objId="#request.formCode")
    public Result<Boolean> save(@RequestBody @Valid FormSaveRequest request) {
        return Result.success(formService.saveForm(request));
    }

    /**
     * 删除表单
     *
     * @param request
     * @return
     */
    @PostMapping("/delete")
    @AuditLog(businessType = "表单管理", operType = "表单删除", operDesc = "表单删除", objId="null")
    public Result<Boolean> delete(@RequestBody @Valid FormDelRequest request) {
        return Result.success(formService.deleteForm(request));
    }

    /**
     * 获取最新发布详情
     *
     * @param formCode
     * @param source
     * @return
     */
    @GetMapping("/getLastPublishDetail")
    @AuditLog(businessType = "表单管理", operType = "最新表单发布详情", operDesc = "最新表单发布详情", objId="#formCode")
    public Result<FormVO> getLastPublishDetail(@RequestParam("formCode") String formCode,@RequestParam("source") String source) {
        return Result.success(formService.getLastPublishDetail(formCode, source));
    }

    /**
     * 获取最新表单详情
     *
     * @param formCode
     * @param source
     * @return
     */
    @GetMapping("/getLastDetail")
    @AuditLog(businessType = "表单管理", operType = "最新表单详情", operDesc = "最新表单详情", objId="#formCode")
    public Result<FormVO> getLastDetail(@RequestParam("formCode") String formCode, @RequestParam("source") String source) {
        return Result.success(formService.getDetail(formCode, source));
    }

    /**
     * 根据版本获取表单详情
     *
     * @param formCode
     * @param source
     * @param version
     * @return
     */
    @GetMapping("/getDetailByVersion")
    @AuditLog(businessType = "表单管理", operType = "查询表单版本详情", operDesc = "查询表单版本详情", objId="#formCode")
    public Result<FormVO> getDetailByVersion(@RequestParam("formCode") String formCode,@RequestParam("source") String source,@RequestParam("version") Long version) {
        return Result.success(formService.getDetailByVersion(formCode, source, version));
    }

    /**
     * 根据表单编码查询表单列表
     *
     * @param request
     * @return
     */
    @PostMapping("/queryFormList")
    @AuditLog(businessType = "表单管理", operType = "查询表单列表", operDesc = "查询表单列表", objId="null")
    public Result<List<FormVO>> queryFormList(@RequestBody @Valid FormQueryRequest request) {
        return Result.success(formService.queryFormList(request));
    }
}
