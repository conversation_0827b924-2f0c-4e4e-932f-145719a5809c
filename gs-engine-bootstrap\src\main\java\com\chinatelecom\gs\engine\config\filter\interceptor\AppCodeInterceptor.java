package com.chinatelecom.gs.engine.config.filter.interceptor;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.config.filter.BaseHandlerInterceptor;
import com.chinatelecom.gs.engine.kms.model.dto.AppRoleDTO;
import com.chinatelecom.gs.engine.kms.repository.AppRoleRepository;
import com.chinatelecom.gs.engine.kms.sdk.vo.app.AppVO;
import com.chinatelecom.gs.engine.kms.service.AppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
@Component
@Slf4j
public class AppCodeInterceptor implements BaseHandlerInterceptor {

    @Resource
    private AppService appService;

    @Resource
    private AppRoleRepository appRoleRepository;

    public boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        String appCode = RequestContext.getAppCode();
        if (appCode == null) {
            InterceptorUtils.writeError((HttpServletResponse) response, "A0016", "AppCode不能为空");
            return false;
        }

        AppVO app = appService.get(appCode);
        if (app == null) {
            InterceptorUtils.writeError((HttpServletResponse) response, "A0017", "AppCode不存在");
            return false;
        }

        List<AppRoleDTO> appRole = appRoleRepository.findRoleByAppCode(appCode, RequestContext.getTenantId(), RequestContext.getUserId(), RequestContext.getTeamCodes());
        if (CollectionUtils.isEmpty(appRole)) {
            log.error("用户无该应用权限：{}", JsonUtils.toJsonString(RequestContext.get()));
            InterceptorUtils.writeError((HttpServletResponse) response, "A0018", "用户无该应用权限");
            return false;
        }

        return true;
    }


}
