package com.chinatelecom.gs.engine.core.entity.ability.recognition;

import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @USER: pengmc1
 * @DATE: 2025/1/17 18:39
 */

@Component
public class EntityRecognitionAbilityHolder {

    @Autowired
    private List<EntityRecognitionAbilityService> entityRecognitionAbilityServices;

    private Map<String, EntityRecognitionAbilityService> instructServiceMap = new HashMap<>();

    @PostConstruct
    private void init(){
        if(!CollectionUtils.isEmpty(entityRecognitionAbilityServices)){
            for(EntityRecognitionAbilityService entityRecognitionAbilityService : entityRecognitionAbilityServices){
                instructServiceMap.put(entityRecognitionAbilityService.abilityCode(), entityRecognitionAbilityService);
            }
        }
    }

    /**
     * 根据指令编码获取指令服务
     * @param abilityCode
     * @return
     */
    public EntityRecognitionAbilityService getAbilityService(String abilityCode){
        if(!instructServiceMap.containsKey(abilityCode)){
            throw new BizException("未找到实体识别能力处理器" + abilityCode);
        }
        return instructServiceMap.get(abilityCode);
    }
}
