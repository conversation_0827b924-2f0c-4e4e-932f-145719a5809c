package com.chinatelecom.gs.engine.robot.dialog.execute.service.llm;

import cn.hutool.core.text.CharSequenceUtil;
import com.chinatelecom.gs.engine.common.enums.EventTypeEnum;
import com.chinatelecom.gs.engine.common.utils.RepeatedSubstringUtils;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.model.toolkit.handler.CommonStreamMessageHandler;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.SafeCheckAssistService;
import com.chinatelecom.gs.engine.core.sdk.enums.LLMMessageBizCode;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.core.sdk.vo.msg.MessageCallback;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.AgentStrategyConfig;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.config.LlmTextDuplicateCheckerConfig;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.secure.AgentSecureCheck;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.ContentTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.SystemIntentEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntentAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.ToolMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.AnswerResponseType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.ToolAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.*;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.reason.Reason;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.DialogMessageTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import retrofit2.Call;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @since 2025/2/18 20:46
 */
@Slf4j
public class LLMStreamCallbackHandler extends CommonStreamMessageHandler<MessageCallback<ToolMessageResponse>> {

    private static final String ACTION_CODE_FORMAT = "%s_%s";

    private static final ActionTypeEnum ACTION_TYPE= ActionTypeEnum.LLM_GENERATION;

    private final AgentSecureCheck agentSecureCheck;

    private final SafeCheckAssistService safeCheckAssistService;

    private final AgentStrategyConfig actionRequest;

    private final StringBuilder fenceStringBuilder = new StringBuilder();

    private StringBuilder reasonStringBuilder = new StringBuilder();

    private IActionCallBackService<ActionMessage> actionCallBackService;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    @Getter
    private ToolMessageResponse messageResponse;

    /**
     * 0 正常
     * 1 用户中断
     * 2 敏感词中断
     */
    @Getter
    private Integer interrupt = 0;

    private Boolean cachedPre = false;

    public LLMStreamCallbackHandler(CountDownLatch latch, String messageId, MessageCallback<ToolMessageResponse> callback, IActionCallBackService<ActionMessage> actionCallBackService,
                                    AgentSecureCheck agentSecureCheck, SafeCheckAssistService safeCheckAssistService, AgentStrategyConfig actionRequest) {
        super(latch, messageId, callback);
        this.agentSecureCheck = agentSecureCheck;
        this.actionRequest = actionRequest;
        this.safeCheckAssistService = safeCheckAssistService;
        this.actionCallBackService = actionCallBackService;
    }

    public void start(String content) {
        MessageCallback<ToolMessageResponse> callback = getCallback();
        if (Objects.isNull(callback)) {
            log.error("callback is null");
            return;
        }
        try {
            callback.lock();
            startTime = LocalDateTime.now();
            if (actionCallBackService != null) {
                actionCallBackService.onStart(buildStartActions());
            }
            //callBackProgress(content);
        } catch (Exception e) {
            log.error("处理过程消息失败", e);
        } finally {
            callback.unlock();
        }
    }

    @Override
    public void onNext(Call<ResponseBody> call, WrapLLMMessage token) {
        if (Objects.isNull(this.getCallback())) {
            return;
        }

        if (getCallback().isLock()) {
            if (StringUtils.isNotBlank(token.getContent())) {
                fenceStringBuilder.append(token.getContent());
            }
            if (StringUtils.isNotBlank(token.getReasoning_content())) {
                reasonStringBuilder.append(token.getReasoning_content());
            }
            cachedPre = true;
        } else {
            if (cachedPre) {
                if (reasonStringBuilder.length() > 0) {
                    token.setReasoning_content(reasonStringBuilder + token.getReasoning_content());
                }

                if (fenceStringBuilder.length() > 0) {
                    token.setContent(fenceStringBuilder + token.getContent());
                }
                cachedPre = false;
            }


            ToolMessageResponse response = buildToolMessage(token);

            String callbackResult = null;
            if (interrupt == 0) {
                callbackResult = this.getCallback().invoke(response);
            }

            if (Objects.equals(callbackResult, EventTypeEnum.CANCEL.name())) {
                interrupt = 1;
            } else if (Objects.nonNull(response.getToolAnswer())
                    && response.getToolAnswer().getToolIntentId().equalsIgnoreCase(SystemIntentEnum.SENSITIVE.getCode())) {
                interrupt = 2;
                messageResponse = response;
            }
            else if (Objects.nonNull(response.getToolAnswer())
                    && response.getToolAnswer().getToolIntentId().equalsIgnoreCase(SystemIntentEnum.DUPLICATED.getCode())) {
                interrupt = 3;
                messageResponse = response;
            }

            if (interrupt != 0) {
                log.warn("用户请求停止流输出或命中敏感词 status {}, 中断连接", interrupt);
                if (call != null) {
                    call.cancel();
                }
            }
        }
    }

    @Override
    public void onComplete(Response<WrapLLMMessage> response) {
        this.setResult(new AtomicReference<>(response.content()));
        log.info("最终结果 {}", this.getResult());
        endTime = LocalDateTime.now();
        if (actionCallBackService != null) {
            actionCallBackService.onComplete(buildCompleteActions());
        }

        resumeEngine();
    }

    @Override
    public void onError(Throwable error, Response<WrapLLMMessage> response) {
        if (Objects.nonNull(response)) {
            this.setResult(new AtomicReference<>(response.content()));
        }
        if (interrupt == 0) {
            log.error("调用大模型失败 {}", response, error);
        } else {
            log.warn("中断，调用大模型失败 {}", response, error);
        }
        resumeEngine();
    }

    private ToolMessageResponse buildToolMessage(WrapLLMMessage token) {

        ToolAnswer toolAnswer = new ToolAnswer();
        toolAnswer.setAnswerType(AnswerTypeEnum.MARKDOWN);
        toolAnswer.setContent(token.getContent());
        toolAnswer.setResponseType(AnswerResponseType.LLM);
        toolAnswer.setMessageId(this.getMessageId());
        if (StringUtils.isNotBlank(token.getReasoning_content())) {
            Reason reason = new Reason();
            reason.setReasoningContent(token.getReasoning_content());
            reasonStringBuilder.append(token.getReasoning_content());
            toolAnswer.setReasoning(reason);
        }

        ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
        toolIntentAnswer.setToolIntentId(SystemIntentEnum.LLM.getCode());
        toolIntentAnswer.setDialogEngineType(DialogEngineType.LLM);
        toolIntentAnswer.setToolAnswer(toolAnswer);

        ToolMessageResponse toolMessageResponse = null;

        if(LLMMessageBizCode.SENSITIVE.getCode().equalsIgnoreCase(token.getCode())){
            toolAnswer.setContentType(ContentTypeEnum.ALL.getCode());
            toolIntentAnswer.setToolIntentId(SystemIntentEnum.SENSITIVE.getCode());
            toolMessageResponse = ToolMessageResponse.builder()
                    .sessionId(actionRequest.getHeader().getTrack().getSessionId())
                    .upMsgId(actionRequest.getHeader().getTrack().getMessageId())
                    .downMsgId(actionRequest.getHeader().getTrack().getDownMessageId())
                    .toolAnswer(toolIntentAnswer)
                    .messageType(DialogMessageTypeEnum.PART)
                    .eventType(SendMessageTypeEnum.COVER)
                    .build();
            return toolMessageResponse;
        }else{
            toolMessageResponse = ToolMessageResponse.builder()
                    .toolAnswer(toolIntentAnswer)
                    .downMsgId(actionRequest.getHeader().getTrack().getDownMessageId())
                    .sessionId(actionRequest.getHeader().getTrack().getSessionId())
                    .userId(actionRequest.getHeader().getUser().getUserId())
                    .eventType(SendMessageTypeEnum.ADD)
                    .messageType(DialogMessageTypeEnum.PART)
                    .upMsgId(actionRequest.getHeader().getTrack().getMessageId()).build();
        }

        if (StringUtils.isNotBlank(token.getContent()) && !Objects.equals("null", token.getContent())) {
            fenceStringBuilder.append(token.getContent());
        }

        LlmTextDuplicateCheckerConfig duplicateCheckerConfig = SpringContextUtils.getBean(LlmTextDuplicateCheckerConfig.class);

        Integer minCount = duplicateCheckerConfig.getMinCount();
        if (RepeatedSubstringUtils.hasDuplicateSubstring(fenceStringBuilder.toString(), minCount) || RepeatedSubstringUtils.hasDuplicateSubstring(reasonStringBuilder.toString(), minCount)) {
            log.info("【llm】 命中重复检测  {} 内容 {}, {}", minCount, fenceStringBuilder.toString(), reasonStringBuilder.toString());
            toolAnswer.setAnswerType(AnswerTypeEnum.MARKDOWN);
            Reason reason = new Reason();
            reason.setReasoningContent("检测到模型出现异常，尝试重新生成答案。\n");
            toolAnswer.setReasoning(reason);
            toolAnswer.setResponseType(AnswerResponseType.LLM);
            toolAnswer.setContent(CharSequenceUtil.EMPTY);
            toolAnswer.setContentType(ContentTypeEnum.ALL.getCode());
            // 触发安全围栏，将意图设置为敏感词
            toolIntentAnswer.setToolIntentId(SystemIntentEnum.DUPLICATED.getCode());
            // 将页面已生成的答案覆盖为安全围栏兜底话术
            toolMessageResponse.setEventType(SendMessageTypeEnum.COVER);
        }
        return toolMessageResponse;
    }

    private ActionMessage buildStartActions() {
        Action action = Action.builder()
                .type(ACTION_TYPE.getCode())
                .code(getActionCode())
                .name(ACTION_TYPE.getDesc())
                .state(ActionStateEnum.START.getCode())
                .build();
        return ActionMessage.builder().actions(Lists.newArrayList(action)).messageId(getMessageId()).build();
    }

    private ActionMessage buildCompleteActions() {
        Action action = Action.builder()
                .type(ACTION_TYPE.getCode())
                .code(getActionCode())
                .name(ACTION_TYPE.getDesc())
                .state(ActionStateEnum.FINISH.getCode())
                .cost(getCost())
                .build();
        return ActionMessage.builder().actions(Lists.newArrayList(action)).messageId(getMessageId()).build();
    }

    private ActionMessage buildErrorActions() {
        Action action = Action.builder()
                .type(ACTION_TYPE.getCode())
                .code(getActionCode())
                .name(ACTION_TYPE.getDesc())
                .state(ActionStateEnum.ERROR.getCode())
                .cost(getCost())
                .build();
        return ActionMessage.builder().actions(Lists.newArrayList(action)).messageId(getMessageId()).build();
    }

    private String getActionCode() {
        return ACTION_CODE_FORMAT.formatted(actionRequest.getHeader().getTrack().getDownMessageId(), ACTION_TYPE.getCode());
    }

    private String getCost() {
        if (startTime == null || endTime == null) {
            return StringUtils.EMPTY;
        }
        return "%.3f".formatted(Duration.between(startTime, endTime).toNanos() / 1e9);
    }
}
