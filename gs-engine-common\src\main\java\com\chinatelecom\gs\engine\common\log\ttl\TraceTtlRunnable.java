package com.chinatelecom.gs.engine.common.log.ttl;

import com.alibaba.ttl.TtlRunnable;
import com.chinatelecom.gs.engine.common.log.utils.MDCTraceUtils;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class TraceTtlRunnable implements Runnable {

    private final TtlRunnable delegate;

    @Override
    public void run() {
        delegate.run();
    }

    public static TraceTtlRunnable get(Runnable runnable) {
        return get(runnable, false);
    }

    public static TraceTtlRunnable get(Runnable runnable, boolean releaseTtlValueReferenceAfterRun) {
        if (runnable == null) {
            return null;
        } else if (runnable instanceof TraceTtlRunnable ttlRunnable1) {
            return ttlRunnable1;
        } else if (runnable instanceof TtlRunnable ttlRunnable) {
            runnable = ttlRunnable.getRunnable();
        }
        Runnable finalRunnable = runnable;
        return new TraceTtlRunnable(TtlRunnable.get(() -> {
            MDCTraceUtils.putTrace(MDCTraceUtils.getTraceId(), MDCTraceUtils.getSpanId());
            finalRunnable.run();
        }, releaseTtlValueReferenceAfterRun));
    }
}
