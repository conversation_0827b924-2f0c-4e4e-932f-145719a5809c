package com.chinatelecom.gs.engine.core.manager.vo.config.base;

import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;

@Data
public class BaseConfigCreateParam {

    /**
     * 业务配置名称
     */
    private String name;

    /**
     * 配置维度
     */
    private DimensionEnum dimension;
    /**
     * 业务配置场景
     */
    @NotEmpty(message = "配置类型不能为空")
    private String configType;

    /**
     * 业务配置场景说明
     */
    private String description;

    /**
     * 业务场景配置唯一标识
     */
    @NotEmpty(message = "业务编码不能为空")
    private String businessNo;

    /**
     * 配置json格式value值
     */
    @NotEmpty(message = "配置内容不能为空")
    private String configData;

    /**
     * 配置json的class对象
     */
    private String objClass;
}
