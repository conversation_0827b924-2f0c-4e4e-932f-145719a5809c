# 本地启动自测使用配置
app:
  db:
    kms:
      host: localhost
      port: 3306
      username: root
      password: ${DB_PASSWORD:15inyLC9lRxjByr}
      dbname: telecom-ai-gs-engine
  redis:
    username: default
    password: ${REDIS_PASSWORD:}
    host: localhost
    port: 6379
  kafka:
    servers: 127.0.0.1:9092



telecom:
  ai:
    search:
      discovery-type: simple
      app: # 自动创建应用及关联es集群的配置
        auto-create: true
        name: 知识中台
        cluster-name: ES测试集群
        cluster-address: ""
        cluster-password: ""
        cluster-username: ""
        s3-access-key: ${gs.s3.accessKey}
        s3-secret-key: ${gs.s3.secretKey}
        s3-endpoint: ${gs.s3.endPoint}
        s3-bucket-name: ${gs.s3.bucketName}
      user-id: gs-ks
      simple: # simple类型，需手动指定搜索服务地址
        address: "*************:31260"
      enabled: false


gs:
  system:
    env: dev  # 环境隔离前缀，生产使用prod
    logEnabled: true # 是否打印日志
  s3:
    type: LOCAL
  cache:
    type: REDIS
  mockRequest:
    enabled: true
  searchTemplate:
    doc: "{\"query\":{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"match\":{\"fileContent\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\"}}}]}}]}},\"knn\":{\"field\":\"fileContentVector\",\"query\":\"${query?json_string}\",\"k\":100,\"num_candidates\":1000},\"highlightFields\":[],\"page\":1,\"size\":50,\"knnMinScore\":0.65,\"firstRankExpression\":\"atan_normalized_bm25(0.01)*0.5+knn_score()*0.5\",\"type\":\"VECTOR\"}"
    faq: "{\"query\":{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"match\":{\"fileContent\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\",\"boost\":2.0}}}]}}],\"should\":[{\"match_phrase\":{\"fileContent\":{\"query\":\"${query?json_string}\",\"boost\":2.0}}},{\"match_phrase\":{\"title\":{\"query\":\"${query?json_string}\",\"boost\":2.0}}}]}},\"knn\":{\"field\":\"fileContentVector\",\"query\":\"${query?json_string}\",\"k\":100,\"num_candidates\":1000},\"knnMinScore\":0.75,\"firstRankExpression\":\"atan_normalized_bm25(0.1)*0.5+knn_score()*0.5\",\"type\":\"VECTOR\",\"page\":1,\"size\":50}"
    filter: "{\"distinct\":{\"distField\":\"knowledgeCode\",\"distTimes\":1,\"distCount\":1,\"collectFields\":[\"knowledgeCode\",\"file\",\"fileOriginalContent\",\"fileContent\",\"fileSource\",\"type\"],\"collectCount\":3},\"type\":\"TRADITION\",\"page\":1,\"size\":50}"
    search_doc: "{\"query\":{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"match\":{\"fileContent\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"fileContentSpell\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\"}}}]}}]}},\"page\":1,\"size\":50,\"type\":\"TRADITION\"}"
    search_faq: "{\"query\":{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"match\":{\"fileContent\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\",\"boost\":2.0}}}]}}],\"should\":[{\"match_phrase\":{\"fileContent\":{\"query\":\"${query?json_string}\",\"boost\":2.0}}},{\"match_phrase\":{\"title\":{\"query\":\"${query?json_string}\",\"boost\":2.0}}}]}},\"type\":\"TRADITION\",\"page\":1,\"size\":50}"
    search_filter: "{\"distinct\":{\"distField\":\"knowledgeCode\",\"distTimes\":1,\"distCount\":1,\"collectFields\":[\"knowledgeCode\",\"file\",\"fileOriginalContent\",\"fileContent\",\"fileSource\",\"type\"],\"collectCount\":3},\"type\":\"TRADITION\",\"page\":1,\"size\":50}"

