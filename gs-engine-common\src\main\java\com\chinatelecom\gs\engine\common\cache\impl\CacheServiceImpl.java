package com.chinatelecom.gs.engine.common.cache.impl;

import com.chinatelecom.gs.engine.common.cache.CacheService;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 一些缓存的封装操作
 *
 * <AUTHOR>
 * @date 2023-04-14 15:17
 */
@Component
public class CacheServiceImpl implements CacheService {

    @Resource
    @Lazy
    private CacheManager cacheManager;

    @Override
    public void put(String cacheName, String cacheKey, Object cacheValue) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.put(cacheKey, cacheValue);
        }
    }

    @Override
    public <T> T get(String cacheName, String cacheKey, Class<T> type) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            return cache.get(cacheKey, type);
        } else {
            return null;
        }
    }

    @Override
    public void evict(String cacheName, String cacheKey) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.evict(cacheKey);
        }
    }

    @Override
    public void evictIfPresent(String cacheName, String cacheKey) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.evictIfPresent(cacheKey);
        }
    }

    @Override
    public void clear(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
        }
    }

    @Override
    public void invalidate(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.invalidate();
        }
    }
}
