package com.chinatelecom.gs.engine.channel.service.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/22 15:44
 * @description
 */
@Data
public class QywxConfigDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 7846116764745294068L;

    /**
     * 企业微信token
     */
    private String token;

    /**
     * 企业微信corpId
     */
    private String corpId;

    /**
     * aes key
     */
    private String encodingAESKey;

    /**
     * 应用ID
     */
    private Integer agentId;

    /**
     * 应用secret
     */
    private String secret;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 用户名称
     */
    private String updateName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 渠道对应的机器人code
     */
    private String agentCode;
}
