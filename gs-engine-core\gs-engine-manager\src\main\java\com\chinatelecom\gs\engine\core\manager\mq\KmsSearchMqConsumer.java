package com.chinatelecom.gs.engine.core.manager.mq;

import com.chinatelecom.gs.engine.common.log.track.LogTopicConstants;
import com.chinatelecom.gs.engine.common.mq.KmsSearchMessage;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.manager.service.LogAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Objects;

/**
 * 知识搜索消息消费
 *
 * @USER: pengmc1
 * @DATE: 2025/5/21 14:46
 */

@Component
@Slf4j
public class KmsSearchMqConsumer {

    @Resource
    private LogAppService logAppService;

    @KafkaListener(topics = "${gs.system.mqPrefix:}" + "${gs.system.env:}" + LogTopicConstants.KMS_SEARCH_TOPIC, groupId = "${gs.system.mqPrefix:}" + "${gs.system.env:}" + LogTopicConstants.DEFAULT_CONSUMER_GROUP)
    protected void consumerMessage(String message) {
        log.debug("知识搜索埋点数据：{}", message);
        KmsSearchMessage kmsSearchMessage = JsonUtils.parseObject(message, KmsSearchMessage.class);
        if (Objects.isNull(kmsSearchMessage)) {
            log.info("知识搜索埋点数据解析为空");
            return;
        }
        try {
            //埋点数据写入到ES
            logAppService.saveSearchKmsLog(kmsSearchMessage);
        } catch (Exception e) {
            log.error("知识搜索埋点数据发生异常，数据为：{}", message, e);
        }
    }
}
