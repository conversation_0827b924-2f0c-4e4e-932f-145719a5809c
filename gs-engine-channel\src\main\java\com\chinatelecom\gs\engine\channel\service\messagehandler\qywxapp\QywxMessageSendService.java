package com.chinatelecom.gs.engine.channel.service.messagehandler.qywxapp;

import com.blade.kit.http.HttpRequest;
import com.chinatelecom.gs.engine.channel.common.AccessTokenUtil;
import com.chinatelecom.gs.engine.channel.common.SessionIdHolder;
import com.chinatelecom.gs.engine.channel.common.UidUtils;
import com.chinatelecom.gs.engine.channel.common.cache.localcache.ChannelCaches;
import com.chinatelecom.gs.engine.channel.common.enums.MessageDirectionEnum;
import com.chinatelecom.gs.engine.channel.common.enums.QywxMessageTypeEnum;
import com.chinatelecom.gs.engine.channel.common.utils.HttpUtils;
import com.chinatelecom.gs.engine.channel.foundation.BotPlatformDialog;
import com.chinatelecom.gs.engine.channel.foundation.platformanswer.PlatformAnswerTransformer;
import com.chinatelecom.gs.engine.channel.service.dto.BaseSendMessageDTO;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelMsgRecordDTO;
import com.chinatelecom.gs.engine.channel.service.dto.RobotConfigDTO;
import com.chinatelecom.gs.engine.channel.service.messagehandler.MessageRecordService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.BotAnswer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/18 15:38
 */
@Service
@Slf4j
public class QywxMessageSendService {

    @Resource
    private ChannelCaches caches;

    @Resource
    private BotPlatformDialog botPlatformDialog;

    @Resource
    private SessionIdHolder sessionIdHolder;

    @Resource
    private MessageRecordService messageRecordService;

    @Resource
    private AccessTokenUtil accessTokenUtil;


    @Resource
    private PlatformAnswerTransformer platformAnswerTransformer;

    @Value("${weixin.send.message.url:}")
    private String weixinSendMessageUrl;

    private static final Integer TIME_OUT = 3000;

    @Async
    public void asyncReply(String channelId, String userId, String content, Integer agentId, String corpId) {
        RobotConfigDTO robotConfig = caches.getRobotConfig(channelId);
        try {
            String sessionId = sessionIdHolder.getSessionId(channelId, userId);
            String messageId = UidUtils.randomString();
            ChannelMsgRecordDTO userRecordDTO = ChannelMsgRecordDTO.builder()
                    .channelId(channelId)
                    .robotCode(robotConfig.getBotCode())
                    .sessionId(sessionId)
                    .userId(userId)
                    .messageId(messageId)
                    .message(content)
                    .messageType(QywxMessageTypeEnum.TEXT.getCode())
                    .msgDirection(MessageDirectionEnum.USER)
                    .build();
            userRecordDTO.setTenantId(robotConfig.getTenantId());
            messageRecordService.recordMessage(userRecordDTO);

            List<BotAnswer> platformAnswers = this.botPlatformDialog.chat(userId,
                    sessionId, messageId,
                    content, robotConfig);
            List<BaseSendMessageDTO> messages = this.platformAnswerTransformer.convertCsrobotAnswer(platformAnswers, userId, agentId, channelId, corpId);
            for (BaseSendMessageDTO messageDTO : messages) {
                pushMessage(messageDTO, channelId);
                ChannelMsgRecordDTO botRecordDTO = ChannelMsgRecordDTO.builder()
                        .channelId(channelId)
                        .robotCode(robotConfig.getBotCode())
                        .sessionId(sessionId)
                        .userId(userId)
                        .messageId(messageId)
                        .message(messageDTO.toString())
                        .messageType(messageDTO.getMsgtype())
                        .msgDirection(MessageDirectionEnum.BOT)
                        .build();
                botRecordDTO.setTenantId(robotConfig.getTenantId());
                messageRecordService.recordMessage(botRecordDTO);
                //访问微信展示顺序乱,休眠1秒
                Thread.sleep(1000);
            }


        } catch (InterruptedException e) {
            log.error("企业微信应用消息聊天失败 {}", e.getMessage());
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 发送消息
     */
    void pushMessage(BaseSendMessageDTO message, String channelId) {
        String accessToken = accessTokenUtil.getAccessToken(channelId);
        if (StringUtils.isEmpty(accessToken)) {
            log.warn("推送企微消息获取token为空");
            return;
        }
        try {
            HttpRequest request = null;
            String body = message.toString();
            log.info("推送企微消息body:{}", body);
            String url = weixinSendMessageUrl + "?access_token=" + accessToken;
            request = HttpUtils.post(url, TIME_OUT, "application/json");
            request.send(body);
            String queryRes = request.body();
            request.disconnect();
            log.info("推送企微消息返回:{}", queryRes);
        } catch (Exception e) {
            log.error("推送企微消息报错", e);
        }
    }
}
