package com.chinatelecom.gs.engine.channel.dao.mapper;

import com.chinatelecom.gs.engine.channel.dao.po.ChannelConfigPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


public class ChannelConfigMapperTest {
    @Mock
    private ChannelConfigMapper channelConfigMapper;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void testSelectById() {
        // Arrange
        long id = 1L;
        ChannelConfigPO expectedPo = new ChannelConfigPO();
        expectedPo.setId(id);
        expectedPo.setConfigKey("config1");
        expectedPo.setConfigValue("value1");

        when(channelConfigMapper.selectById(id)).thenReturn(expectedPo);

        // Act
        ChannelConfigPO actualPo = channelConfigMapper.selectById(id);

        // Assert
        assertNotNull(actualPo);
        assertEquals(expectedPo.getId(), actualPo.getId());
        assertEquals(expectedPo.getConfigKey(), actualPo.getConfigKey());
        assertEquals(expectedPo.getConfigValue(), actualPo.getConfigValue());
        verify(channelConfigMapper).selectById(id);
    }

}