package com.chinatelecom.gs.engine.channel.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 保存会话反馈信息
 * @date 2024年04月07日
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Schema
public class SaveSessionFeedBackReq extends CommonAuthParam implements Serializable {

    /**
     * 会话id
     */
    @Schema(description = "会话id", required = true)
    @NotBlank(message = "会话id不能为空")
    private String sessionId;

    /**
     * //     * 评价分数（0-5）
     * //
     */
    @Schema(description = "评价分数（0-5）", required = true)
    @NotNull(message = "评价分数不能为空")
    @Min(value = 1, message = "评价分数为1-5颗星")
    @Max(value = 5, message = "评价分数为1-5颗星")
    private Integer evaluationScore;

    /**
     * 描述
     */
    @Schema(description = "描述", required = true)
    private String userDescribe;

    /**
     * 联系方式
     */
    @Schema(description = "联系方式")
    private String contactInformation;

    /**
     * 是否提供截图
     */
    @Schema(description = "是否提供截图")
    private Boolean provideScreenshots = Boolean.TRUE;

    /**
     * 截图信息
     */
    @Schema(description = "截图信息")
    private List<String> screenshots;
}
