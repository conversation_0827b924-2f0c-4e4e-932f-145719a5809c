# API文档选择性参数暴露功能

## 功能概述

本功能允许您通过注解的方式控制不同API类型（Web、OpenAPI、RPC）中参数和接口的可见性。

- **Web端接口**: 暴露所有参数和接口
- **OpenAPI接口**: 选择性暴露参数和接口
- **RPC接口**: 选择性暴露参数和接口

## 快速开始

### 1. 引入注解

```java
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
```

### 2. 在字段上使用注解

```java
// 多个API类型隐藏
@Schema(description = "用户密码")
@HideFromApiTypes({ApiType.OPENAPI, ApiType.RPC})  // 在OpenAPI和RPC文档中隐藏
private String password;

// 单个API类型隐藏（简化写法）
@Schema(description = "内部令牌")
@HideFromApiTypes(ApiType.OPENAPI)  // 仅在OpenAPI文档中隐藏
private String internalToken;
```

### 3. 在接口方法上使用注解

```java
// 单个API类型隐藏
@PostMapping("/admin/users")
@HideFromApiTypes(ApiType.OPENAPI)  // 仅在OpenAPI文档中隐藏
public Result<User> createUser(@RequestBody User user) {
    // 实现逻辑
}

// 多个API类型隐藏
@DeleteMapping("/internal/users")
@HideFromApiTypes({ApiType.OPENAPI, ApiType.RPC})  // 在OpenAPI和RPC文档中隐藏
public Result<Void> deleteUser(@PathVariable String id) {
    // 实现逻辑
}
```

### 4. 在方法参数上使用注解（新功能）

```java
@GetMapping("/download")
public void downloadFile(
    @RequestParam String fileKey,
    @RequestParam(required = false) String fileName,

    // 断点续传参数仅在OpenAPI中隐藏
    @HideFromApiTypes(ApiType.OPENAPI)
    @RequestParam(required = false, defaultValue = "false") boolean range,

    // 调试参数在OpenAPI和RPC中都隐藏
    @HideFromApiTypes({ApiType.OPENAPI, ApiType.RPC})
    @RequestParam(required = false, defaultValue = "false") boolean debug) {
    // 实现逻辑
}
```

## 核心组件

### 1. ApiType 枚举
定义了三种API类型：
- `WEB`: Web端接口
- `OPENAPI`: 对外开放接口
- `RPC`: 内部服务接口

### 2. @HideFromApiTypes 注解
用于标记需要在特定API类型中隐藏的字段或接口方法。

**使用位置**:
- 字段级别：隐藏模型中的特定字段
- 方法级别：隐藏控制器中的特定接口
- 类级别：隐藏整个控制器类
- **参数级别**：隐藏接口方法中的特定参数（新功能）

**注意**: 注解已迁移到`gs-engine-sdk-common`模块，保持SDK包的轻量化。

## 使用方法

### 字段级别隐藏

```java
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;

@Schema(description = "用户模型")
@Data
public class UserModel {
    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "用户名")
    private String username;

    // 密码字段在OpenAPI和RPC文档中隐藏
    @Schema(description = "密码")
    @HideFromApiTypes({ApiType.OPENAPI, ApiType.RPC})
    private String password;

    // 内部令牌仅在OpenAPI文档中隐藏（单个值简化写法）
    @Schema(description = "内部令牌")
    @HideFromApiTypes(ApiType.OPENAPI)
    private String internalToken;
}
```

### 接口级别隐藏

```java
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;

@RestController
@Tag(name = "用户管理")
public class UserController {

    // 公开接口，在所有API文档中可见
    @GetMapping("/api/user/{id}")
    @Operation( summary = "获取用户信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识库11223344")})} )
    public UserModel getUser(@PathVariable String id) {
        // 实现逻辑
    }

    // 管理接口，在OpenAPI文档中隐藏（单个值简化写法）
    @PostMapping("/api/admin/user")
    @Operation( summary = "管理用户", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识库11223344")})} )
    @HideFromApiTypes(ApiType.OPENAPI)
    public UserModel adminUser(@RequestBody UserModel user) {
        // 实现逻辑
    }

    // 内部接口，在OpenAPI和RPC文档中都隐藏
    @DeleteMapping("/api/internal/user")
    @Operation( summary = "内部用户操作", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识库11223344")})} )
    @HideFromApiTypes({ApiType.OPENAPI, ApiType.RPC})
    public String internalOperation() {
        // 实现逻辑
    }
}
```

### 类级别隐藏

```java
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;

// 整个控制器在OpenAPI文档中隐藏（单个值简化写法）
@RestController
@HideFromApiTypes(ApiType.OPENAPI)
public class InternalController {
    // 所有方法都会在OpenAPI文档中隐藏
}
```

## 配置说明

系统已自动配置了三个API文档分组：

1. **Web端接口** (`/web/**`): 显示所有参数和接口
2. **对外开放接口** (`/openapi/**`): 根据注解隐藏标记的参数和接口
3. **内部服务接口** (`/rpc/**`): 根据注解隐藏标记的参数和接口

## 访问地址

- Swagger UI: `http://localhost:8092/ais/swagger-ui/index.html`
- Knife4j UI: `http://localhost:8092/ais/doc.html`

在文档界面中，您可以通过切换不同的分组来查看不同API类型的文档。

## 工作原理

详细的工作原理和流程说明请参考：[API_HIDING_PRINCIPLE.md](./API_HIDING_PRINCIPLE.md)

### 核心流程
1. **应用启动**: 初始化模型类发现机制，扫描并缓存所有模型类
2. **文档生成**: SpringDoc为每个API分组生成OpenAPI规范
3. **定制器处理**: 根据注解隐藏相应的字段和接口
4. **智能类查找**: 使用多层次策略快速定位模型类

### 性能优化
- 使用缓存避免重复扫描和反射操作
- 分层查找策略，优先使用快速方法
- 支持并发访问和延迟初始化

## 注意事项

1. **注解优先级**: 方法级别的注解会覆盖类级别的注解
2. **路径匹配**: 确保您的接口路径符合对应的模式（`/web/**`、`/openapi/**`、`/rpc/**`）
3. **字段隐藏**: 字段隐藏基于智能类查找机制，支持模糊匹配
4. **性能考虑**: 注解处理在文档生成时进行，不会影响运行时性能
5. **日志级别**: 当前使用INFO级别日志，便于临时查看处理过程

## 调试功能

提供调试接口用于验证和测试功能：
- `/web/debug/api-hiding/model-discovery/stats` - 查看模型类发现统计
- `/web/debug/api-hiding/model-discovery/find/{className}` - 测试类查找
- `/web/debug/api-hiding/test-field-hiding` - 测试字段隐藏

## 示例

参考以下示例类查看完整的使用方法：
- `ExampleApiModel` - 字段级别隐藏示例
- `ExampleController` - 接口级别隐藏示例
- `ParameterHidingExampleController` - 参数级别隐藏示例
