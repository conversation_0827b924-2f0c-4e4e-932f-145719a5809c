package com.chinatelecom.gs.engine.channel.service.dto;

import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * API秘钥管理
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Getter
@Setter
public class ChannelApiSecretDTO extends BaseDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 应用id
     */
    private String appId;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 秘钥id
     */
    private String secretId;

    /**
     * 名称
     */
    private String name;

    /**
     * 秘钥
     */
    private String secret;

    /**
     * 创建用户ID
     */
    private String createId;

    /**
     * 创建用户名
     */
    private String createName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后修改用户ID
     */
    private String updateId;

    /**
     * 最后修改用户名
     */
    private String updateName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * appCode
     */
    private String appCode;

    /**
     * sourceSystem
     */
    private AppSourceType sourceSystem;
}
