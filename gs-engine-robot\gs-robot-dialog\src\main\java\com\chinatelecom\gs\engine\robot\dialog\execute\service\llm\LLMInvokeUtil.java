package com.chinatelecom.gs.engine.robot.dialog.execute.service.llm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson2.JSON;
import com.chinatelecom.gs.engine.common.cache.memory.MessageResponseCache;
import com.chinatelecom.gs.engine.common.s3.CloudStorageDao;
import com.chinatelecom.gs.engine.core.corekit.common.core.AnswerService;
import com.chinatelecom.gs.engine.core.corekit.common.core.RequestNodeCache;
import com.chinatelecom.gs.engine.core.model.toolkit.MultiStreamingChatLanguageModel;
import com.chinatelecom.gs.engine.core.model.toolkit.StreamingChatLanguageModel;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.*;
import com.chinatelecom.gs.engine.core.model.toolkit.core.prompt.PromptRender;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.SafeCheckAssistService;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.config.SafeLLMContext;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.enums.ContentTypeEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.enums.EndMarkEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.service.SafeFenceConfigService;
import com.chinatelecom.gs.engine.core.sdk.enums.LLMMessageBizCode;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.Tool;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.ToolFunction;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.core.sdk.vo.msg.MessageCallback;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.AgentActionRequest;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.AgentStrategyConfig;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.config.AgentLlmConfig;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.secure.AgentSecureCheck;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.toolselect.ToolUtils;
import com.chinatelecom.gs.engine.robot.dialog.memory.sys.LLMHistoryStoreService;
import com.chinatelecom.gs.engine.robot.dialog.memory.usr.sys.DialogDetailStoreService;
import com.chinatelecom.gs.engine.robot.manage.info.domain.dto.StrategyNodeSetting;
import com.chinatelecom.gs.engine.robot.manage.info.utils.AgentLLMUtil;
import com.chinatelecom.gs.engine.robot.manage.service.AgentKnowledgeBaseService;
import com.chinatelecom.gs.engine.robot.sdk.dto.FunctionTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.dto.SourceInfo;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.InnerPromptEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.IntentRetrieveType;
import com.chinatelecom.gs.engine.robot.sdk.enums.SystemIntentEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.utils.IdGenerator;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.AgentConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.DialogConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.GlobalPolicyConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.ModelConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.common.Header;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntent;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntentAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.LLMSourceTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.ToolMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.Answer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.AnswerResponseType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.ToolAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.ToolMixerAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.ActionTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.request.AnswerBuildRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.DialogMessageTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.model.LLMConfig;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LLMInvokeUtil {

    public static final String LLM_RES_KEY = "llmResProc";

    @Resource
    private AgentLLMUtil agentLLMUtil;

    @Resource
    private SafeFenceConfigService safeFenceConfigService;

    @Resource
    private LLMHistoryStoreService llmHistoryStoreService;

    @Resource
    private StreamingChatLanguageModel streamingChatLanguageModel;

    @Resource
    private MultiStreamingChatLanguageModel multiStreamingChatLanguageModel;

    @Resource
    private AgentSecureCheck agentSecureCheck;

    @Resource
    private AnswerService answerService;

    @Resource
    private RequestNodeCache requestNodeCache;

    @Resource
    private DialogDetailStoreService dialogDetailStoreService;

    @Resource
    private AgentLlmConfig agentLlmConfig;

    @Resource
    private CloudStorageDao cloudStorageDao;

    @Resource
    protected ModelServiceClient remoteServiceClient;

    @Resource
    private AgentKnowledgeBaseService agentKnowledgeBaseService;

    public LLMResult invoke(String query, @NotNull AgentStrategyConfig actionRequest, StrategyNodeSetting strategyNodeSetting, List<ToolIntentAnswer> intents, List<WrapLLMMessage> histories, DagContext context) {
        // 大模型配置
        LLMRequest<String> llmRequest = this.agentLLMUtil.buildLLMRequest(actionRequest.getAgentConfig());

        llmRequest.setRequestId(actionRequest.getHeader().getTrack().getMessageId());
        llmRequest.setSystemSettings(StringUtils.isNotBlank(strategyNodeSetting.getSystemPrompt()) ?
                strategyNodeSetting.getSystemPrompt() + "用户输入信息：" + JSON.toJSONString(context.getDagParam().getInputParamMap())
                : strategyNodeSetting.getSystemPrompt());

        llmRequest.setHistories(histories); // 对话历史

        // 用户请求query
        WrapLLMMessage requestMessage = new WrapLLMMessage();
        requestMessage.setContent(buildPrompt(intents, actionRequest, query));
        requestMessage.setRole(MessageRoleEnum.user.getCode());
        llmRequest.setRequestMessage(requestMessage);

        llmRequest.setTools(buildTools(intents.stream().map(intent -> (ToolIntent) intent).collect(Collectors.toList()))); // 工具：插件 + 工作流

        PropertyFilter filter = (object, name, value) -> !name.equals("apikey") && !name.equals("modelSecret") && !name.equals("modelApi");
        log.info("【LLM】 上行消息内容：{}", JSON.toJSONString(llmRequest, filter));

        List<SourceInfo> sourceInfo = getSourceInfoList(intents, false);

        LLMResult llmResult = invokeLLM(llmRequest, actionRequest, sourceInfo);
        if (Objects.nonNull(llmResult) && Objects.nonNull(llmResult.getLlmMessage())) {
            log.info("【LLM】保存对话历史：{}", JSON.toJSONString(llmResult));
            saveLLMHistory(actionRequest, llmRequest.getRequestMessage(), llmResult.getLlmMessage());
        }
        return llmResult;
    }


    public LLMResult invokeMix(String query, @NotNull AgentStrategyConfig actionRequest, StrategyNodeSetting strategyNodeSetting, Map<String, ToolMixerAnswer> multiAnswersMap, List<WrapLLMMessage> histories, DagContext context) {
        // 大模型配置
        LLMRequest<String> llmRequest = this.agentLLMUtil.buildLLMRequest(actionRequest.getAgentConfig());

        llmRequest.setRequestId(actionRequest.getHeader().getTrack().getMessageId());
        llmRequest.setSystemSettings(StringUtils.isNotBlank(strategyNodeSetting.getSystemPrompt()) ?
                strategyNodeSetting.getSystemPrompt() + "用户输入信息：" + JSON.toJSONString(context.getDagParam().getInputParamMap())
                : strategyNodeSetting.getSystemPrompt());

        llmRequest.setHistories(histories); // 对话历史
        // 用户请求query
        WrapLLMMessage requestMessage = new WrapLLMMessage();
        requestMessage.setContent(buildMixPrompt(multiAnswersMap, actionRequest, query));
        requestMessage.setRole(MessageRoleEnum.user.getCode());
        llmRequest.setRequestMessage(requestMessage);

        PropertyFilter filter = (object, name, value) -> !name.equals("apikey") && !name.equals("modelSecret") && !name.equals("modelApi");
        log.info("【LLM】 Mix上行消息内容：{}", JSON.toJSONString(llmRequest, filter));

        LLMResult llmResult = invokeLLM(llmRequest, actionRequest, Collections.emptyList());
        if (Objects.nonNull(llmResult) && Objects.nonNull(llmResult.getLlmMessage())) {
            log.info("【LLM】Mix保存对话历史：{}", JSON.toJSONString(llmResult));
            saveLLMHistory(actionRequest, llmRequest.getRequestMessage(), llmResult.getLlmMessage());
        }
        return llmResult;
    }


    public LLMResult summaryInvoke(AgentStrategyConfig actionRequest, List<ToolIntentAnswer> intents, String query) {
        LLMRequest<String> llmRequest = this.agentLLMUtil.buildLLMRequest(actionRequest.getAgentConfig());
        String promptTemplate = this.agentLLMUtil.getDefaultPromptByTemplate(InnerPromptEnum.KMS_CONTENT_SUMMARY.getConfigKey());
        int maxLen = llmRequest.getMaxInputTokens() - (ObjectUtils.isEmpty(llmRequest.getSystemSettings()) ? 0 : llmRequest.getSystemSettings().length()) - promptTemplate.length();
        String prompt = PromptRender.apply(promptTemplate, buildSummaryParams(actionRequest, query, intents, maxLen));
        WrapLLMMessage requestMessage = new WrapLLMMessage();
        requestMessage.setContent(prompt);
        requestMessage.setRole(MessageRoleEnum.user.getCode());
        llmRequest.setRequestMessage(requestMessage);
        PropertyFilter filter = (object, name, value) -> !name.equals("apikey") && !name.equals("modelSecret") && !name.equals("modelApi");
        log.info("【LLM】 总结上行消息内容：{}", JSON.toJSONString(llmRequest), filter);
        LLMResult llmResult = invokeLLM(llmRequest, actionRequest, getSourceInfoList(intents, false));
        if (Objects.isNull(llmResult) || Objects.isNull(llmResult.getLlmMessage())) {
            return null;
        }


        return llmResult;

    }

    public LLMResult invokeMulti(String query, AgentStrategyConfig actionRequest, ToolIntentAnswer intent) {
        //top1是图片类型，调用多模态大模型
        String imageKey = (intent.getOriginalFileKey());
        log.debug("调用多模态下载图片，imageKey : {}", imageKey);
        try (InputStream inputStream = cloudStorageDao.download(imageKey);
             BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream)) {

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;

            while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            byte[] bytes = outputStream.toByteArray();
            String image = Base64.getEncoder().encodeToString(bytes);
            BaseMultiLLMRequest request = new BaseMultiLLMRequest();
            request.setImage(image);
            request.setText(query);

            ModelConfig multiModelConfig = actionRequest.getAgentConfig().getMultiModelConfig();
            LLMConfig llmConfig = multiModelConfig.getLlmConfig();

            request.setLlmModelInfo(ExternalModelInfo.builder().modelName(llmConfig.getModelName())
                    .modelUrl(llmConfig.getModelUrl())
                    .modelApi(llmConfig.getModelApi())
                    .modelSecret(llmConfig.getModelSecret()).build());

            //1.调用VLLM进行图片理解
            LLMResult vllmResult = invokeMultiLLMAction(request, actionRequest, getSourceInfoList(Collections.singletonList(intent), false));

            //2.根据query、描述、多模态结果，交给LLM融合
            LLMRequest<String> llmRequest = this.buildLLMRequestWithVLLMInfo(query, actionRequest, intent, vllmResult);
            LLMResult llmResult = invokeLLM(llmRequest, actionRequest, getSourceInfoList(Collections.singletonList(intent), false));
            return llmResult;
        } catch (IOException e) {
            throw new BizException("B0001", "图片下载失败");
        }
    }

    private LLMRequest<String> buildLLMRequestWithVLLMInfo(String query, AgentStrategyConfig actionRequest, ToolIntentAnswer intent, LLMResult vllmResult) {
        LLMRequest<String> llmRequest = this.agentLLMUtil.buildLLMRequest(actionRequest.getAgentConfig());
        llmRequest.setSystemSettings(null);
        //1.promptTemplate
        String promptTemplate = this.agentLLMUtil.getDefaultPromptByTemplate(InnerPromptEnum.LLM_ANSWER_WITH_MULTI.getConfigKey());
        //2.params
        Map<String, String> params = Maps.newLinkedHashMap();
        params.put("query", query != null ? query : StringUtils.EMPTY);
        params.put("vl_result", Optional.ofNullable(vllmResult.getLlmMessage()).map(WrapLLMMessage::getContent).orElse(StringUtils.EMPTY));
        params.put("desc", Optional.ofNullable(intent).map(ToolIntentAnswer::getSimilarContent).orElse(StringUtils.EMPTY));
        //安全围栏相关配置
        setSafeCenterConfig(actionRequest, llmRequest);
        //3.裁剪，避免超过token长度
        int maxLen = llmRequest.getMaxInputTokens() - promptTemplate.length();
        Map<String, String> truncateParams = truncateParams(params, maxLen);
        //4.拼接content
        String content = PromptRender.apply(promptTemplate, truncateParams);
        WrapLLMMessage requestMessage = new WrapLLMMessage();
        requestMessage.setContent(content);
        requestMessage.setRole(MessageRoleEnum.user.getCode());
        llmRequest.setRequestMessage(requestMessage);
        return llmRequest;
    }

    private void setSafeCenterConfig(AgentStrategyConfig actionRequest, LLMRequest<?> llmRequest) {
        Boolean clientSafeFenceSwitch = actionRequest.getHeader().getClient().getSafeFenceSwitch();
        llmRequest.setSafeFenceSwitch((Objects.nonNull(clientSafeFenceSwitch) ? clientSafeFenceSwitch : actionRequest.getAgentConfig().getGlobalPolicyConfig().isSecurityFenceSwitch()) && safeFenceConfigService.getSafeFenceSwitch());
        llmRequest.setSafeFenceConfig(actionRequest.getAgentConfig().getSafeFenceConfig());
        llmRequest.setFenceScript(actionRequest.getAgentConfig().getGlobalPolicyConfig().getSecurityFenceScript());
    }

    private Map<String, String> truncateParams(Map<String, String> originParams, int maxLength) {
        if (maxLength <= 0) {
            return Maps.newHashMap();
        }
        Map<String, String> result = Maps.newHashMap();
        int currentLength = 0;
        for (Map.Entry<String, String> entry : originParams.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            int remainingLength = maxLength - currentLength;
            if (remainingLength <= 0) {
                return result;
            }
            String truncatedValue = value.substring(0, Math.min(value.length(), remainingLength));
            result.put(key, truncatedValue);
            currentLength += truncatedValue.length();
        }
        return result;
    }

    public LLMResult invokeForToolResult(AgentStrategyConfig actionRequest, List<ToolIntentAnswer> intents, WrapLLMMessage toolResult, List<WrapLLMMessage> histories, String messageId) {
        LLMRequest<String> llmRequest = agentLLMUtil.buildLLMRequest(actionRequest.getAgentConfig());
        llmRequest.setHistories(histories);

        ToolIntentAnswer intentAnswer = intents.get(0);

        llmRequest.setRequestMessage(toolResult);
        List<Tool> tools = this.buildTools(Collections.singletonList(intentAnswer));
        llmRequest.setTools(tools);

        llmRequest.setEnableThinking(false);
        llmRequest.setRequestId(actionRequest.getHeader().getTrack().getMessageId());
        llmRequest.setLlmProcessType(LLMProcessType.DIRECT);
        llmRequest.setMessageId(messageId);

        List<SourceInfo> sourceInfo = getSourceInfoList(intents, true);
        PropertyFilter filter = (object, name, value) -> !name.equals("apikey") && !name.equals("modelSecret") && !name.equals("modelApi");
        log.info("【LLM】 工具调用上行消息内容：{}", JSON.toJSONString(llmRequest), filter);


        LLMResult llmResult = invokeLLM(llmRequest, actionRequest, sourceInfo);
        if (Objects.isNull(llmResult) || Objects.isNull(llmResult.getLlmMessage())) {
            return null;
        }

        WrapLLMMessage returnMessage = llmResult.getLlmMessage();

        this.saveLLMHistory(actionRequest, llmRequest.getRequestMessage(), returnMessage);

        return llmResult;
    }

    public LLMResult invokeStepLLM(LLMRequest<?> llmRequest, String progressMsg, MessageCallback<ToolMessageResponse> messageCallback, AgentConfig agentConfig, Header header) {
        CountDownLatch latch = new CountDownLatch(1);
        String messageId = header.getTrack().getDownMessageId();
        LLMStepResStreamHandler llmResStreamHandler = new LLMStepResStreamHandler(latch, messageId, messageCallback, this.agentSecureCheck, this.answerService, agentConfig, header);
        this.streamingChatLanguageModel.generateMessage(llmRequest, llmResStreamHandler);
        if (StringUtils.isNotBlank(progressMsg)) {
            llmResStreamHandler.start(progressMsg);
        }
        this.agentLLMUtil.waitingForComplete(latch);
        WrapLLMMessage returnMessage = llmResStreamHandler.getResult().get();
        log.info("【LLM】 下行消息内容：{}", JSON.toJSONString(returnMessage));
        return new LLMResult(llmResStreamHandler.getMessageId(), Collections.emptyList(), returnMessage, llmResStreamHandler.getInterrupt(), null);
    }

    public void saveLLMHistory(AgentStrategyConfig actionRequest, WrapLLMMessage request, WrapLLMMessage response) {
        List<WrapLLMMessage> currents = new ArrayList<>();
        if (Objects.nonNull(request)) {
            currents.add(request);
        }

        currents.add(response);
        Header header = actionRequest.getHeader();
        this.llmHistoryStoreService.addLLMHistory(LLM_RES_KEY, header.getTrack().getSessionId(), currents);
    }

    public ToolIntentAnswer buildLLMAnswer(LLMResult llmResult, String agentCode) {
        WrapLLMMessage llmMessage = llmResult.getLlmMessage();
        AnswerBuildRequest answerBuildRequest = new AnswerBuildRequest();
        answerBuildRequest.setContent(llmMessage.getContent());
        answerBuildRequest.setReasoningContent(llmMessage.getReasoning_content());
        answerBuildRequest.setAnswerTypeEnum(AnswerTypeEnum.MARKDOWN);
        Answer answer = answerService.buildAnswer(answerBuildRequest);
        answer.setMessageId(llmResult.getMessageId());
        answer.setSourceInfos(llmResult.getSourceInfos());


        ToolIntentAnswer intentAnswer = new ToolIntentAnswer();
        intentAnswer.setToolIntentName(SystemIntentEnum.LLM.getDesc());
        intentAnswer.setToolIntentId(SystemIntentEnum.LLM.getCode());
        intentAnswer.setScore(0.1);
        intentAnswer.setDialogEngineType(DialogEngineType.LLM);

        ToolAnswer toolIntentAnswer = BeanUtil.toBean(answer, ToolAnswer.class);
        intentAnswer.setToolAnswer(toolIntentAnswer);
        return intentAnswer;
    }

    public ToolIntentAnswer buildLLMSecurityAnswer(LLMResult llmResult) {
        AnswerBuildRequest answerBuildRequest = new AnswerBuildRequest();
        answerBuildRequest.setContent(llmResult.getLlmMessage().getContent());
        answerBuildRequest.setAnswerTypeEnum(AnswerTypeEnum.MARKDOWN);
        Answer answer = answerService.buildAnswer(answerBuildRequest);
        answer.setMessageId(llmResult.getMessageId());
        answer.setSourceInfos(llmResult.getSourceInfos());


        ToolIntentAnswer intentAnswer = new ToolIntentAnswer();
        intentAnswer.setToolIntentName(SystemIntentEnum.SENSITIVE.getDesc());
        intentAnswer.setToolIntentId(SystemIntentEnum.SENSITIVE.getCode());
        intentAnswer.setScore(0.1);
        intentAnswer.setDialogEngineType(DialogEngineType.SYSTEM);

        ToolAnswer toolIntentAnswer = BeanUtil.toBean(answer, ToolAnswer.class);
        intentAnswer.setToolAnswer(toolIntentAnswer);
        return intentAnswer;
    }


    public List<MessageResponseCache> loadUserHistory(AgentActionRequest actionRequest) {
        Header header = actionRequest.getHeader();
        AgentConfig agentConfig = actionRequest.getAgentConfig();
        if (Objects.nonNull(agentConfig.getModelConfig().getMemoryCount()) && (agentConfig.getModelConfig().getMemoryCount() > 1)) {
            List<MessageResponseCache> llmMessages = this.dialogDetailStoreService.getChatHistoryToMessage(actionRequest.getAgentCode(), header.getTrack().getSessionId(), header.getTrack().getMessageId(), agentConfig.getModelConfig().getMemoryCount());
            return llmMessages;
        }
        return Lists.newArrayList();
    }

    private String buildMixPrompt(Map<String, ToolMixerAnswer> multiAnswersMap, AgentStrategyConfig actionRequest, String query) {
        Long version = actionRequest.getHeader().getClient().getTest() ? actionRequest.getAgentConfig().getAgentInfo()
                .getTestVersion() : actionRequest.getAgentConfig().getAgentInfo().getProdVersion();

        InnerPromptEnum innerPromptEnum = InnerPromptEnum.LOCAL_MIX_CONTENT;
        GlobalPolicyConfig globalPolicyConfig = actionRequest.getAgentConfig().getGlobalPolicyConfig();
        if (Objects.nonNull(globalPolicyConfig)) {
            Boolean clientChatLLMSwitch = actionRequest.getHeader().getClient().getChatLLMSwitch();
            Boolean chatLLMSwitch = Objects.nonNull(clientChatLLMSwitch) ? clientChatLLMSwitch : globalPolicyConfig.getChatLLMSwitch();
            if (Boolean.FALSE.equals(chatLLMSwitch)) {
                innerPromptEnum = InnerPromptEnum.LOCAL_MIX_CONTENT_NOT_CHAT;
            }
        }

        String promptTemplate = this.agentLLMUtil.getPromptTemplateByCode(actionRequest.getAgentCode(),
                    innerPromptEnum.getConfigKey(), version);
        return PromptRender.apply(promptTemplate, buildMixParam(actionRequest, query, multiAnswersMap));

    }

    private Map<String, String> buildMixParam(AgentStrategyConfig actionRequest, String query, Map<String, ToolMixerAnswer> multiAnswersMap) {
        Map<String, String> params = new HashMap<>();
        params.put("question", query);
        String context = toMixContext(multiAnswersMap);
        params.put("context", context);
        DialogConfig dialogConfig = actionRequest.getAgentConfig().getDialogConfig();
        if (Objects.nonNull(dialogConfig) && StringUtils.isNotBlank(dialogConfig.getNoAnswerMarkdownScript())) {
            params.put("noAnswerScript", dialogConfig.getNoAnswerMarkdownScript());
        } else {
            params.put("noAnswerScript", "对不起，我无法回答你的问题。你是否有其他问题需要帮助？");
        }
        return params;
    }

    private String toMixContext(Map<String, ToolMixerAnswer> multiAnswersMap){
        List<String> candidates = new ArrayList<>();
        int index = 1;

        for (Map.Entry<String, ToolMixerAnswer> entry : multiAnswersMap.entrySet()) {
            String functionType = entry.getKey();
            ToolMixerAnswer answer = entry.getValue();

            String content = answer.getToolAnswer().getContent().toString();
            if (functionType.equalsIgnoreCase(FunctionTypeEnum.TABLE.getCode())) {
                List<ToolIntentAnswer> sourceAnswers = answer.getSourceToolAnswers();
                if (sourceAnswers.stream().anyMatch(ToolUtils::biAnswer)) {
                    //抽取出summary
                    com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(content);
                    if (jsonObject.containsKey("analyze")) {
                        content = "{\"summary\": \"" + jsonObject.getString("analyze") + ", 详情见表格\"}";
                    }
                }

            }

            String stringBuilder = "#来源" +
                    index +
                    "\n" +
                    content +
                    "\n";
            candidates.add(stringBuilder);
            index++;
        }

        return String.join("\n", candidates)+"\n";
    }




    private String buildPrompt(List<ToolIntentAnswer> intents, AgentStrategyConfig actionRequest, String query) {
        Map<DialogEngineType, List<ToolIntentAnswer>> groupedIntents = intents.stream()
                .collect(Collectors.groupingBy(ToolIntentAnswer::getDialogEngineType));
        List<ToolIntentAnswer> kmsIntents = groupedIntents.get(DialogEngineType.KMS);
        List<ToolIntentAnswer> fileIntents = groupedIntents.get(DialogEngineType.FILE);
        List<ToolIntentAnswer> databaseIntents = groupedIntents.get(DialogEngineType.DATABASE);
        if (!CollectionUtils.isEmpty(kmsIntents) || !CollectionUtils.isEmpty(fileIntents) || !CollectionUtils.isEmpty(databaseIntents)) {
            Long version = actionRequest.getHeader().getClient().getTest() ? actionRequest.getAgentConfig()
                    .getAgentInfo().getTestVersion() : actionRequest.getAgentConfig().getAgentInfo().getProdVersion();
            GlobalPolicyConfig globalPolicyConfig = actionRequest.getAgentConfig().getGlobalPolicyConfig();
            InnerPromptEnum innerPromptEnum = InnerPromptEnum.LOCAL_DOC_QA;
            if (Objects.nonNull(globalPolicyConfig)) {
                Boolean clientChatLLMSwitch = actionRequest.getHeader().getClient().getChatLLMSwitch();
                Boolean chatLLMSwitch = Objects.nonNull(clientChatLLMSwitch) ? clientChatLLMSwitch : globalPolicyConfig.getChatLLMSwitch();
                if (Boolean.FALSE.equals(chatLLMSwitch)) {
                    innerPromptEnum = InnerPromptEnum.LOCAL_DOC_QA_NOT_CHAT;
                }
            }
            String promptTemplate = this.agentLLMUtil.getPromptTemplateByCode(actionRequest.getAgentCode(),
                    innerPromptEnum.getConfigKey(), version);
            String prompt = PromptRender.apply(promptTemplate, buildLLMKmsPromptParams(actionRequest, query, intents));
            return prompt + agentLlmConfig.getResponseImgPromptTemplate();
        }
        return query;
    }


    public List<Tool> buildTools(List<ToolIntent> intents) {
        List<Tool> tools = new ArrayList<>();
        for (ToolIntent intent : intents) {
            if (intent.getDialogEngineType().equals(
                    DialogEngineType.WORKFLOW) || intent.getDialogEngineType().equals(DialogEngineType.PLUGIN)) {
                Object content = intent.getContent();
                if (content instanceof ToolFunction toolFunction) {
                    //这里假定function的name格式默认为dialogEngine:intent_id:realName
                    ToolFunction copy = new ToolFunction();
                    String intentName = intent.getToolIntentId().replace("-", "");
                    String toolName = intent.getDialogEngineType().getCode() + "-" + intentName + "-" +
                            toolFunction.getName();
                    copy.setName(toolName);
                    copy.setDescription(toolFunction.getDescription());
                    copy.setParameters(toolFunction.getParameters());

                    Tool tool = new Tool();
                    tool.setType("function");
                    tool.setFunction(copy);

                    tools.add(tool);
                }
            }
        }
        return tools;
    }

    private LLMResult invokeLLM(LLMRequest<?> llmRequest, AgentStrategyConfig actionRequest, List<SourceInfo> sources) {
        SafeCheckAssistService safeCheckAssistService = SpringContextUtils.getBean(SafeCheckAssistService.class);
        /** 安全围栏相关配置 */
        setSafeCenterConfig(actionRequest, llmRequest);

        CountDownLatch latch = new CountDownLatch(1);
        String messageId = null;
        if (StringUtils.isEmpty(llmRequest.getMessageId())) {
            messageId = IdGenerator.getMessageId();
        } else {
            messageId = llmRequest.getMessageId();
        }
        LLMStreamCallbackHandler callbackHandler = new LLMStreamCallbackHandler(latch, messageId, actionRequest.getMessageCallback(), actionRequest.getActionCallback(), this.agentSecureCheck, safeCheckAssistService, actionRequest);
        callbackHandler.start(StringUtils.EMPTY);
        try {
            streamingChatLanguageModel.generateMessage(llmRequest, callbackHandler);
        } catch (BizException e) {
            if ("C0005".equals(((e.getCode())))) {
                ToolMessageResponse toolMessageResponse = buildSafeMessage(actionRequest, messageId);
                callbackHandler.getCallback().invoke(toolMessageResponse);
                WrapLLMMessage wrapLLMMessage = new WrapLLMMessage();
                wrapLLMMessage.setCode(LLMMessageBizCode.SENSITIVE.getCode());
                wrapLLMMessage.setContent(e.getMessage());
                callbackHandler.onComplete(new Response<>(wrapLLMMessage));
                log.error("大模型调用命中安全中心检测", e);
            }
        }
        agentLLMUtil.waitingForComplete(latch);

        //出现重复输入，再次生成
        if (Objects.nonNull(callbackHandler.getInterrupt()) && callbackHandler.getInterrupt() == 3) {
            log.info("【LLM】 出现重复输入，再次生成");
            if (latch.getCount() <= 0) {
                latch = new CountDownLatch(1);
            }
            callbackHandler = new LLMStreamCallbackHandler(latch, callbackHandler.getMessageId(), actionRequest.getMessageCallback(), actionRequest.getActionCallback(), this.agentSecureCheck, safeCheckAssistService, actionRequest);
            streamingChatLanguageModel.generateMessage(llmRequest, callbackHandler);
            agentLLMUtil.waitingForComplete(latch);
        }

        if (Objects.nonNull(callbackHandler.getInterrupt()) && callbackHandler.getInterrupt() == 3) {
            log.info("【LLM】 二次生成依然重复");
            return null;
        }


        WrapLLMMessage returnMessage = Objects.nonNull(callbackHandler.getResult()) ? callbackHandler.getResult().get() : new WrapLLMMessage();
        SafeLLMContext safeLLMContext = safeFenceConfigService.buildSafeLLMContext(llmRequest);

        boolean isPassSafeFence = safeCheckAssistService.checkContentIsSafe(callbackHandler.getResult().get().getContent(), ContentTypeEnum.MODEL_OUTPUT, safeLLMContext, EndMarkEnum.END, llmRequest.getSendInvokeMQ());
        if (!isPassSafeFence) {
            log.info("【LLM】 大模型输出触发安全围栏");
            returnMessage.setCode(LLMMessageBizCode.SENSITIVE.getCode());
            returnMessage.setContent(safeLLMContext.getFenceScript());
        }
        log.info("【LLM】 下行消息内容：{}", JSON.toJSONString(returnMessage));
        return new LLMResult(callbackHandler.getMessageId(), sources, returnMessage, callbackHandler.getInterrupt(), callbackHandler.getMessageResponse());
    }

    private LLMResult invokeMultiLLMAction(BaseMultiLLMRequest llmRequest, AgentStrategyConfig actionRequest,
                                           List<SourceInfo> sourceInfos) {

        SafeCheckAssistService safeCheckAssistService = SpringContextUtils.getBean(SafeCheckAssistService.class);

        CountDownLatch latch = new CountDownLatch(1);
        LLMActionStreamHandler callbackHandler = new LLMActionStreamHandler(latch, actionRequest.getHeader().getTrack().getDownMessageId(), actionRequest.getActionCallback(), this.agentSecureCheck, safeCheckAssistService, actionRequest, ActionTypeEnum.IMAGE_UNDERSTAND);
        callbackHandler.start(null);
        this.multiStreamingChatLanguageModel.generateMessage(llmRequest, callbackHandler);
        this.agentLLMUtil.waitingForComplete(latch);

        //出现重复输入，再次生成
        if (Objects.nonNull(callbackHandler.getInterrupt()) && callbackHandler.getInterrupt() == 3) {
            log.info("【VLLM】 出现重复输入，再次生成");
            if (latch.getCount() <= 0) {
                latch = new CountDownLatch(1);
            }
            callbackHandler = new LLMActionStreamHandler(latch, callbackHandler.getMessageId(), actionRequest.getActionCallback(), this.agentSecureCheck, safeCheckAssistService, actionRequest, ActionTypeEnum.IMAGE_UNDERSTAND);
            this.multiStreamingChatLanguageModel.generateMessage(llmRequest, callbackHandler);
            agentLLMUtil.waitingForComplete(latch);
        }

        if (Objects.nonNull(callbackHandler.getInterrupt()) && callbackHandler.getInterrupt() == 3) {
            log.info("【VLLM】 二次生成依然重复");
            return null;
        }

        WrapLLMMessage returnMessage = callbackHandler.getResult().get();
        log.info("【VLLM】 下行消息内容：{}", JSON.toJSONString(returnMessage));
        return new LLMResult(callbackHandler.getMessageId(), sourceInfos, returnMessage, callbackHandler.getInterrupt(), null);
    }


    private List<SourceInfo> getSourceInfoList(List<ToolIntentAnswer> intents, boolean toolInvokeResult) {
        Map<LLMSourceTypeEnum, LinkedHashMap<String, SourceInfo>> sourceMaps = Maps.newTreeMap();
        Map<String, SourceInfo.Candidate> docCandidateMap = Maps.newHashMap();

        if (CollectionUtils.isEmpty(intents)) {
            return new ArrayList<>();
        }

        for (int i = 0; i < intents.size(); i++) {
            ToolIntentAnswer intent = intents.get(i);

            LLMSourceTypeEnum llmSourceTypeEnum;

            if (intent.getDialogEngineType().equals(DialogEngineType.KMS)) {
                llmSourceTypeEnum = LLMSourceTypeEnum.KNOWLEDGE;

                LinkedHashMap<String, SourceInfo> sources = sourceMaps.getOrDefault(llmSourceTypeEnum, Maps.newLinkedHashMap());
                SourceInfo sourceInfo = sources.get(intent.getParentCode());
                if (Objects.isNull(sourceInfo)) {
                    sourceInfo = new SourceInfo();
                    sourceInfo.setSourceType(llmSourceTypeEnum.getType());
                    sourceInfo.setSourceCode(intent.getParentCode());
                    sourceInfo.setSourceName(intent.getParentName());
                    sourceInfo.setPublicViewSwitch(intent.isPublicViewSwitch());
                    sourceInfo.setCandidates(new ArrayList<>());
                }

                if (Objects.isNull(intent.getContent())) {
                    continue;
                }


                List<SourceInfo.Candidate> candidates = sourceInfo.getCandidates();
                SourceInfo.Candidate candidate = SourceInfo.Candidate.builder()
                        .code(intent.getToolIntentId())
                        //这里的name先写死
                        .name(intent.getToolIntentName())
                        .index(i)
                        .build();

                Map<String, Object> extraData = intent.getExtraData();
                candidate.setExtraData(extraData);

                Answer answer = (Answer) intent.getContent();
                candidate.setContent(this.answerService.getContent(answer.getAnswerType().getCode(), answer.getContent()));
                candidate.setSimilarContent(intent.getSimilarContent());
                candidates.add(candidate);
                sourceInfo.setCandidates(candidates);

                sources.put(sourceInfo.getSourceCode(), sourceInfo);
                sourceMaps.put(llmSourceTypeEnum, sources);

            } else if (intent.getDialogEngineType().equals(DialogEngineType.PLUGIN)) {

            } else if (intent.getDialogEngineType().equals(DialogEngineType.WORKFLOW)) {

            } else if (intent.getDialogEngineType().equals(DialogEngineType.FILE)) {
                llmSourceTypeEnum = LLMSourceTypeEnum.KNOWLEDGE;
                LinkedHashMap<String, SourceInfo> sources = sourceMaps.getOrDefault(llmSourceTypeEnum, Maps.newLinkedHashMap());
                SourceInfo sourceInfo = sources.get(DialogEngineType.FILE.getCode());
                if (Objects.isNull(sourceInfo)) {
                    sourceInfo = new SourceInfo();
                    sourceInfo.setSourceType(LLMSourceTypeEnum.KNOWLEDGE.getType());
                    sourceInfo.setSourceCode(DialogEngineType.FILE.getCode());
                    sourceInfo.setSourceName("用户上传文档");
                    sourceInfo.setCandidates(new ArrayList<>());
                    sources.put(sourceInfo.getSourceCode(), sourceInfo);
                }
                if (!docCandidateMap.containsKey(intent.getParentCode())) {
                    List<SourceInfo.Candidate> candidates = sourceInfo.getCandidates();
                    SourceInfo.Candidate candidate = SourceInfo.Candidate.builder()
                            .code(intent.getToolIntentId())
                            //这里的name先写死
                            .name(intent.getToolIntentName())
                            .index(i)
                            .build();
                    candidates.add(candidate);
                    docCandidateMap.put(intent.getParentCode(), candidate);
                }
                sourceMaps.put(llmSourceTypeEnum, sources);
            } else if (intent.getDialogEngineType().equals(DialogEngineType.DATABASE)) {
                llmSourceTypeEnum = LLMSourceTypeEnum.DATABASE;
                LinkedHashMap<String, SourceInfo> sources = sourceMaps.getOrDefault(llmSourceTypeEnum, Maps.newLinkedHashMap());
                SourceInfo sourceInfo = sources.get(intent.getParentCode());
                if (Objects.isNull(sourceInfo)) {
                    sourceInfo = new SourceInfo();
                    sourceInfo.setSourceType(LLMSourceTypeEnum.DATABASE.getType());
                    sourceInfo.setSourceCode(intent.getParentCode());
                    sourceInfo.setSourceName(intent.getParentName());
                    sourceInfo.setCandidates(new ArrayList<>());
                    sources.put(sourceInfo.getSourceCode(), sourceInfo);
                }
                if (!docCandidateMap.containsKey(intent.getParentCode())) {
                    List<SourceInfo.Candidate> candidates = sourceInfo.getCandidates();
                    SourceInfo.Candidate candidate = SourceInfo.Candidate.builder()
                            .code(intent.getToolIntentId())
                            //这里的name先写死
                            .name(intent.getToolIntentName())
                            .index(i)
                            .build();
                    candidates.add(candidate);
                    docCandidateMap.put(intent.getParentCode(), candidate);
                }
                sourceMaps.put(llmSourceTypeEnum, sources);
            }
        }

        List<SourceInfo> mounts = Lists.newArrayList();
        for (Map.Entry<LLMSourceTypeEnum, LinkedHashMap<String, SourceInfo>> entry : sourceMaps.entrySet()) {
            if (toolInvokeResult && (LLMSourceTypeEnum.PLUGIN.equals(entry.getKey()) || LLMSourceTypeEnum.WORKFLOW.equals(entry.getKey()))) {
                LinkedHashMap<String, SourceInfo> sourceMap = entry.getValue();
                mounts.addAll(new ArrayList<>(sourceMap.values()));
            } else if (!toolInvokeResult && (LLMSourceTypeEnum.KNOWLEDGE.equals(entry.getKey()) || LLMSourceTypeEnum.DATABASE.equals(entry.getKey()))) {
                LinkedHashMap<String, SourceInfo> sourceMap = entry.getValue();
                mounts.addAll(new ArrayList<>(sourceMap.values()));
            }
        }
        return mounts;
    }

    private Map<String, String> buildLLMKmsPromptParams(AgentStrategyConfig actionRequest, String query, List<ToolIntentAnswer> kmsIntents) {
        Map<String, String> params = new HashMap<>();
        params.put("question", query);
        String candidatesString = toKmsCandidatesString(kmsIntents, actionRequest.getAgentConfig().getAgentInfo().isDefaultRagAgent());
        params.put("context", candidatesString);
        DialogConfig dialogConfig = actionRequest.getAgentConfig().getDialogConfig();
        if (Objects.nonNull(dialogConfig) && StringUtils.isNotBlank(dialogConfig.getNoAnswerMarkdownScript())) {
            params.put("noAnswerScript", dialogConfig.getNoAnswerMarkdownScript());
        } else {
            params.put("noAnswerScript", "对不起，我无法回答你的问题。你是否有其他问题需要帮助？");
        }
        return params;
    }

    private String toKmsCandidatesString(List<ToolIntentAnswer> intents, boolean defaultRagAgent) {
        List<String> candidateContents = Lists.newArrayList();

        for (int i = 0; i < intents.size(); i++) {
            ToolIntent intent = intents.get(i);
            if (!intent.getDialogEngineType().equals(DialogEngineType.KMS) && !intent.getDialogEngineType().equals(DialogEngineType.FILE)
                    && !intent.getDialogEngineType().equals(DialogEngineType.DATABASE)) {
                continue;
            }

            if (Objects.equals(intent.getRetrieveType(), IntentRetrieveType.DOC)) {
                if (defaultRagAgent) {
                    String content = "```" + i + "```" + " " +
                            "\"文章标题：" + intent.getToolIntentName() + "，" +
                            "关联片段：" + intent.getSimilarContent() + "\"\n";
                    candidateContents.add(content);
                } else {
                    candidateContents.add(intent.getSimilarContent());
                }
            } else {
                if (Objects.isNull(intent.getContent())) {
                    continue;
                }
                if (defaultRagAgent) {
                    Answer answer = (Answer) intent.getContent();

                    String content = "```" + i + "```" + " " +
                            "\"问题：" + intent.getSimilarContent() + "，" +
                            "答案：" + this.answerService.getContent(answer.getAnswerType().getCode(), answer.getContent()) + "\"\n";
                    candidateContents.add(content);

                } else {
                    // 改成json格式
                    JSONObject qaJson = new JSONObject();
                    Answer answer = (Answer) intent.getContent();
                    qaJson.set("问题", intent.getSimilarContent());
                    qaJson.set("答案", answerService.getContent(answer.getAnswerType().getCode(), answer.getContent()));
                    candidateContents.add(qaJson.toString());
                }
            }
        }

        if (defaultRagAgent) {
            return String.join("\n", candidateContents);
        } else {
            return new JSONArray(candidateContents).toString();
        }
    }


    private Map<String, String> buildSummaryParams(AgentStrategyConfig actionRequest, String query, List<ToolIntentAnswer> intents, int maxLen) {
        Map<String, String> params = new HashMap<>();
        params.put("query", query);
        String contents = intents.stream().map(ToolIntentAnswer::getToolAnswer)
                .map(answer -> this.answerService.getContent(answer.getAnswerType().getCode(), answer.getContent()))
                .collect(Collectors.joining("\n"));
        if (contents.length() > maxLen) {
            contents = contents.substring(0, maxLen);
        }
        params.put("content", contents);
        return params;
    }

    private List<SourceInfo> toSummarySourceInfo(List<ToolIntentAnswer> intents) {
        List<SourceInfo> sourceInfos = new ArrayList<>();
        Map<String, SourceInfo> sourceMap = Maps.newHashMap();
        Map<String, SourceInfo.Candidate> docCandidateMap = Maps.newHashMap();
        int i = 0;
        for (ToolIntentAnswer intent : intents) {
            if (intent.getDialogEngineType().equals(DialogEngineType.KMS)) {
                SourceInfo sourceInfo = new SourceInfo();
                sourceInfo.setSourceType(LLMSourceTypeEnum.KNOWLEDGE.getType());
                sourceInfo.setSourceCode(intent.getParentCode());
                sourceInfo.setSourceName(intent.getParentName());
                SourceInfo.Candidate candidate = SourceInfo.Candidate.builder().code(intent.getToolIntentId())
                        .name(intent.getToolIntentName())
                        //因为总结文本太多，不展示内容，所以用name来代替
                        .content(intent.getToolIntentName())
                        .build();
                sourceInfo.setCandidates(Collections.singletonList(candidate));
                sourceInfos.add(sourceInfo);
            } else if (intent.getDialogEngineType().equals(DialogEngineType.FILE)) {
                SourceInfo sourceInfo = sourceMap.get(DialogEngineType.FILE.getCode());
                if (Objects.isNull(sourceInfo)) {
                    sourceInfo = new SourceInfo();
                    sourceInfo.setSourceType(LLMSourceTypeEnum.KNOWLEDGE.getType());
                    sourceInfo.setSourceCode(DialogEngineType.FILE.getCode());
                    sourceInfo.setSourceName("用户上传文档");
                    sourceInfo.setCandidates(new ArrayList<>());
                    sourceMap.put(DialogEngineType.FILE.getCode(), sourceInfo);
                    sourceInfos.add(sourceInfo);
                }
                if (!docCandidateMap.containsKey(intent.getParentCode())) {
                    List<SourceInfo.Candidate> candidates = sourceInfo.getCandidates();
                    SourceInfo.Candidate candidate = SourceInfo.Candidate.builder()
                            .code(intent.getToolIntentId())
                            //这里的name先写死
                            .name(intent.getToolIntentName())
                            .index(i)
                            .build();
                    i++;
                    candidates.add(candidate);
                    docCandidateMap.put(intent.getParentCode(), candidate);
                }
            }
        }
        return sourceInfos;
    }

    /**
     * 推送安全围栏话术
     */
    private ToolMessageResponse buildSafeMessage(AgentStrategyConfig actionRequest, String messageId) {
        //流式交互，需要推送一把敏感词话术
        ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
        toolIntentAnswer.setToolIntentId(SystemIntentEnum.LLM.getCode());
        toolIntentAnswer.setToolIntentName(SystemIntentEnum.LLM.getDesc());
        toolIntentAnswer.setDialogEngineType(DialogEngineType.LLM);

        ToolAnswer toolAnswer = new ToolAnswer();
        toolAnswer.setMessageId(messageId);
        toolAnswer.setResponseType(AnswerResponseType.DIRECT);
        toolAnswer.setAnswerType(AnswerTypeEnum.MARKDOWN);
        toolAnswer.setContentType(com.chinatelecom.gs.engine.robot.sdk.enums.ContentTypeEnum.ALL.getCode());
        toolAnswer.setContent(actionRequest.getAgentConfig().getGlobalPolicyConfig().getSecurityFenceScript());

        toolIntentAnswer.setToolAnswer(toolAnswer);
        toolIntentAnswer.setToolIntentId(SystemIntentEnum.SENSITIVE.getCode());
        ToolMessageResponse toolMessageResponse = ToolMessageResponse.builder()
                .sessionId(actionRequest.getHeader().getTrack().getSessionId())
                .upMsgId(actionRequest.getHeader().getTrack().getMessageId())
                .downMsgId(actionRequest.getHeader().getTrack().getDownMessageId())
                .toolAnswer(toolIntentAnswer)
                .messageType(DialogMessageTypeEnum.PART)
                .eventType(SendMessageTypeEnum.COVER)
                .build();
        return toolMessageResponse;
    }
}
