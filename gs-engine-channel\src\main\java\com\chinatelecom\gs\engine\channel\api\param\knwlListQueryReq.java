package com.chinatelecom.gs.engine.channel.api.param;

import com.chinatelecom.gs.engine.channel.api.vo.CommonAuthParam;
import com.chinatelecom.gs.engine.robot.sdk.enums.DirectoryTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.KnwlPublishStateEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @author: xktang
 * @date: 2024/5/15 下午3:08
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class knwlListQueryReq extends CommonAuthParam implements Serializable {

    @Schema(description = "技能编码", required = true)
    @NotEmpty(message = "技能编码不能为空")
    private String fatherDirectoryCode;

    @Schema(description = "业务类型编码")
    private String knwlTypeCode;

    @Schema(description = "知识标题")
    private String title;

    @Schema(description = "知识发布状态")
    private KnwlPublishStateEnum knwlPublishState;

    /**
     * 对话能力，取值为技能类型，即目录类型/技能类型
     */
    @Schema(description = "对话能力类型")
    private List<DirectoryTypeEnum> directoryTypeList;

    @Schema(description = "页数,值大小必须大于等于1", required = true)
    @NotNull(message = "页数不能为空")
    @Min(message = "页数不能小于1", value = 1)
    private Integer pageNum = 1;

    @Schema(description = "页大小,值大小必须在[1,1000]范围内", required = true)
    @NotNull(message = "页大小不能为空")
    @Max(message = "页大小不能大于1000", value = 1000)
    @Min(message = "页大小不能小于1", value = 1)
    private Integer pageSize = 50;
}
