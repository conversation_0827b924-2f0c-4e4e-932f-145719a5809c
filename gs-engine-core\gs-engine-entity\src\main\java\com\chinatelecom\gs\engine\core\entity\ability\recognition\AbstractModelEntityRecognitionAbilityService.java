package com.chinatelecom.gs.engine.core.entity.ability.recognition;

import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.enums.LogStatusEnum;
import com.chinatelecom.gs.engine.common.enums.LogTypeEnum;
import com.chinatelecom.gs.engine.common.log.track.LogMessage;
import com.chinatelecom.gs.engine.common.log.track.LogMqProducer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.workflow.core.workflow.core.utils.LogUtils;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 小模型实体识别基础类
 * @USER: pengmc1
 * @DATE: 2025/1/16 19:32
 */

@Slf4j
public abstract class AbstractModelEntityRecognitionAbilityService extends AbstractEntityRecognitionAbilityService {


    /**
     * 发送链路埋点日志
     * @param logTypeEnum
     * @param inputData
     * @param outputData
     * @param logStatusEnum
     * @param modelConfig
     * @param startTime
     * @param endTime
     */
    protected void sendTraceLog(String name, LogTypeEnum logTypeEnum, Object inputData, Object outputData, LogStatusEnum logStatusEnum, ModelPageListParam modelConfig,
                                LocalDateTime startTime, LocalDateTime endTime){
        try{
            LogMqProducer logMqProducer = SpringContextUtils.getBean(LogMqProducer.class);
            LogMessage logMessage = LogUtils.buildCommonLog(logTypeEnum,inputData,outputData,logStatusEnum,startTime,endTime,"");
            logMessage.setLogId(UUID.randomUUID().toString());
            logMessage.setName(name);
            logMessage.setUrl(modelConfig.getExternalModelUrl());
            logMessage.setConfig(JSON.toJSONString(modelConfig));
            logMqProducer.asyncSendLogMessage(logMessage);
        }catch (Exception e){
            log.error("发送大模型跟踪日志消息发生异常！", e);
        }
    }
}
