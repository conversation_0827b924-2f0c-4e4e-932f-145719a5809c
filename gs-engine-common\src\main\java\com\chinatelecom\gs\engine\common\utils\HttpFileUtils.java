package com.chinatelecom.gs.engine.common.utils;


import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import jodd.io.FileNameUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import jakarta.servlet.http.HttpServletResponse;
import java.io.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023年12月28日
 */
@Slf4j
public class HttpFileUtils {
    private HttpFileUtils() {
    }

    /**
     * 网络下载文件工具类
     *
     * @param filePath
     * @param downloadFileName 下载的文件名,如果不传默认取filePath中的文件名
     * @param response
     */
    public static void download(String filePath, String downloadFileName, HttpServletResponse response) {
        // 下载文件
        FileInputStream downFileStream = null;
        try {
            log.info("开始下载文件:{}", filePath);
            String finalFileName = UriUtils.encode(StringUtils.defaultIfBlank(downloadFileName, FileNameUtil.getName(filePath)), "UTF-8");
            String disposition = StringUtils.join("attachment;filename=", finalFileName, ";filename*=utf-8''", finalFileName);
            response.setHeader("Content-disposition", disposition);
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            downFileStream = new FileInputStream(filePath);
            IOUtils.copy(downFileStream, response.getOutputStream());
            response.flushBuffer();
            log.info("下载文件完成:{}", filePath);
        } catch (IOException e) {
            log.error("下载文件异常", e);
            BizAssert.throwBizException("B0008", "下载文件异常:{}", e.getMessage());
        } finally {
            if (downFileStream != null) {
                try {
                    downFileStream.close();
                } catch (IOException e) {
                    log.error("关闭流失败", e);
                }
            }
        }
    }

    /**
     * 网络下载文件工具类
     *
     * @param inputStream
     * @param downloadFileName 下载的文件名,如果不传默认取filePath中的文件名
     * @param inline
     * @param response
     */
    public static void download(InputStream inputStream, String downloadFileName, boolean inline, HttpServletResponse response) {
        try {
            String finalFileName = UriUtils.encode(StringUtils.defaultIfBlank(downloadFileName, "unknown"), "UTF-8");
            String dp = inline ? "inline" : "attachment";
            String disposition = StringUtils.join(dp , ";filename=", finalFileName, ";filename*=utf-8''", finalFileName);
            response.setHeader("Content-disposition", disposition);
            if (StringUtils.isBlank(response.getContentType())) {
                response.setContentType("application/octet-stream");
                response.setCharacterEncoding("UTF-8");
            }
            IOUtils.copy(inputStream, response.getOutputStream());
            response.flushBuffer();
        } catch (IOException e) {
            log.warn("下载文件异常,可能是客户端提前关闭连接导致,message:{}", e.getMessage());
        }catch (Exception e) {
            log.error("下载文件异常,", e);
            BizAssert.throwBizException("B0008", "下载文件异常:{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }


    /**
     * File类型转MultipartFile类型，文件名同被转换文件名称一致，如有需要可以拓展方法。
     *
     * @param fileName
     * @param contentType
     * @param data
     * @return
     * @throws IOException
     */
    public static MultipartFile fileToMultipartFile(String fileName, String contentType, byte[] data) throws IOException {
        FileItemFactory factory = new DiskFileItemFactory(1024 * 1024, null);
        FileItem item = factory.createItem("file", contentType, true, fileName);
        OutputStream os = item.getOutputStream();
        IOUtils.copy(new ByteArrayInputStream(data), os);
        return new CommonsMultipartFile(item);
    }

    /**
     * File类型转MultipartFile类型，文件名同被转换文件名称一致，如有需要可以拓展方法。
     *
     * @param fileName
     * @param contentType
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static MultipartFile fileToMultipartFile(String fileName, String contentType, InputStream inputStream) throws IOException {
        FileItemFactory factory = new DiskFileItemFactory(1024 * 1024, null);
        FileItem item = factory.createItem("file", contentType, true, fileName);
        OutputStream os = item.getOutputStream();
        IOUtils.copy(inputStream, os);
        return new CommonsMultipartFile(item);
    }

    /**
     * 下载http文件到服务器本地文件
     * @param httpPath
     * @param localFilePath
     * @throws IOException
     */
    public static void downloadHttpFile2Local(String httpPath, File localFilePath) throws IOException {
        FileUtils.createParentDirectories(localFilePath);
        // 下载文件
        FileOutputStream docFileOutputStream = FileUtils.openOutputStream(localFilePath);
        Request request = new Request.Builder()
                .url(httpPath)
                .addHeader("Connection", "close")
                .build();
        Response response = null;
        try {
            OkHttpClient okHttpClient = SpringContextUtils.getBean(OkHttpClient.class);
            response = okHttpClient.newCall(request).execute();
            IOUtils.copy(response.body().byteStream(), docFileOutputStream);
        } finally {
            IOUtils.closeQuietly(response);
            IOUtils.closeQuietly(docFileOutputStream);
        }
    }


}
