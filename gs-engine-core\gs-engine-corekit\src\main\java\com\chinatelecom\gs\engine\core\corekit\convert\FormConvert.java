package com.chinatelecom.gs.engine.core.corekit.convert;

import com.chinatelecom.gs.engine.core.corekit.domain.po.FormPO;
import com.chinatelecom.gs.engine.core.sdk.request.FormSaveRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.FormVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 表单转换
 * @USER: pengmc1
 * @DATE: 2025/7/21 15:03
 */

@Mapper
public interface FormConvert {
    FormConvert INSTANCE = Mappers.getMapper(FormConvert.class);

    FormPO convert(FormSaveRequest request);

    FormVO convert(FormPO po);
}
