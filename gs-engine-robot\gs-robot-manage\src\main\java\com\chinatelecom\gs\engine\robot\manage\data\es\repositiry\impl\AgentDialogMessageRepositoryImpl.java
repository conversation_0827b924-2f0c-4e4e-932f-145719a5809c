package com.chinatelecom.gs.engine.robot.manage.data.es.repositiry.impl;

import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.es.converter.PoDtoConverter;
import com.chinatelecom.gs.engine.common.es.repositiry.impl.BaseEsOperationRepositoryImpl;
import com.chinatelecom.gs.engine.robot.manage.common.utils.DateUtils;
import com.chinatelecom.gs.engine.robot.manage.data.constant.AgentIndexConstants;
import com.chinatelecom.gs.engine.robot.manage.data.es.converter.AgentDialogMessageEsConverter;
import com.chinatelecom.gs.engine.robot.manage.data.es.dto.AgentDialogMessageEsDTO;
import com.chinatelecom.gs.engine.robot.manage.data.es.po.AgentDialogMessageEsPO;
import com.chinatelecom.gs.engine.robot.manage.data.es.repositiry.AgentDialogMessageEsRepository;
import com.chinatelecom.gs.engine.robot.manage.data.es.service.AgentDialogMessageEsService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.Page;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.PageImpl;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.ByQueryResponse;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AgentDialogMessageRepositoryImpl extends
        BaseEsOperationRepositoryImpl<AgentDialogMessageEsService, AgentDialogMessageEsDTO, AgentDialogMessageEsPO, Long>
        implements
        AgentDialogMessageEsRepository {

    @Resource
    private AgentDialogMessageEsRepository agentDialogMessageEsRepository;

    @Value("${env:dev}")
    private String env;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Override
    public PoDtoConverter<AgentDialogMessageEsDTO, AgentDialogMessageEsPO> converter(){
        return AgentDialogMessageEsConverter.INSTANCE;
    }

    @PostConstruct
    public void init(){
        String index = getIndex();
        boolean exist = super.indexExist(index);
        if(!exist){
            log.info("============================================");
            log.info("index:{} not exist, create index", getIndex());
            super.createIndex(index);
        }
    }

    private String getIndex(){
        return gsGlobalConfig.getSystem().getEsPrefix() + AgentIndexConstants.DIALOG_MESSAGE_INDEX + env;
    }

    @Override
    public void save(AgentDialogMessageEsDTO entity){
        String agentCode = entity.getAgentCode();
        if(ObjectUtils.isEmpty(agentCode)){
            log.info("save bot dialog message error, agentCode is null");
            return;
        }
        String index = getIndex();
        saveOrUpdate(index, entity);
    }

    @Override
    public List<AgentDialogMessageEsDTO> listTopN(String agentCode, String sessionId, Integer limit){
        Query esQuery = buildAgentCodeAndSessionIdQuery(agentCode, sessionId);

        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .withSort(Sort.by(Sort.Direction.DESC, Constants.MESSAGE_TIME))
                .withPageable(PageRequest.of(0, limit))
                .build();

        SearchHits<AgentDialogMessageEsPO> search = super.search(getIndex(), searchQuery);

        return search.stream().map(o->super.convertToDto(o.getContent())).collect(Collectors.toList());
    }

    @Override
    public List<AgentDialogMessageEsDTO> listSendTypeTopN(String robotCode, String sessionId, String messageSend, Integer limit) {
        Query esQuery = buildAgentCodeAndSessionIdAndSenderTypeQuery(robotCode, sessionId, messageSend);

        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .withSort(Sort.by(Sort.Direction.DESC, Constants.MESSAGE_TIME))
                .withPageable(PageRequest.of(0, limit))
                .build();

        SearchHits<AgentDialogMessageEsPO> search = super.search(getIndex(), searchQuery);

        return search.stream().map(o->super.convertToDto(o.getContent())).collect(Collectors.toList());
    }

    @Override
    public Page<AgentDialogMessageEsDTO> queryByPage(String agentCode, String sessionId, Integer page,
                                                     Integer pageSize){
        Query esQuery = buildAgentCodeAndSessionIdQuery(agentCode, sessionId);

        // 分页配置
        PageRequest pageRequest = PageRequest.of(page - 1, pageSize);

        // 构建查询对象
        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .withSort(Sort.by(Sort.Direction.ASC, Constants.MESSAGE_TIME))
                .withPageable(pageRequest)
                .build();

        // 执行查询
        SearchHits<AgentDialogMessageEsPO> searchHits = super.search(getIndex(), searchQuery);

        return convertPage(page, pageSize, searchHits);
    }

    @Override
    public List<AgentDialogMessageEsDTO> listAll(String agentCode, String sessionId) {
        String index = getIndex();

        // 构建查询
        Query esQuery = buildAgentCodeAndSessionIdQuery(agentCode, sessionId);

        // 构建查询对象
        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .withSort(Sort.by(Sort.Direction.ASC, Constants.MESSAGE_TIME))
                .withPageable(PageRequest.of(0, 1000)) // es没传页码，默认查10十条数据, 所以这里设置为1000条
                .build();

        // 执行查询
        SearchHits<AgentDialogMessageEsPO> searchHits = super.search(index, searchQuery);

        // 转换结果
        return Optional.of(searchHits.getSearchHits()).orElse(Collections.emptyList()).stream()
                .map(hit -> convertToDto(hit.getContent())) // 假设 convertToDto 方法将 ES 实体转换为 DTO
                .collect(Collectors.toList());
    }

    @Override
    public List<AgentDialogMessageEsDTO> listByTime(String agentCode, LocalDateTime startTime, LocalDateTime endTime) {
        String index = getIndex();

        // 构建查询
        Map<String, Object> paramMap = new HashMap<>(2);
        TimeRange timeRange = new TimeRange();
        timeRange.setStartTime(startTime);
        timeRange.setEndTime(endTime);
        paramMap.put(Constants.AGENT_CODE, agentCode);
        paramMap.put(Constants.MESSAGE_TIME, timeRange);
        Query esQuery = buildElasticsearchQuery(paramMap);

        // 构建查询对象
        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .withSort(Sort.by(Sort.Direction.DESC, Constants.MESSAGE_TIME))
                .withPageable(PageRequest.of(0, 1000))
                .build();

        log.debug("index:{} ==> 查询参数{}", index, JSON.toJSONString(paramMap));
        //执行查询
        SearchHits<AgentDialogMessageEsPO> searchHits = super.search(index, searchQuery);
        log.debug("查询结果{}", JSON.toJSONString(searchHits));
        // 转换结果
        return Optional.of(searchHits.getSearchHits()).orElse(Collections.emptyList()).stream()
                .map(hit -> convertToDto(hit.getContent())) // 假设 convertToDto 方法将 ES 实体转换为 DTO
                .collect(Collectors.toList());
    }

    @Override
    public List<AgentDialogMessageEsDTO> listByTimeAndPage(LocalDateTime startTime, LocalDateTime endTime, Integer page,
                                                           Integer pageSize){
        String index = getIndex();
        log.info("startTime:{}, endTime:{}, page:{}, pageSize:{}", startTime, endTime, page, pageSize);
        // 构建查询
        Map<String, Object> paramMap = new HashMap<>(2);
        TimeRange timeRange = new TimeRange();
        timeRange.setStartTime(startTime);
        timeRange.setEndTime(endTime);
        paramMap.put(Constants.CREATE_TIME, timeRange);
        Query esQuery = buildCreateTimeQuery(paramMap);

        // 分页配置
        PageRequest pageRequest = PageRequest.of(page - 1, pageSize);

        // 构建查询对象
        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .withSort(Sort.by(Sort.Direction.ASC, Constants.CREATE_TIME))
                .withPageable(pageRequest)
                .build();

        log.debug("index:{} ==> 查询参数{}", index, JSON.toJSONString(paramMap));
        //执行查询
        SearchHits<AgentDialogMessageEsPO> searchHits = super.search(index, searchQuery);
        log.debug("查询结果{}", JSON.toJSONString(searchHits));
        // 转换结果
        return Optional.of(searchHits.getSearchHits()).orElse(Collections.emptyList()).stream()
                .map(hit->convertToDto(hit.getContent())) // 假设 convertToDto 方法将 ES 实体转换为 DTO
                .collect(Collectors.toList());
    }

    private Query buildCreateTimeQuery(Map<String, Object> queryParams){
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        for(Map.Entry<String, Object> entry : queryParams.entrySet()){
            Object value = entry.getValue();
            TimeRange timeRange = (TimeRange) value;
            LocalDateTime startTime = timeRange.getStartTime();
            LocalDateTime endTime = timeRange.getEndTime();
            String startTimeStr = DateUtils.format(startTime);
            String endTimeStr = DateUtils.format(endTime);

            // 使用新的变体语法构建日期范围查询
            boolQueryBuilder.must(RangeQuery.of(r -> r
                .date(d -> d.field(Constants.CREATE_TIME)
                    .gte(startTimeStr)
                    .lte(endTimeStr)
                )
            )._toQuery());
        }
        return boolQueryBuilder.build()._toQuery();
    }

    @Override
    public AgentDialogMessageEsDTO queryByMessageId(String agentCode, String messageId){
        String index = getIndex();
        // 构建查询
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put(Constants.AGENT_CODE, agentCode);
        paramMap.put(Constants.MESSAGE_ID, messageId);

        Query esQuery = buildElasticsearchQuery(paramMap);
        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .build();

        SearchHit<AgentDialogMessageEsPO> searchOneHit = super.searchOne(index, searchQuery);

        if(searchOneHit == null){
            log.warn("no this dialog message {}", messageId);
            return null;
        }

        //获取结果
        return convertToDto(searchOneHit.getContent());
    }

    @Override
    public List<AgentDialogMessageEsDTO> queryByMessageIds(String agentCode, List<String> messageIds){
        String index = getIndex();

        if(CollectionUtils.isEmpty(messageIds)){
            return Collections.emptyList();
        }

        // 构建查询
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put(Constants.AGENT_CODE, agentCode);
        paramMap.put(Constants.MESSAGE_ID, messageIds);
        Query esQuery = buildElasticsearchQuery(paramMap);

        // 构建查询对象
        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .withPageable(PageRequest.of(0, messageIds.size())) // 限制查询结果为单个记录
                .withSort(Sort.by(Sort.Direction.ASC, Constants.CREATE_TIME)) // 添加排序
                .build();

        SearchHits<AgentDialogMessageEsPO> searchHits = super.search(index, searchQuery);

        // 获取并转换结果
        return Optional.of(searchHits.getSearchHits()).orElse(Collections.emptyList()).stream()
                .map(hit->convertToDto(hit.getContent())) // 假设 convertToDto 方法将 ES 实体转换为 DTO
                .collect(Collectors.toList());
    }

    @Override
    public List<AgentDialogMessageEsDTO> queryByUpMessageId(String robotCode, String upMessageIds) {
        String index = getIndex();

        // 构建查询
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        // 添加 agentCode 查询条件
        boolQueryBuilder.must(TermQuery.of(t -> t.field(Constants.AGENT_CODE).value(robotCode))._toQuery());

        // 添加 upMessageIds 或 messageIds 查询条件
        BoolQuery.Builder shouldQueryBuilder = new BoolQuery.Builder()
                .should(TermQuery.of(t -> t.field(Constants.UP_MESSAGE_ID).value(upMessageIds))._toQuery())
                .should(TermQuery.of(t -> t.field(Constants.MESSAGE_ID).value(upMessageIds))._toQuery());
        boolQueryBuilder.must(shouldQueryBuilder.build()._toQuery());

        Query esQuery = boolQueryBuilder.build()._toQuery();

        // 构建查询对象
        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .withPageable(PageRequest.of(0, 100)) // 限制查询结果为单个记录
                .withSort(Sort.by(Sort.Direction.ASC, Constants.CREATE_TIME)) // 添加排序
                .build();

        SearchHits<AgentDialogMessageEsPO> searchHits = super.search(index, searchQuery);

        // 获取并转换结果
        return Optional.of(searchHits.getSearchHits()).orElse(Collections.emptyList()).stream()
                .map(hit -> convertToDto(hit.getContent())) // 假设 convertToDto 方法将 ES 实体转换为 DTO
                .collect(Collectors.toList());
    }

    @Override
    public boolean checkAnswerExist(String senderType, String botCode, LocalDateTime start, LocalDateTime end){
        Query esQuery = buildSenderTypeAndBotCodeQuery(senderType, botCode, start, end);

        // 构建查询对象
        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .withPageable(PageRequest.of(0, 1)) // 限制查询结果为单个记录
                .build();

        long count = super.search(getIndex(), searchQuery).getTotalHits();

        return count > 0;
    }

    @Override
    public Page<AgentDialogMessageEsDTO> queryByPage(String senderType, String botCode, LocalDateTime start,
                                                     LocalDateTime end, Integer page, Integer pageSize){
        // 构建查询
        Query esQuery = buildSenderTypeAndBotCodeQuery(senderType, botCode, start, end);

        // 构建查询对象
        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .withPageable(PageRequest.of(page - 1, pageSize))
                .withSort(Sort.by(Sort.Direction.ASC, Constants.MESSAGE_TIME))
                .build();

        SearchHits<AgentDialogMessageEsPO> searchHits = super.search(getIndex(), searchQuery);
        return convertPage(page, pageSize, searchHits);
    }

    @Override
    public Boolean removeChatLog(String agentCode, String sessionId){
        return this.remove(agentCode, sessionId);
    }

    @Override
    public Boolean removeChatLogList(String agentCode, List<String> sessionIdList){
        return this.remove(agentCode, sessionIdList);
    }

    @Override
    public List<AgentDialogMessageEsDTO> listTopByUpMessageId(String agentCode, String sessionId, Integer limit) {
        // Step 1: 获取所有符合条件的数据
        Query esQuery = buildAgentCodeAndSessionIdQuery(agentCode, sessionId);

        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .build();

        SearchHits<AgentDialogMessageEsPO> searchHits = super.search(getIndex(), searchQuery);

        // 将搜索结果转换为DTO对象列表
        List<AgentDialogMessageEsDTO> allMessages = searchHits.stream()
                .map(hit -> super.convertToDto(hit.getContent()))
                .collect(Collectors.toList());

        // Step 2: 按照 upMessageId 进行分组，并对每组的消息按时间降序排序
        Map<String, List<AgentDialogMessageEsDTO>> groupedMessages = allMessages.stream()
                .collect(Collectors.groupingBy(AgentDialogMessageEsDTO::getUpMessageId));

        // 对每组的消息按时间降序排序
        for (List<AgentDialogMessageEsDTO> group : groupedMessages.values()) {
            group.sort(Comparator.comparing(AgentDialogMessageEsDTO::getMessageTime).reversed());
        }

        // Step 3: 根据每组第一个消息的时间对所有组进行排序，并提取前N个组的数据
        List<List<AgentDialogMessageEsDTO>> sortedGroups;
        if (limit != null && limit > 0) {
            sortedGroups = groupedMessages.values().stream()
                    .sorted((o1, o2) -> o2.get(0).getMessageTime().compareTo(o1.get(0).getMessageTime()))
                    .limit(limit)
                    .collect(Collectors.toList());
        } else {
            sortedGroups = groupedMessages.values().stream()
                    .sorted((o1, o2) -> o2.get(0).getMessageTime().compareTo(o1.get(0).getMessageTime()))
                    .collect(Collectors.toList());
        }
        // Step 4: 将排序后前N个组的所有数据合并成一个列表
        return sortedGroups.stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    private Boolean remove(String agentCode, Object sessionId){
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put(Constants.AGENT_CODE, agentCode);
        paramMap.put(Constants.SESSION_ID, sessionId);
        Query esQuery = buildElasticsearchQuery(paramMap);

        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .build();

        ByQueryResponse delete = super.delete(getIndex(), searchQuery);
        log.info("bot_dialog_message delete count: {}", delete.getDeleted());
        return delete.getDeleted() > 0;
    }

    private Query buildSenderTypeAndBotCodeQuery(String senderType, String botCode, LocalDateTime start,
                                                                 LocalDateTime end){
        // 构建查询
        Map<String, Object> paramMap = new HashMap<>(3);
        TimeRange timeRange = new TimeRange();
        timeRange.setStartTime(start);
        timeRange.setEndTime(end);
        paramMap.put(Constants.AGENT_CODE, botCode);
        paramMap.put(Constants.MESSAGE_TIME, timeRange);
        paramMap.put(Constants.SENDER_TYPE, senderType);

        return buildElasticsearchQuery(paramMap);
    }

    private Page<AgentDialogMessageEsDTO> convertPage(Integer page, Integer pageSize,
                                                      SearchHits<AgentDialogMessageEsPO> searchHits){
        long totalHits = searchHits.getTotalHits();

        if(totalHits == 0){
            return PageImpl.empty();
        }
        // 转换结果
        List<AgentDialogMessageEsDTO> content = searchHits.getSearchHits().stream()
                .map(hit->super.convertToDto(hit.getContent())) // 假设 convertToDto 方法将 ES 实体转换为 DTO
                .collect(Collectors.toList());

        // 返回分页对象
        // 计算总页数
        long totalPages = totalHits / pageSize;
        if(totalHits % pageSize != 0){
            totalPages++;
        }

        return PageImpl.of(page, pageSize, totalHits, totalPages, content);
    }

    private Query buildAgentCodeAndSessionIdQuery(String agentCode, String sessionId){
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put(Constants.AGENT_CODE, agentCode);
        paramMap.put(Constants.SESSION_ID, sessionId);
        return buildElasticsearchQuery(paramMap);
    }

    private Query buildAgentCodeAndSessionIdAndSenderTypeQuery(String agentCode, String sessionId, String senderType){
        Map<String, Object> paramMap = new HashMap<>(3);
        paramMap.put(Constants.AGENT_CODE, agentCode);
        paramMap.put(Constants.SESSION_ID, sessionId);
        paramMap.put(Constants.SENDER_TYPE, senderType);
        return buildElasticsearchQuery(paramMap);
    }

    private Query buildElasticsearchQuery(Map<String, Object> queryParams){
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        for(Map.Entry<String, Object> entry : queryParams.entrySet()){
            String key = entry.getKey();
            Object value = entry.getValue();

            if(value instanceof String string){
                boolQueryBuilder.must(TermQuery.of(t -> t.field(key).value(string))._toQuery());
            }else if(value instanceof Collections){
                @SuppressWarnings("unchecked")
                Collection<String> values = (Collection<String>) value;
                // 对于集合类型，使用多个 term 查询
                for(String val : values) {
                    boolQueryBuilder.must(TermQuery.of(t -> t.field(key).value(val))._toQuery());
                }
            }else if(value instanceof TimeRange timeRange){
                if(Objects.isNull(timeRange.getStartTime()) && Objects.isNull(timeRange.getEndTime())){
                    continue;
                }

                // 使用新的变体语法构建日期范围查询
                if(Objects.nonNull(timeRange.getStartTime())){
                    String startTimeStr = DateUtils.formatWithMillis(timeRange.getStartTime());
                    if(Objects.nonNull(timeRange.getEndTime())){
                        String endTimeStr = DateUtils.formatWithMillis(timeRange.getEndTime());
                        // 使用日期变体语法
                        boolQueryBuilder.must(RangeQuery.of(r -> r
                            .date(d -> d.field(Constants.MESSAGE_TIME)
                                .gte(startTimeStr)
                                .lte(endTimeStr)
                            )
                        )._toQuery());
                    } else {
                        // 只有开始时间
                        boolQueryBuilder.must(RangeQuery.of(r -> r
                            .date(d -> d.field(Constants.MESSAGE_TIME)
                                .gte(startTimeStr)
                            )
                        )._toQuery());
                    }
                } else if(Objects.nonNull(timeRange.getEndTime())){
                    String endTimeStr = DateUtils.formatWithMillis(timeRange.getEndTime());
                    // 只有结束时间
                    boolQueryBuilder.must(RangeQuery.of(r -> r
                        .date(d -> d.field(Constants.MESSAGE_TIME)
                            .lte(endTimeStr)
                        )
                    )._toQuery());
                }
            }
        }

        return boolQueryBuilder.build()._toQuery();
    }

    /**
     * 聊天窗查询消息历史记录
     *
     * @param robotCode
     * @param sessionId
     * @param startTime
     * @param endTime
     * @param page
     * @param pageSize
     * @return
     */
    @Override
    public Page<AgentDialogMessageEsDTO> queryChatMessageByPage(String robotCode, String sessionId,
                                                                LocalDateTime startTime, LocalDateTime endTime, Integer page, Integer pageSize) {

        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put(Constants.AGENT_CODE, robotCode);
        paramMap.put(Constants.SESSION_ID, sessionId);
        TimeRange timeRange = new TimeRange();
        timeRange.setStartTime(startTime);
        timeRange.setEndTime(endTime);
        paramMap.put(Constants.MESSAGE_TIME, timeRange);
        Query esQuery = buildElasticsearchQuery(paramMap);

        // 构建查询对象
        NativeQuery searchQuery = NativeQuery.builder()
                .withQuery(esQuery)
                .withSort(Sort.by(Sort.Direction.DESC, Constants.MESSAGE_TIME)) // 添加排序
                .withPageable(PageRequest.of(page - 1, pageSize)) // 分页配置
                .build();

        // 执行查询
        SearchHits<AgentDialogMessageEsPO> searchHits = super.search(getIndex(), searchQuery);
        return convertPage(page, pageSize, searchHits);
    }

    @Data
    static class TimeRange {

        LocalDateTime startTime;

        LocalDateTime endTime;
    }
}
