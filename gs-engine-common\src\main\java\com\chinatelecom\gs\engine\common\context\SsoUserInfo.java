package com.chinatelecom.gs.engine.common.context;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/13 9:44
 * @desc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SsoUserInfo implements UserInfo, Serializable {

    @Serial
    private static final long serialVersionUID = 8570926206042904905L;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 请求头token
     */
    private String token;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 获取部门名称
     *
     * @return
     */
    private String deptName;

    /**
     * 企业id
     */
    private Long enterpriseId;

    /**
     * 企业名称
     *
     * @return
     */
    private String enterpriseName;

    private String appCode;

}
