package com.chinatelecom.gs.engine.core.manager.search.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.common.infra.base.BaseCodeEntity;
import com.chinatelecom.gs.engine.core.manager.search.AnalyzerTypeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@TableName("search_analyzer_config")
public class SearchAnalyzerConfigPO extends BaseCodeEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private AnalyzerTypeEnum analyzerType;

    private DimensionEnum dimension;

    private String configValue;

    private String description;

    private String businessNo;
}