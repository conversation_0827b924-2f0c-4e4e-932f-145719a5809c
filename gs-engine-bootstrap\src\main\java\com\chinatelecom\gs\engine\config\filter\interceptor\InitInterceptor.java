package com.chinatelecom.gs.engine.config.filter.interceptor;

import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.TeamInfo;
import com.chinatelecom.gs.engine.config.filter.BaseHandlerInterceptor;
import com.chinatelecom.gs.engine.kms.common.MemCache;
import com.chinatelecom.gs.engine.kms.enums.CacheType;
import com.chinatelecom.gs.engine.kms.sdk.enums.AppRoleType;
import com.chinatelecom.gs.engine.kms.sdk.vo.app.AppCreateParam;
import com.chinatelecom.gs.engine.kms.service.AppService;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import java.util.List;

import static com.chinatelecom.gs.engine.common.constants.Constants.COMMON_APP_NAME;
import static com.chinatelecom.gs.engine.common.constants.Constants.TEAM_APP_NAME;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
@Component
@Slf4j
public class InitInterceptor implements BaseHandlerInterceptor {

    @Resource
    private AppService appService;

    @Resource
    private MemCache memCache;

    public boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        try {
            if (RequestContext.get() != null) {

                // 创建租户级别的公共空间
                AppCreateParam appDTO = new AppCreateParam();
                appDTO.setPersonal(false);
                appDTO.setName(COMMON_APP_NAME);
                appDTO.setAppRoles(Lists.newArrayList(AppCreateParam.AppRole.builder().roleType(AppRoleType.PUB).code(RequestContext.getTenantId()).build()));
                createApp(appDTO);

                // 个人空间
//                appDTO = new AppCreateParam();
//                appDTO.setPersonal(true);
//                appDTO.setName(user.getUserId() + PRI_APP_NAME);
//                appDTO.setAppRoles(Lists.newArrayList(AppCreateParam.AppRole.builder().roleType(AppRoleType.PRI).code(RequestContext.getUserId()).build()));
//                createApp(appDTO);

                // 创建团队的公共空间
//                createTeamApp();
            } else {
                log.warn("请求上下文为空，无法执行初始化操作");
            }
        } catch (Exception e) {
            log.error("初始化空间信息失败", e);
        }
        return true;
    }


    private void createApp(AppCreateParam appDTO) {
        Cache<String, Boolean> cache = memCache.getTwoLayerCache(CacheType.APP_NAME, RequestContext.getTenantId());
        cache.get(appDTO.getName(), x -> {
            appService.createIfNotExist(appDTO);
            return true;
        });
    }

    private void createTeamApp() {
        memCache.getCache(CacheType.TEAM_APP_NAME, RequestContext.getUserId(), x -> {
            // 创建团队的公共空间
            List<TeamInfo> teams = RequestContext.getCheckedTeam();

            if (CollectionUtils.isNotEmpty(teams)) {
                for (TeamInfo team : teams) {
                    AppCreateParam appDTO = new AppCreateParam();
                    appDTO.setPersonal(false);
                    appDTO.setName(team.getName() + TEAM_APP_NAME);
                    appDTO.setAppRoles(Lists.newArrayList(AppCreateParam.AppRole.builder().roleType(AppRoleType.TEAM).code(team.getTeamCode()).build()));
                    createApp(appDTO);
                }
            }
            return true;
        });
    }

}
