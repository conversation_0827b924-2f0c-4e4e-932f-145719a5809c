package com.chinatelecom.gs.engine.core.corekit.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * @USER: pengmc1
 * @DATE: 2025/5/20 14:12
 */

@Data
@Schema(description = "埋点事件请求")
public class EventRequest {
    @Schema(description = "埋点唯一ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "jdlsii1223323")
    private String logId;

    @Schema(description = "应用编码", example = "some-app-code")
    private String appCode;

    @Schema(description = "事件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "click")
    private String eventType;

    @Schema(description = "终端：mobile：手机|pc: PC", example = "pc")
    private String terminal;

    @Schema(description = "浏览器UA", example = "chrome xxx")
    private String browser;

    @Schema(description = "Refer", example = "http://127.0.0.1/xxx")
    private String refer;

    @Schema(description = "页面地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "http://127.0.0.1/xxx")
    private String url;

    @Schema(description = "meta信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private Meta meta;

    @Schema(description = "时间戳", requiredMode = Schema.RequiredMode.REQUIRED, example = "123921321213")
    private Long timestamp;

    @Data
    @Schema(description = "Meta信息")
    public static class Meta {
        @Schema(description = "A位，页面", requiredMode = Schema.RequiredMode.REQUIRED, example = "首页")
        private String a;

        @Schema(description = "B位，模块", example = "公告")
        private String b;

        @Schema(description = "C位，按钮", example = "编辑")
        private String c;

        @Schema(description = "参数", example = "{ \"id\": \"1232123\" }")
        private Object params;
    }
}
