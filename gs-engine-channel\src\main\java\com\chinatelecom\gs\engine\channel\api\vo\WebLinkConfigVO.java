package com.chinatelecom.gs.engine.channel.api.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "网页链接渠道配置类")
@Data
public class WebLinkConfigVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "网页链接")
    @NotNull(message = "网页链接不能为空")
    private String url;

    @Schema(description = "共享链接secret")
    @NotBlank(message = "共享链接密码不能为空")
    private String secret;

    //新增secretOn
    @Schema(description = "共享链接密码是否生效")
    private boolean secretOn;
}
