package com.chinatelecom.gs.engine.channel.api.controller.open;


import cn.hutool.core.exceptions.ValidateException;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.channel.api.vo.MessageThumbReq;
import com.chinatelecom.gs.engine.channel.api.vo.ThumbOptionsQueryReq;
import com.chinatelecom.gs.engine.channel.common.constants.ChatConstants;
import com.chinatelecom.gs.engine.channel.manage.OffSiteChannelSwitchCacheService;
import com.chinatelecom.gs.engine.channel.service.messagehandler.ApiMessageAuthCheckService;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.robot.manage.data.service.AgentDialogMessageManageService;
import com.chinatelecom.gs.engine.robot.manage.data.service.AgentDialogService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.ThumbTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.MessageFeedbackRequest;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 点赞点踩、反馈入口
 */
@Slf4j
@Tag(name = "通用API反馈入口")
@RestController
@RequestMapping(value = Constants.CHANNEL_PREFIX + Constants.API_PREFIX + "/feedback")
public class OpenApiFeedbackController {
    /**
     * 无效的签名
     */
    public static final int INVALID_SIGNATURE_CODE = 1001;
    /**
     * 签名为空
     */
    public static final int EMPTY_SIGNATURE_CODE = 1002;

    @Value("${app.agent.thumb.tip:感谢您的反馈和帮助}")
    private String thumbTip;


    @Resource
    private AgentDialogMessageManageService agentDialogMessageManageService;

    @Resource
    private ApiMessageAuthCheckService apiMessageAuthCheckService;

    @Resource
    private OffSiteChannelSwitchCacheService offSiteChannelSwitchCacheService;

    @Resource
    private AgentDialogService agentDialogService;


    @Operation(summary = "点踩选项")
    @PostMapping("/thumb/options")
    @AuditLog(businessType = "通用API反馈入口", operType = "点踩选项", operDesc = "点踩选项", objId="#thumbOptionsQueryReq.agentCode")
    public Result<List<String>> options(@Valid @RequestBody ThumbOptionsQueryReq thumbOptionsQueryReq, HttpServletRequest request) {
        String checkResult = checkTokenOrSign(thumbOptionsQueryReq, request);
        if (StringUtils.isBlank(checkResult)) {
            return Result.success(Collections.emptyList());
        }
        //解析参数
        String thumbType = thumbOptionsQueryReq.getThumbType();
        String downOperation = ThumbTypeEnum.DOWN.getOperation();
        if (downOperation.equalsIgnoreCase(thumbType)) {
            /*MessageFeedbackRequest messageFeedbackRequest = new MessageFeedbackRequest();
            messageFeedbackRequest.setThumbType(downOperation);*/
            // 调用服务，获取结果并返回
            return Result.success(agentDialogMessageManageService.getOptions(downOperation));
        } else {
            return Result.success(Collections.emptyList());
        }
    }

    @Operation(summary = "message点赞点踩")
    @PostMapping("/thumb/set")
    @AuditLog(businessType = "通用API反馈入口", operType = "message点赞点踩", operDesc = "message点赞点踩", objId="#param.agentCode")
    public Result<String> setThumbType(@Valid @RequestBody MessageThumbReq param, HttpServletRequest request) {
        log.info("setThumbType param:{}", JsonUtils.toJsonString(param));
        String checkResult = checkTokenOrSign(param, request);
        if (StringUtils.isBlank(checkResult)) {
            return Result.failed(Result.INVALID_PARAM);
        }
        //解析踩赞类型
        String thumbType = param.getThumbType();
        String thumbTemp;
        if (ThumbTypeEnum.DOWN.getOperation().equalsIgnoreCase(thumbType)) {
            thumbTemp = ThumbTypeEnum.DOWN.getOperation();
        } else if (ThumbTypeEnum.UP.getOperation().equalsIgnoreCase(thumbType)) {
            thumbTemp = ThumbTypeEnum.UP.getOperation();
        } else {
            return Result.failed(Result.INVALID_PARAM);
        }
        //构建请求对象
        List<String> detail = param.getDetail() == null ? Collections.emptyList() : param.getDetail();
        MessageFeedbackRequest messageFeedbackRequest = new MessageFeedbackRequest();
        messageFeedbackRequest.setAgentCode(param.getAgentCode());
        messageFeedbackRequest.setMessageId(param.getMessageId());
        messageFeedbackRequest.setThumbType(thumbTemp);
        messageFeedbackRequest.setRemark(param.getRemark());
        messageFeedbackRequest.setDetail(detail);
        // 调用服务,并返回结果
        String res = agentDialogService.setThumbType(messageFeedbackRequest);
        if (res == null) {
            return Result.failed(Result.INVALID_PARAM, "参数有误");
        } else {
            return Result.success(thumbTip);
        }
    }


    private String checkTokenOrSign(Object dialogRequest, HttpServletRequest request) {
        // 尝试通过accessToken验证
        String accessToken = request.getHeader(HeaderKeys.ACCESS_TOKEN);
        if (StringUtils.isNotBlank(accessToken)) {
            RequestInfo requestInfo = RequestContext.get();
            String channelId = requestInfo.getExtraParamString(ChatConstants.CHANNEL_ID);
            if (StringUtils.isBlank(channelId)) {
                log.warn("API请求被拒绝: 无效的accessToken, URI: {}", request.getRequestURI());
                throw new ValidateException(INVALID_SIGNATURE_CODE, "无效的签名");
            }
            return channelId;
        }

        // 如果accessToken不存在，使用签名验证
        String sign = request.getHeader(HeaderKeys.APP_SIGN);
        String apiKey = request.getHeader(HeaderKeys.APP_SECRET);

        if (StringUtils.isBlank(sign) || StringUtils.isBlank(apiKey)) {
            log.warn("API请求被拒绝: 缺少签名, URI: {}", request.getRequestURI());
            throw new ValidateException(EMPTY_SIGNATURE_CODE, "签名为空");
        }

        // 验证签名
        String signContent = null;
        if (dialogRequest instanceof ThumbOptionsQueryReq tempParam) {
            signContent = apiMessageAuthCheckService.buildSignContent(
                    tempParam.getAgentCode(),
                    tempParam.getContent(),
                    tempParam.getUserId(),
                    tempParam.getRequestTime());
        } else if (dialogRequest instanceof MessageThumbReq tempParam) {
            signContent = apiMessageAuthCheckService.buildSignContent(
                    tempParam.getAgentCode(),
                    tempParam.getContent(),
                    tempParam.getUserId(),
                    tempParam.getRequestTime());
        }

        return apiMessageAuthCheckService.validReChannelId(apiKey, signContent, sign);
    }
}
