package com.chinatelecom.gs.engine.core.entity.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.entity.domain.query.EntityQuery;
import com.chinatelecom.gs.engine.core.entity.domain.request.EntityDeleteRequest;
import com.chinatelecom.gs.engine.core.entity.domain.request.EntityDetailRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.core.entity.service.EntityService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;


/**
 * 实体管理
 *
 * @USER: pengmc1
 * @DATE: 2025/1/20 9:17
 */

@RestController
@Slf4j
@Tag(name = "实体管理")
@PermissionTag(code = {MenuConfig.DIALOG_FLOW})
@RequestMapping({Apis.BASE_PREFIX + Apis.WEB_API + Apis.ENTITY_API})
public class EntityController {

    @Resource
    private EntityService entityService;

    @Operation(summary = "实体分页列表")
    @PlatformRestApi(name = "实体分页列表", groupName = "实体管理")
    @PostMapping("/page")
    @AuditLog(businessType = "实体管理", operType = "实体分页列表", operDesc = "实体分页列表", objId="null")
    public Result<Page<EntityDetailVO>> page(@Validated @RequestBody EntityQuery query) {
        return Result.success(entityService.queryEntityDetailList(query));
    }

    @Operation(summary = "实体添加")
    @PlatformRestApi(name = "实体添加", groupName = "实体管理")
    @PostMapping("/add")
    @AuditLog(businessType = "实体管理", operType = "实体添加", operDesc = "实体添加", objId="#query.entityCode")
    public Result<Boolean> add(@Validated @RequestBody EntityDetailVO query) {
        return Result.success(entityService.addEntity(query));
    }

    @Operation(summary = "实体删除")
    @PlatformRestApi(name = "实体删除", groupName = "实体管理")
    @PostMapping("/delete")
    @AuditLog(businessType = "实体管理", operType = "实体删除", operDesc = "实体删除", objId="null")
    public Result<Boolean> delete(@Validated @RequestBody EntityDeleteRequest query) {
        return Result.success(entityService.delete(query));
    }

    @Operation(summary = "实体修改接口")
    @PlatformRestApi(name = "实体修改", groupName = "实体管理")
    @PostMapping("/update")
    @AuditLog(businessType = "实体管理", operType = "实体修改", operDesc = "实体修改", objId="#query.entityCode")
    public Result<Boolean> update(@Validated @RequestBody EntityDetailVO query) {
        return Result.success(entityService.updateByCode(query));
    }

    @Operation(summary = "实体详情接口")
    @PlatformRestApi(name = "实体详情", groupName = "实体管理")
    @PostMapping("/detail")
    @AuditLog(businessType = "实体管理", operType = "实体详情", operDesc = "实体详情", objId="#query.entityCode")
    public Result<EntityDetailVO> detail(@Validated @RequestBody EntityDetailRequest query) {
        return Result.success(entityService.getEntityDetail(query.getEntityCode()));
    }
}
