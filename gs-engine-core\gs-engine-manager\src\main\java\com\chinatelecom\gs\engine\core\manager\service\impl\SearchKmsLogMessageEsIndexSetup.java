package com.chinatelecom.gs.engine.core.manager.service.impl;

import com.chinatelecom.gs.engine.common.utils.EsPrefixUtils;
import com.chinatelecom.gs.engine.robot.sdk.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class SearchKmsLogMessageEsIndexSetup {

    private static final int NUMBER_OF_SHARDS = 5;
    private static final int NUMBER_OF_REPLICAS = 1;

    @Resource
    private ElasticsearchOperations elasticsearchOperations;


    /**
     * 如果索引不存在，则创建索引
     */
    @PostConstruct
    public void createIndexIfNotExists() {
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME));

            if (!elasticsearchOperations.indexOps(indexCoordinates).exists()) {
                log.info("Elasticsearch index [{}] does not exist, creating now...", EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME));

                Map<String, Object> settings = buildIndexSettings();

                boolean created = elasticsearchOperations.indexOps(indexCoordinates).create(settings);
                if (created) {
                    log.info("Elasticsearch index [{}] created successfully.", EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME));
                } else {
                    log.warn("Failed to create Elasticsearch index [{}].", EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME));
                }
            } else {
                log.info("Elasticsearch index [{}] already exists.", EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME));
            }
        } catch (Exception e) {
            log.error("Error occurred while checking/creating Elasticsearch index: {}", EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME), e);
            throw e;
        }
    }

    /**
     * 构建索引设置
     */
    private Map<String, Object> buildIndexSettings() {
        HashMap<String, Object> setting = new HashMap<>();
        setting.put("index.number_of_shards", NUMBER_OF_SHARDS);
        setting.put("index.number_of_replicas", NUMBER_OF_REPLICAS);
        return setting;
    }
}
