package com.chinatelecom.gs.engine.config.filter.interceptor;

import cn.hutool.core.util.StrUtil;
import com.chinatelecom.gs.engine.channel.common.constants.ChatConstants;
import com.chinatelecom.gs.engine.channel.manage.ChannelSecretManagerService;
import com.chinatelecom.gs.engine.channel.manage.WebLinkConfigService;
import com.chinatelecom.gs.engine.channel.service.dto.AccessTokenData;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelecom.gs.engine.config.filter.BaseHandlerInterceptor;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@Component
public class ApiContextInterceptor implements BaseHandlerInterceptor {

    @Resource
    private ChannelSecretManagerService channelSecretManagerService;

    @Autowired
    private WebLinkConfigService webLinkConfigService;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    @Override
    public boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        // 过滤掉channel/openapi/file/download接口
        String requestURI = ((HttpServletRequest) request).getRequestURI();
        if (requestURI.startsWith("/ais/channel/openapi/file/download")) {
            return true;
        }
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse resp = (HttpServletResponse) response;
        String method = req.getMethod();

        // 1. 验证用户ID
        String userId = req.getHeader(HeaderKeys.USER_ID);
        if (StrUtil.isBlank(userId)) {
            log.warn("API request rejected: Missing X-UserId header, URI: {}, Method: {}", requestURI, method);
            try {
                return InterceptorUtils.writeError(resp, "A0023", "X-UserId请求头不能为空");
            } catch (IOException e) {
                log.error("Failed to write error response for missing X-UserId, URI: {}, Method: {}, Error: {}",
                        requestURI, method, e.getMessage(), e);
                return false;
            }
        }

        // 2. 尝试通过 AccessToken 验证
        String accessToken = req.getHeader(HeaderKeys.ACCESS_TOKEN);
        if (StringUtils.hasText(accessToken)) {
            return validateWithAccessToken(req, accessToken, userId, requestURI, method, resp);
        }

        // 3. 退回到 App-Sign 和 App-Secret 验证
        return validateWithAppSecret(req, resp, userId, requestURI, method);
    }

    /**
     * 通过 AccessToken 进行验证并设置请求上下文
     */
    private boolean validateWithAccessToken(HttpServletRequest request, String accessToken, String userId, String requestURI, String method, HttpServletResponse response) {
        try {
            // 验证 AccessToken
            AccessTokenData accessTokenData = webLinkConfigService.validateAndGetTokenData(accessToken, userId);
            if (accessTokenData == null) {
                log.warn("API request rejected: Invalid accessToken, URI: {}, Method: {}", requestURI, method);
                return InterceptorUtils.writeError(response, "A0028", "AccessToken无效或已过期");
            }

            // 设置请求上下文
            RequestInfo requestInfo = new RequestInfo();
            requestInfo.setAppCode(accessTokenData.getAppCode());
            requestInfo.setTenantId(accessTokenData.getTenantId());
            requestInfo.setUserId(userId); // 使用请求中的userId
            requestInfo.putExtraParam(ChatConstants.CHANNEL_ID, accessTokenData.getChannelId());
            requestInfo.putExtraParam(ChatConstants.APP_ID, accessTokenData.getAppId());
            requestInfo.putExtraParam(ChatConstants.OPENAPI_SOURCE, ChatConstants.OPENAPI_SOURCE);
            requestInfo.setIsSuperTenant(superTenant.equals(accessTokenData.getTenantId()));
            InterceptorUtils.setSourceTypeByRpc(request, requestInfo);
            RequestContext.set(requestInfo);
            log.debug("API request authenticated via accessToken, URI: {}, Method: {}, accessTokenData: {}",
                    requestURI, method, accessTokenData);
            return true;
        } catch (Exception e) {
            log.error("Error validating accessToken, URI: {}, Method: {}, Error: {}",
                    requestURI, method, e.getMessage(), e);
            try {
                return InterceptorUtils.writeError(response, "A0029", "AccessToken验证过程发生错误");
            } catch (IOException ioe) {
                log.error("Failed to write error response for accessToken validation, URI: {}, Method: {}, Error: {}",
                        requestURI, method, ioe.getMessage(), ioe);
                return false;
            }
        }
    }

    /**
     * 通过 App-Sign 和 App-Secret 进行验证并设置请求上下文
     */
    private boolean validateWithAppSecret(HttpServletRequest request, HttpServletResponse response,
                                          String userId, String requestURI, String method) {
        // 验证请求头
        String appSign = request.getHeader(HeaderKeys.APP_SIGN);
        String appSecret = request.getHeader(HeaderKeys.APP_SECRET);

        if (StrUtil.isBlank(appSign)) {
            log.warn("API request rejected: Missing App-Sign header, URI: {}, Method: {}", requestURI, method);
            try {
                return InterceptorUtils.writeError(response, "A0026", "App-Sign请求头不能为空");
            } catch (IOException e) {
                log.error("Failed to write error response for missing App-Sign, URI: {}, Method: {}, Error: {}",
                        requestURI, method, e.getMessage(), e);
                return false;
            }
        }

        if (StrUtil.isBlank(appSecret)) {
            log.warn("API request rejected: Missing App-Secret header, URI: {}, Method: {}", requestURI, method);
            try {
                return InterceptorUtils.writeError(response, "A0027", "App-Secret请求头不能为空");
            } catch (IOException e) {
                log.error("Failed to write error response for missing App-Secret, URI: {}, Method: {}, Error: {}",
                        requestURI, method, e.getMessage(), e);
                return false;
            }
        }

        try {
            // 验证密钥
            ChannelApiSecretDTO dto = channelSecretManagerService.getSecretWithSecretId(appSecret);
            if (dto == null) {
                log.warn("API request rejected: Invalid App-Secret, URI: {}, Method: {}, SecretId: {}",
                        requestURI, method, appSecret);
                try {
                    return InterceptorUtils.writeError(response, "A0026", "密钥不存在，验签失败");
                } catch (IOException ioe) {
                    log.error("Failed to write error response for invalid App-Secret, URI: {}, Method: {}, Error: {}",
                            requestURI, method, ioe.getMessage(), ioe);
                    return false;
                }
            }

            // 设置请求上下文
            RequestInfo requestInfo = new RequestInfo();
            requestInfo.setAppCode(dto.getAppCode());
            requestInfo.setTenantId(dto.getTenantId());
            requestInfo.setUserId(userId); // 使用请求中的userId，而不是dto.getCreateId()
            requestInfo.putExtraParam(ChatConstants.CHANNEL_ID, dto.getChannelId());
            InterceptorUtils.setSourceTypeByRpc(request, requestInfo);
            RequestContext.set(requestInfo);


            log.debug("API request authenticated via App-Secret, URI: {}, Method: {}, AppCode: {}",
                    requestURI, method, dto.getAppCode());
            return true;
        } catch (Exception e) {
            log.error("Error validating App-Secret, URI: {}, Method: {}, Error: {}",
                    requestURI, method, e.getMessage(), e);
            try {
                return InterceptorUtils.writeError(response, "A0030", "密钥验证过程发生错误");
            } catch (IOException ioe) {
                log.error("Failed to write error response for App-Secret validation, URI: {}, Method: {}, Error: {}",
                        requestURI, method, ioe.getMessage(), ioe);
                return false;
            }
        }
    }

    /**
     * 完成后调用
     *
     * @param request
     * @param response
     * @param ex
     * @throws Exception
     */
    @Override
    public void afterCompletion(ServletRequest request, ServletResponse response, Exception ex) throws Exception {
        RequestContext.remove();
    }
}
