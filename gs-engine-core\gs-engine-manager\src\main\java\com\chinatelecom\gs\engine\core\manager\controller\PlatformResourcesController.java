package com.chinatelecom.gs.engine.core.manager.controller;

import cn.hutool.core.bean.BeanUtil;
import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.cloud.platform.client.config.PlatformSsoProperties;
import com.chinatelecom.cloud.platform.client.rpc.Menu;
import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.util.AuthUtils;
import com.chinatelecom.cloud.platform.client.util.LogoutUtils;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.manager.service.PermissionService;
import com.chinatelecom.gs.engine.core.manager.vo.PlatformUserInfo;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


@RestController
@Slf4j
@Tag(name = "账号资源接口")
@RequestMapping({Apis.BASE_PREFIX + Apis.WEB_API + Apis.PLATFORM})
@Deprecated
public class PlatformResourcesController {

    @Value("${platform.super.tenant:botSuper}")
    private String superTenant;

    @Resource
    private PlatformSsoProperties platformSsoProperties;

    @Resource
    private PermissionService permissionService;

    @GetMapping("/menu")
    @ResponseBody
    @Operation(summary = "获取菜单信息", tags = "获取菜单信息")
    @PlatformRestApi(name = "获取菜单信息", groupName = "账号资源管理")
    @AuditLog(businessType = "账号资源接口", operType = "获取菜单信息", operDesc = "获取菜单信息", objId="null")
    Result<List<Menu>> menu(HttpServletResponse response) {
        PlatformUser user = SsoUtil.get();
        BaseResult<List<Menu>> result = AuthUtils.getMenuList(user.getCorpCode(), user.getUserId());

        if (result.getCode() != BaseResult.SUCCESS) {
            throw new BizException("CA001", "查询菜单列表失败");
        }
        result.setData(permissionService.filterMenu(result.getData()));


        return Result.success(result.getData());
    }

//    @GetMapping("/resource")
//    @ResponseBody
//    @Operation(summary = "获取前端资源信息", tags = "获取前端资源信息,permissionType 0:接口 2：web资源")
//    @PlatformRestApi(name = "获取前端资源信息", groupName = "账号资源管理")
//    @AuditLog(businessType = "账号资源接口", operType = "获取前端资源信息", operDesc = "获取前端资源信息", objId="null")
//    Result<List<Permission>> resource(HttpServletResponse response) {
//        PlatformUser user = SsoUtil.get();
//        BaseResult<List<Permission>> result =
//                AuthUtils.getResourceList(user.getCorpCode(), user.getUserId(), "2");
//
//        if (result.getCode() != BaseResult.SUCCESS) {
//            throw new BizException("CA002", "查询按钮列表失败");
//        }
//        result.setData(permissionService.filterResource(result.getData()));
//        return Result.success(result.getData());
//    }

    @GetMapping("/user")
    @ResponseBody
    @Operation(summary = "获取用户信息", tags = "获取用户信息")
    @PlatformRestApi(name = "获取用户信息", groupName = "账号资源管理")
    @AuditLog(businessType = "账号资源接口", operType = "获取用户信息", operDesc = "获取用户信息", objId="null")
    Result<PlatformUserInfo> user() {
        PlatformUser user = SsoUtil.get();
        PlatformUserInfo platformUserInfo = new PlatformUserInfo();
        BeanUtil.copyProperties(user, platformUserInfo);
        platformUserInfo.setIsBotSuper(Boolean.TRUE.equals(superTenant.equals(RequestContext.getAppCode())));
        return Result.success(platformUserInfo);
    }

    @Operation(summary = "退出登录")
    @PlatformRestApi(name = "退出登录", groupName = "账号资源管理")
    @GetMapping("/logout")
    @AuditLog(businessType = "账号资源接口", operType = "退出登录", operDesc = "退出登录", objId="null")
    public void logout(HttpServletResponse response, HttpServletRequest request) throws IOException {
        response.setStatus(HttpStatus.MOVED_TEMPORARILY.value());
        response.sendRedirect(LogoutUtils.getLogoutUrl(platformSsoProperties.getServerUrl(),
                platformSsoProperties.getAppUrl()));

    }

    @Operation(summary = "获取授权跳转地址")
    @PlatformRestApi(name = "获取授权跳转地址", groupName = "账号资源管理")
    @GetMapping("/address")
    @ResponseBody
    @AuditLog(businessType = "账号资源接口", operType = "获取授权跳转地址", operDesc = "获取授权跳转地址", objId="null")
    public Result<String> address() {
        return Result.success(platformSsoProperties.getServerUrl());
    }

}
