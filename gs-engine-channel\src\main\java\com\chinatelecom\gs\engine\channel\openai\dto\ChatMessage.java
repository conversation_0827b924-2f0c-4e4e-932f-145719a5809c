package com.chinatelecom.gs.engine.channel.openai.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * OpenAI Chat Message 格式
 */
@Data
public class ChatMessage {
    /**
     * 消息角色：system, user, assistant, function
     */
    @NotBlank(message = "角色不能为空")
    @Schema(description = "消息发送者的角色。必须是以下之一：消息角色：system, user, assistant, function")
    private String role;

    /**
     * 消息内容
     */
    @Schema(description = "消息的内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    /**
     * 名称，当role为function时使用
     */
    @Schema(description = "函数名称，当角色(role)为function时必须提供")
    private String name;

    /**
     * 推理内容，对应bot的reasoningContent字段
     */
    @Schema(description = "推理过程的内容，对应bot的reasoningContent字段")
    private String reasoning_content;
}
