package com.chinatelecom.gs.engine.core.corekit.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * @author: Wei
 * @date: 2025-02-05 10:10
 */
@Schema(description = "指令管理保存参数")
@Data
public class RobotAiInstructRequest {

    /**
     * 指令Id
     */
    @Schema(description = "指令Id, 编辑时必传")
    private Long id;

    /**
     * 指令标识
     */
    @Schema(description = "指令标识")
    @NotBlank(message = "指令标识不能为空")
    private String instructCode;

    /**
     * 指令描述
     */
    @Schema(description = "指令描述")
    private String instructDesc;

    /**
     * 指令参数（Json数组字符串）
     */
    @Schema(description = "指令参数")
    @Valid
    private List<InstructParam> instructParams;

    @Data
    public static class InstructParam{

        /**
         * 参数名称
         */
        @Schema(description = "参数名称")
        @NotBlank(message = "参数名称不能为空")
        private String paramName;

        /**
         * 参数默认值
         */
        @Schema(description = "参数默认值")
        private String defaultValue;

        /**
         * 参数描述
         */
        @Schema(description = "参数描述")
        private String paramDesc;

    }

}
