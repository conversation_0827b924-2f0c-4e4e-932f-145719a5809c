package com.chinatelecom.gs.engine.core.corekit.common.core.builder;

import com.chinatelecom.gs.engine.core.corekit.common.core.tts.TextSpeechService;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.ContentTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.Answer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.entry.RichTextOptionCard;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.request.AnswerBuildRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.speech.Speech;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Objects;

/**
 * @USER: pengmc1
 * @DATE: 2025/6/12 16:51
 */

@Component
public class CardOptionAnswerBuilder implements AnswerBuilder{

    @Resource
    private TextSpeechService textSpeechService;

    @Override
    public boolean valid(AnswerBuildRequest answerBuildRequest) {
        return answerBuildRequest.getAnswerTypeEnum().equals(AnswerTypeEnum.CARD_OPTION);
    }

    @Override
    public Answer build(AnswerBuildRequest answerBuildRequest) {
        Answer answer = new Answer();
        if (Objects.nonNull(answerBuildRequest.getContent())) {
            RichTextOptionCard richTextFoldCard = (RichTextOptionCard)answerBuildRequest.getContent();
            answer.setContent(richTextFoldCard);
            answer.setAnswerType(AnswerTypeEnum.CARD_OPTION);
            answer.setContentType(ContentTypeEnum.ALL.getCode());
            answer.setNamespace("com.card.option");
            answer.setVersion(answer.getAnswerType().getVersion());
            Speech speech = this.textSpeechService.genSpeech(richTextFoldCard.getShowCard().getHtml());
            if(Objects.nonNull(speech)){
                speech.setEnableSmartInterruption(answerBuildRequest.getEnableSmartInterruption());
            }
            answer.setSpeech(speech);
        }
        answer.setInstructions(answerBuildRequest.getInstructions());
        return answer;
    }
}
