package com.chinatelecom.gs.engine.core.entity.domain.request;

import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityVO;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
import lombok.Data;

/**
 * @USER: pengmc1
 * @DATE: 2025/1/23 9:41
 */

@Data
public class EntityValidatorRequest {
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 跟踪ID
     */
    private String sessionId;
    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 当前用户query
     */
    private String query;
    /**
     * 实体列表
     */
    private Entity entity;
    /**
     * 实体元数据
     */
    private EntityVO entityVO;
}
