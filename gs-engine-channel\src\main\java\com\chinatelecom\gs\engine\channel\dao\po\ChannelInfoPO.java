package com.chinatelecom.gs.engine.channel.dao.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/22 14:00
 * @description
 */
@Setter
@Getter
@TableName("channel_info")
public class ChannelInfoPO extends BasePO implements Serializable {
    @Serial
    private static final long serialVersionUID = 3608436838996520240L;

    @TableField("channel_name")
    private String channelName;

    @TableField("channel_type")
    private ChannelTypeEnum channelType;

    @TableField("channel_id")
    private String channelId;

    /**
     * 机器人code
     */
    @TableField("agent_code")
    private String agentCode;

    /**
     * 渠道是否启用
     */
    @TableField("enable")
    private boolean enable;

    @Override
    public String toString() {
        return "ChannelInfoPO{" +
                "channelName='" + channelName + '\'' +
                ", channelType=" + channelType +
                ", channelId='" + channelId + '\'' +
                ", robotCode='" + agentCode + '\'' +
                ", enable=" + enable +
                '}';
    }
}
