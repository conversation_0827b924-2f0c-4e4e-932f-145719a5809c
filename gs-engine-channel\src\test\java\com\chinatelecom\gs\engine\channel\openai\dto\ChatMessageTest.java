package com.chinatelecom.gs.engine.channel.openai.dto;
import jakarta.validation.ConstraintViolation; import jakarta.validation.Validation; import jakarta.validation.Validator; import jakarta.validation.ValidatorFactory; import org.junit.jupiter.api.BeforeEach; import org.junit.jupiter.api.Test;
import java.util.Set;
import static org.assertj.core.api.Assertions.assertThat;
public class ChatMessageTest {
    private ChatMessage chatMessage;
    private Validator validator;

    @BeforeEach
    public void setUp() {
        chatMessage = new ChatMessage();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    public void testDefaultValues() {
        // Arrange & Act

        // Assert
        assertThat(chatMessage.getRole()).isNull();
        assertThat(chatMessage.getContent()).isNull();
        assertThat(chatMessage.getName()).isNull();
        assertThat(chatMessage.getReasoning_content()).isNull();
    }

    @Test
    public void testSettersAndGetters() {
        // Arrange
        String role = "user";
        String content = "Hello, world!";
        String name = "assistant_name";
        String reasoningContent = "This is a reasoning content.";

        // Act
        chatMessage.setRole(role);
        chatMessage.setContent(content);
        chatMessage.setName(name);
        chatMessage.setReasoning_content(reasoningContent);

        // Assert
        assertThat(chatMessage.getRole()).isEqualTo(role);
        assertThat(chatMessage.getContent()).isEqualTo(content);
        assertThat(chatMessage.getName()).isEqualTo(name);
        assertThat(chatMessage.getReasoning_content()).isEqualTo(reasoningContent);
    }

    @Test
    public void testValidation() {
        // Arrange
        Set<ConstraintViolation<ChatMessage>> violations;

        // Test with null role
        chatMessage.setRole(null);
        violations = validator.validate(chatMessage);
        assertThat(violations).isNotEmpty().hasSize(1)
                .extracting("message").containsOnly("角色不能为空");

        // Test with empty role
        chatMessage.setRole("");
        violations = validator.validate(chatMessage);
        assertThat(violations).isNotEmpty().hasSize(1)
                .extracting("message").containsOnly("角色不能为空");
    }
}