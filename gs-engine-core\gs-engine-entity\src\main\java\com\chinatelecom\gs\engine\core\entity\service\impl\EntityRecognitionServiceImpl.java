package com.chinatelecom.gs.engine.core.entity.service.impl;

import com.alibaba.fastjson2.JSON;
import com.chinatelecom.gs.engine.core.entity.ability.recognition.EntityRecognitionAbilityHolder;
import com.chinatelecom.gs.engine.core.entity.ability.recognition.EntityRecognitionAbilityService;
import com.chinatelecom.gs.engine.core.entity.ability.validator.EntityValidatorAbilityHolder;
import com.chinatelecom.gs.engine.core.entity.ability.validator.EntityValidatorAbilityService;
import com.chinatelecom.gs.engine.core.entity.common.utils.ParallelUtils;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.core.entity.domain.request.EntityValidatorRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
import com.chinatelecom.gs.engine.core.entity.service.EntityRecognitionService;
import com.chinatelecom.gs.engine.core.entity.service.EntityService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @USER: pengmc1
 * @DATE: 2025/1/16 19:26
 */

@Slf4j
@Service
public class EntityRecognitionServiceImpl implements EntityRecognitionService {

    @Resource
    private EntityService entityService;

    @Resource
    private EntityRecognitionAbilityHolder entityRecognitionAbilityHolder;

    @Resource
    private EntityValidatorAbilityHolder entityValidatorAbilityHolder;

    @Resource
    private ExecutorService commonExecutorService;

    /**
     * 实体识别
     *
     * @param request
     * @return
     */
    @Override
    public EntityRecognitionResponse predict(EntityRecognitionRequest request) {
        List<EntityVO> entityVOS = entityService.getEntityList(request.getEntityCodes());
        //按识别类型进行分组
//        Map<String, List<EntityVO>> entityAbilityMap = entityGroup(entityVOS);
        //并发进行实体识别
        List<Entity> entityList = parallelRecognition(request, entityVOS);
        //对识别进行进行校验
        List<Entity> finalEntityList = entityValidator(request, entityVOS, entityList);
        EntityRecognitionResponse recognitionResponse = new EntityRecognitionResponse();
        recognitionResponse.setEntities(finalEntityList);
        return recognitionResponse;
    }

    /**
     * 按识别类型进行分组
     * 规则识别： 能力编码
     * 前缀树识别： 能力编码
     * 小模型识别： 能力编码 + 模型编码
     * 大模型识别： 能力编码 + 大模型编码
     * 插件识别： 能力编码
     * @return
     */
    protected Map<String, List<EntityVO>> entityGroup(List<EntityVO> entityVOS){
        Map<String, List<EntityVO>> entityAbilityMap = Maps.newHashMap();
        entityVOS.stream().forEach( entityVO -> {
            EntityAbilityEnum abilityEnum = entityVO.getAbility();
            if(EntityAbilityEnum.REGEX_MATCH.getCode().equals(abilityEnum.getCode())){
                //规则匹配
                String abilityKey = EntityAbilityEnum.REGEX_MATCH.getCode();
                if(!entityAbilityMap.containsKey(abilityKey)){
                    entityAbilityMap.put(abilityKey, new ArrayList<>());
                }
                entityAbilityMap.get(abilityKey).add(entityVO);
            }else if(EntityAbilityEnum.TRIE_MATCH.getCode().equals(abilityEnum.getCode())){
                //前缀树匹配
                String abilityKey = EntityAbilityEnum.TRIE_MATCH.getCode();
                if(!entityAbilityMap.containsKey(abilityKey)){
                    entityAbilityMap.put(abilityKey, new ArrayList<>());
                }
                entityAbilityMap.get(abilityKey).add(entityVO);
            }else if(EntityAbilityEnum.BIG_MODEL_RECOGNITION.getCode().equals(abilityEnum.getCode())){
                //大模型识别
                String abilityKey = EntityAbilityEnum.BIG_MODEL_RECOGNITION.getCode();
                if(!entityAbilityMap.containsKey(abilityKey)){
                    entityAbilityMap.put(abilityKey, new ArrayList<>());
                }
                entityAbilityMap.get(abilityKey).add(entityVO);
            }else if(EntityAbilityEnum.MODEL_RECOGNITION.getCode().equals(abilityEnum.getCode())){
                //模型识别
                String abilityKey = EntityAbilityEnum.MODEL_RECOGNITION.getCode() + ":" + entityVO.getAbilityConfig().getModelCode();
                if(!entityAbilityMap.containsKey(abilityKey)){
                    entityAbilityMap.put(abilityKey, new ArrayList<>());
                }
                entityAbilityMap.get(abilityKey).add(entityVO);
            }else if(EntityAbilityEnum.PLUGIN_RECOGNITION.getCode().equals(abilityEnum.getCode())){
                //插件识别
                String abilityKey = EntityAbilityEnum.PLUGIN_RECOGNITION.getCode();
                if(!entityAbilityMap.containsKey(abilityKey)){
                    entityAbilityMap.put(abilityKey, new ArrayList<>());
                }
                entityAbilityMap.get(abilityKey).add(entityVO);
            }
        });
        return entityAbilityMap;
    }

    /**
     * 并发进行实体识别
     * @param request
     * @return
     */
    protected List<Entity> parallelRecognition(EntityRecognitionRequest request, List<EntityVO> entityVOS){
        List<Entity> entityList = ParallelUtils.parallelExecute(commonExecutorService, entityVOS , 60000L, entity -> {
            Set<String> abilityCodes = entity.getAbilityConfigMap().keySet();
            for (String ability : abilityCodes) {
                if(EntityAbilityEnum.MODEL_RECOGNITION.getCode().equals(ability)){
                    //模型识别
                    ability = EntityAbilityEnum.MODEL_RECOGNITION.getCode() + ":" + entity.getAbilityConfigMap().get(ability).getModelCode();
                }
                EntityRecognitionAbilityService entityRecognitionAbilityService = entityRecognitionAbilityHolder.getAbilityService(ability);
                EntityRecognitionRequest recognitionRequest = new EntityRecognitionRequest();
                recognitionRequest.setSessionId(request.getSessionId());
                recognitionRequest.setMessageId(request.getMessageId());
                recognitionRequest.setTenantId(request.getTenantId());
                recognitionRequest.setQuery(request.getQuery());
                List<String> entityCodes = Collections.singletonList(entity.getEntityCode());
                recognitionRequest.setEntityCodes(entityCodes);
                List<Entity> predictRes = entityRecognitionAbilityService.predict(recognitionRequest);
                if (CollectionUtils.isNotEmpty(predictRes)) {
                    log.info("【实体识别】能力: {}, 实体编码: {}, 最终识别到结果：{}", ability, entity.getEntityCode(), JSON.toJSONString(predictRes));
                    return predictRes;
                }
            }
            return null;
        });
        return entityList;
    }

    /**
     * 进行实体校验
     * @param entityVOS
     * @param entityList
     * @return
     */
    protected List<Entity> entityValidator(EntityRecognitionRequest request, List<EntityVO> entityVOS, List<Entity> entityList){
        if(CollectionUtils.isEmpty(entityList)){
            return entityList;
        }
        Map<String, EntityVO> entityVOMap = entityVOS.stream().collect(Collectors.toMap(EntityVO::getEntityCode, Function.identity(),(k1,k2) -> k1));
        List<Entity> finalEntityList = ParallelUtils.parallelExecute(commonExecutorService, entityList, 60000L, entity -> {
            EntityVO entityVO = entityVOMap.get(entity.getEntityCode());
            //开启校验
            if(entityVO.getValidatorSwitch()){
                EntityValidatorAbilityService entityValidatorAbilityService = entityValidatorAbilityHolder.getValidatorAbilityService(entityVO.getValidatorType());
                EntityValidatorRequest validatorRequest = new EntityValidatorRequest();
                validatorRequest.setSessionId(request.getSessionId());
                validatorRequest.setMessageId(request.getMessageId());
                validatorRequest.setQuery(request.getQuery());
                validatorRequest.setEntityVO(entityVO);
                validatorRequest.setEntity(entity);
                Boolean validator = entityValidatorAbilityService.validator(validatorRequest);
                if(validator){
                    return Arrays.asList(entity);
                }
            }else{
                return Arrays.asList(entity);
            }
            return null;
        });
        return finalEntityList;
    }
}
