package com.chinatelecom.gs.engine.channel.service.dto;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/16 14:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseSendMessageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7665260654680742797L;

    /**
     * 接受消息的成员
     */
    private String touser;

    /**
     * 消息类型
     */
    private String msgtype;

    /**
     * 企业应用的id
     */
    private Integer agentid;

    /**
     * 是否是保密消息，0表示可对外分享，1表示不能分享且内容显示水印，默认为0
     */
    private Integer safe;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
