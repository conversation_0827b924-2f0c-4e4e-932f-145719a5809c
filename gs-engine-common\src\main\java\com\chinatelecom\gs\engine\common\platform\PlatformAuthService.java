package com.chinatelecom.gs.engine.common.platform;

import com.chinatelecom.cloud.platform.client.rpc.Permission;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class PlatformAuthService {

    @Resource
    private GovernmentAuthClient governmentAuthClient;

    @Resource
    private KsAuthClient ksAuthClient;


    public List<String> getResourceList() {
        AppSourceType appSourceType = RequestContext.getAppSourceType();
        if (appSourceType == AppSourceType.KS) {
            Result<List<Permission>> resourceList = ksAuthClient.getResourceList();
            if (Objects.nonNull(resourceList) && Objects.nonNull(resourceList.getData())) {
                return resourceList.getData().stream().map(Permission::getPermissionCode).collect(Collectors.toList());
            }

        } else {
            AppOwnerRequest appOwnerRequest = new AppOwnerRequest();
            appOwnerRequest.setCorpCode(RequestContext.getTenantId());
            appOwnerRequest.setUserId(RequestContext.getUserId());
            Result<List<String>> resourceList = governmentAuthClient.getResourceList(appOwnerRequest);
            if (Objects.nonNull(resourceList) && Objects.nonNull(resourceList.getData())) {
                return resourceList.getData();
            }
        }
        return Collections.EMPTY_LIST;
    }
}
