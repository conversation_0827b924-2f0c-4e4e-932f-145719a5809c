package com.chinatelecom.gs.engine.channel.foundation.platformanswer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.channel.common.AccessTokenUtil;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelMediaRepository;
import com.chinatelecom.gs.engine.channel.service.dto.BaseSendMessageDTO;
import com.chinatelecom.gs.engine.channel.service.dto.TextMessageDTO;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.BotAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.entry.RichTextCard;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;


public class PlatformAnswerTransformerTest {
    @Mock
    private AccessTokenUtil tokenUtil;

    @Mock
    private ChannelMediaRepository channelMediaRepository;

    @InjectMocks
    private PlatformAnswerTransformer platformAnswerTransformer;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testConvertCsrobotAnswerWithSimpleRichText() {
        // Arrange
        BotAnswer botAnswer = new BotAnswer();
        botAnswer.setAnswerType(AnswerTypeEnum.SIMPLE_RICH_TEXT);
        RichTextCard richTextCard = new RichTextCard();
        richTextCard.setHtml("Hello World");
        botAnswer.setContent(JSON.toJSONString(richTextCard));

        List<BotAnswer> answers = new ArrayList<>();
        answers.add(botAnswer);

        String userId = "user1";
        Integer agentId = 1;
        String channelId = "channel1";
        String corpId = "corp1";

        when(channelMediaRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Act
        List<BaseSendMessageDTO> result = platformAnswerTransformer.convertCsrobotAnswer(answers, userId, agentId, channelId, corpId);

        // Assert
        assertEquals(1, result.size());
        assertTrue(result.get(0) instanceof TextMessageDTO);
        TextMessageDTO textMessageDTO = (TextMessageDTO) result.get(0);
        assertEquals("Hello World", textMessageDTO.getText().getString("content"));
    }

    @Test
    public void testConvertCsrobotAnswerWithPlainText() {
        // Arrange
        BotAnswer botAnswer = new BotAnswer();
        botAnswer.setAnswerType(AnswerTypeEnum.PLAIN_TEXT);
        botAnswer.setContent("Hello Plain Text");

        List<BotAnswer> answers = new ArrayList<>();
        answers.add(botAnswer);

        String userId = "user1";
        Integer agentId = 1;
        String channelId = "channel1";
        String corpId = "corp1";

        // Act
        List<BaseSendMessageDTO> result = platformAnswerTransformer.convertCsrobotAnswer(answers, userId, agentId, channelId, corpId);

        // Assert
        assertEquals(1, result.size());
        assertTrue(result.get(0) instanceof TextMessageDTO);
        TextMessageDTO textMessageDTO = (TextMessageDTO) result.get(0);
        assertEquals("Hello Plain Text", textMessageDTO.getText().getString("content"));
    }

    @Test
    public void testExtractImg() {
        // Arrange
        String content = "<div><img src=\"http://example.com/image1.jpg\"/><img src=\"http://example.com/image2.jpg\"/></div>";

        // Act
        List<String> result = platformAnswerTransformer.extractImgTest(content);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.contains("http://example.com/image1.jpg"));
        assertTrue(result.contains("http://example.com/image2.jpg"));
    }

    @Test
    public void testProcessRichText() {
        // Arrange
        BotAnswer botAnswer = new BotAnswer();
        RichTextCard richTextCard = new RichTextCard();
        richTextCard.setHtml("<div>Hello World</div>");
        botAnswer.setContent(JSON.toJSONString(richTextCard));

        // Act
        String result = platformAnswerTransformer.processRichTextTest(botAnswer);

        // Assert
        assertEquals("Hello World", result);
    }
}