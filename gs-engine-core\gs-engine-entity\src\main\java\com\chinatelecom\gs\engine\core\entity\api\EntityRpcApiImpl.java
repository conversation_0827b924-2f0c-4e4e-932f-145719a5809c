package com.chinatelecom.gs.engine.core.entity.api;

import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.core.entity.service.EntityService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityQueryRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * @USER: pengmc1
 * @DATE: 2025/8/5 9:45
 */

@Slf4j
@RestController
@RequestMapping(Apis.BASE_PREFIX + Apis.RPC_PREFIX + Apis.ENTITY_API)
public class EntityRpcApiImpl {

    @Resource
    private EntityService entityService;
    /**
     * 获取实体详情列表
     *
     * @param request
     * @return
     */
    @PostMapping("/getEntityDetailList")
    public Result<List<EntityDetailVO>> getEntityDetailList(@Valid @RequestBody EntityQueryRequest request) {
        return Result.success(entityService.getEntityDetailList(request.getEntityCodes()));
    }
}
