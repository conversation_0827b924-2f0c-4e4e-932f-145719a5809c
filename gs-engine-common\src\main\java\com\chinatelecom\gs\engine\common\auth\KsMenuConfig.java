package com.chinatelecom.gs.engine.common.auth;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年06月16日
 */
public interface KsMenuConfig {

    //知识管理
    String KNOWLEDGE_BASE = "/knowledgeBase";
    String KNOWLEDGE_BASE_1 = "/flow-sub/knowledgeBase";

    //文档知识列表
    String KNWL_LIST = "/knwllist";
    String KNWL_LIST_1 = "/flow-sub/knwllist";

    //文档知识库
    String DOC_KNOWLEDGE = "/word-knowledge";
    String DOC_KNOWLEDGE_1 = "/flow-sub/word-knowledge";

    //文档详情
    String DOC_KNWL_DETAIL = "/knwldetail";
    String DOC_KNWL_DETAIL_1 = "/flow-sub/knwldetail";

    // 问答知识库
    String FAQ_KNWL_List = "/querylist";
    String FAQ_KNWL_List_1 = "/flow-sub/querylist";

    //问答文档详情
    String FAQ_KNWL_DETAIL = "/querydetail";
    String FAQ_KNWL_FAQ_KNWL_DETAIL_1 = "/flow-sub/querydetail";

    //知识检索
    String KNWL_SEARCH = "/knowledgeSearch";
    String KNWL_SEARCH_1 = "/flow-sub/knowledgeSearch";

    // 智能写作
    String CUSTOM_REPORT = "customReport";
    String CUSTOM_REPORT_1 = "/flow-sub/customReport/report";

    //AI报告详情
    String REPORT_DETAIL = "/articleEditor";
    String REPORT_DETAIL_1 = "/flow-sub/customReport/articleEditor";

    //公文模板
    String WORD_TPL = "/officialDocTpl";
    String WORD_TPL_1 = "/flow-sub/officialDocTpl";

    //知识收藏
    String COLLECTION = "/myCollection";
    String COLLECTION_1 = "/flow-sub/myCollection";

    //流程中心
    String FLOW = "/approval/process";
    String FLOW_1 = "/flow-sub/approval/process";

    //监听管理
    String LISTENER = "/approval/listener";
    String LISTENER_1 = "/flow-sub/approval/listener";

    //流程设计
    String BPMN = "/approval/bpmn";
    String BPMN_1 = "/flow-sub/approval/bpmn";

    //标签管理
    String TAG_MANAGE = "/sysconfig/tagsManage";
    String TAG_MANAGE_1 = "/flow-sub/sysconfig/tagsManage";

    //标签管理详情
    String TAG_DETAIL = "/sysconfig/tagsManage/tagDetail";
    String TAG_DETAIL_1 = "/flow-sub/sysconfig/tagsManage/tagDetail";

    //配置管理
    String PUBLISH_CONFIG = "/flow-sub/sysconfig/publish";
    String PUBLISH_CONFIG_1 = "/sysconfig/publish";

    //我的bot
    String BOT = "bot";

    // 我的业务流
    String WORKFLOW_FLOW = "faq";

    // 我的插件
    String PLUGIN = "plugin";

    // 机器人市场
    String ROBOT_MARKET = "robotMarket";

    // 插件市场
    String PLUGIN_MARKET = "pluginMarket";

    // 业务流市场
    String FLOW_MARKET = "flowMarket";

    // 数据库
    String DATABASE = "database";

    /**
     * 机器人明细
     */
    String STATISTIC_BOT = "/statistic-bot";

    /**
     * 运营统计
     */
    String STATISTIC_ANALYSIS = "/statistic-analysis";

    /**
     * api明细
     */
    String STATISTIC_API = "/statistic-api";

    /**
     * 敏感词管理
     */
    String SENSITIVE = "/flow-sub/sysconfig/sensitiveManage";

    /**
     * 提示词管理
     */
    String PROMPT = "prompt";

    /**
     * 模型管理
     */
    String MODEL_MANAGE = "/flow-sub/sysconfig/model";
}
