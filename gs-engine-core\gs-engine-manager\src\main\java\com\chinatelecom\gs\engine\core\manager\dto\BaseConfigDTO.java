package com.chinatelecom.gs.engine.core.manager.dto;

import com.chinatelecom.gs.engine.common.dto.base.BaseCodeDTO;
import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月28日
 */
@Getter
@Setter
public class BaseConfigDTO extends BaseCodeDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务配置名称
     */
    private String name;

    /**
     * 配置维度
     */
    private DimensionEnum dimension;
    /**
     * 业务配置场景
     */
    private String configType;

    /**
     * 业务配置场景说明
     */
    private String description;

    /**
     * 业务场景配置唯一标识
     */
    private String businessNo;

    /**
     * 配置json格式value值
     */
    private String configData;

    /**
     * 配置json的class对象
     */
    private String objClass;

}
