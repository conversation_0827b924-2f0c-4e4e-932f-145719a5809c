package com.chinatelecom.gs.engine.channel.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.channel.common.enums.ConfigKeyEnums;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelConfigPO;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelConfigRepository;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.channel.service.dto.RobotConfigDTO;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/25 16:05
 * @description
 */
@Slf4j
@Service
public class RobotConfigService {

    @Resource
    private ChannelConfigRepository configRepository;
    @Resource
    private ChannelInfoRepository channelInfoRepository;


    /**
     * 渠道机器人配置
     *
     * @param channelId      String
     * @param robotConfigDTO RobotConfigDTO
     * @return Boolean
     */
    public Boolean addConfig(String channelId, RobotConfigDTO robotConfigDTO) {
        ChannelInfoDTO channelInfo = channelInfoRepository.getChannelInfo(channelId, robotConfigDTO.getBotCode());
        if (channelInfo == null) {
            throw new BizException("A0049", "渠道信息不正确，请校验参数");
        }
        ChannelConfigPO configPO = new ChannelConfigPO();
        configPO.setChannelId(channelId);
        configPO.setConfigKey(ConfigKeyEnums.ROBOT_CONFIG.getCode());
        configPO.setConfigValue(JsonUtils.toJsonString(robotConfigDTO));
        return configRepository.save(configPO);
    }

    /**
     * 查询机器人配置信息
     *
     * @param channelId String
     * @param robotCode String
     * @return RobotConfigDTO
     */
    public RobotConfigDTO getRobotConfig(String channelId, String robotCode) {
        ChannelInfoDTO channelInfo = channelInfoRepository.getChannelInfo(channelId, robotCode);
        if (channelInfo == null) {
            throw new BizException("A0049", "渠道信息不正确，请校验参数");
        }
        LambdaQueryWrapper<ChannelConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelConfigPO::getChannelId, channelId)
                .eq(ChannelConfigPO::getConfigKey, ConfigKeyEnums.ROBOT_CONFIG.getCode());
        ChannelConfigPO configPO = configRepository.getOne(queryWrapper);
        if (Objects.isNull(configPO) || StringUtils.isBlank(configPO.getConfigValue())) {
            throw new BizException("A0055","机器人接入信息配置为空");
        }

        return JsonUtils.parseObject(configPO.getConfigValue(), RobotConfigDTO.class);
    }
}
