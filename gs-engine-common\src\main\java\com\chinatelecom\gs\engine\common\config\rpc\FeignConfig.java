package com.chinatelecom.gs.engine.common.config.rpc;

import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.context.LocalContext;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.log.LogTrace;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import feign.RequestInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * FeignConfig
 *
 * <AUTHOR>
 * @date 2023-04-20 10:54
 */
@Configuration
@EnableFeignClients(basePackages = {"com.chinatelecom.gs.engine", "com.telecom.ais.telephone"})
public class FeignConfig {

    private static final Logger log = LoggerFactory.getLogger(FeignConfig.class);
    @Resource
    private LogTrace logTrace;

    @Bean
    public RequestInterceptor feignInterceptor() {
        return template -> {
            log.debug("feign config:{}", JSON.toJSONString(RequestContext.get()));
            String appCode = RequestContext.getAppCode();
            if (StringUtils.isNotBlank(appCode)) {
                template.header(HeaderKeys.APP_CODE, appCode);
            }

            String userId = RequestContext.getUserId();
            if (StringUtils.isNotBlank(userId)) {
                template.header(HeaderKeys.USER_ID, userId);
            }

            String tenantId = RequestContext.getTenantId();
            if (StringUtils.isNotBlank(tenantId)) {
                template.header(HeaderKeys.TENANT_ID, tenantId);
            }

            AppSourceType appSourceType = RequestContext.getAppSourceType();
            if (appSourceType != null) {
                template.header(HeaderKeys.APP_SOURCE, appSourceType.name());
            }
            String sessionId = StringUtils.isNotBlank(LocalContext.getSessionId()) ? LocalContext.getSessionId() : RequestContext.getSessionId();
            if(StringUtils.isNotBlank(sessionId)){
                template.header(HeaderKeys.SESSION_ID,sessionId);
            }
            String messageId = StringUtils.isNotBlank(LocalContext.getMessageId()) ? LocalContext.getMessageId() : RequestContext.getMessageId();
            if(StringUtils.isNotBlank(messageId)){
                template.header(HeaderKeys.MESSAGE_ID,messageId);
            }
            String agentCode = StringUtils.isNotBlank(LocalContext.getAgentCode()) ? LocalContext.getAgentCode() : RequestContext.getAgentCode();
            if(StringUtils.isNotBlank(agentCode)){
                template.header(HeaderKeys.AGENT_CODE,agentCode);
            }
            String entry = StringUtils.isNotBlank(LocalContext.getEntry()) ? LocalContext.getEntry() : RequestContext.getEntry();
            if(StringUtils.isNotBlank(entry)){
                template.header(HeaderKeys.ENTRY,entry);
            }
            String bizName = StringUtils.isNotBlank(LocalContext.getBizName()) ? LocalContext.getBizName() : RequestContext.getBizName();
            if(StringUtils.isNotBlank(bizName)){
                template.header(HeaderKeys.BIZ_NAME,bizName);
            }
            boolean checkRole = RequestContext.checkRole();
            String checkRoleStr = Boolean.toString(checkRole);
            if(StringUtils.isNotBlank(checkRoleStr)){
                template.header(HeaderKeys.CHECK_ROLE, checkRoleStr);
            }
            String logId = logTrace.getLogId();
            if(StringUtils.isBlank(logId)){
                logId = RequestContext.getLogId();
            }
            if(StringUtils.isNotBlank(logId)){
                template.header(HeaderKeys.LOG_ID,logId);
            }

            Boolean admin = Optional.ofNullable(RequestContext.get()).map(RequestInfo::getIsAdmin).orElse(null);
            if (Objects.nonNull(admin)) {
                template.header(HeaderKeys.USER_ADMIN, String.valueOf(admin.booleanValue()));
            }

            log.debug("feign interceptor:{}", template.headers());

        };
    }

}
