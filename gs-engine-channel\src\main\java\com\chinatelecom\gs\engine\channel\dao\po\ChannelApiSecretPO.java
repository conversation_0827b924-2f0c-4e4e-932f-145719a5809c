package com.chinatelecom.gs.engine.channel.dao.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.io.Serializable;

import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * API秘钥管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Getter
@Setter
@TableName("channel_api_secret")
public class ChannelApiSecretPO extends BasePO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 应用id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 渠道id
     */
    @TableField("channel_id")
    private String channelId;

    @TableField("secret_type")
    private ApiSecretType secretType;

    /**
     * 秘钥id
     */
    @TableField("secret_id")
    private String secretId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 秘钥
     */
    @TableField("secret")
    private String secret;

    /**
     * 秘钥
     */
    @TableField("app_code")
    private String appCode;

    /**
     * 系统来源
     */
    @TableField("source_system")
    private AppSourceType sourceSystem;

}
