package com.chinatelecom.gs.engine.core.manager.vo.config.base;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BaseConfigDetailsQueryParam {
    /**
     * 业务配置场景
     */
    @NotEmpty(message = "配置类型不能为空")
    private List<String> configTypes;

}
