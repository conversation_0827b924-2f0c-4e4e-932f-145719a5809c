package com.chinatelecom.gs.engine.core.entity.ability.recognition.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.chinatelecom.gs.engine.core.entity.ability.recognition.AbstractEntityRecognitionAbilityService;
import com.chinatelecom.gs.engine.core.entity.common.utils.ParallelUtils;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.plugin.ExecutePluginRpcApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.ExecutePluginApiRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.ExecutePluginApiResponse;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 *
 * 基于插件的实体识别
 *
 * @USER: pengmc1
 * @DATE: 2025/1/17 18:48
 */

@Slf4j
@Service
public class PluginEntityRecognitionAbilityService extends AbstractEntityRecognitionAbilityService {

    @Resource
    private ExecutePluginRpcApi executePluginRpcApi;

    @Resource
    private ExecutorService commonExecutorService;

    /**
     * 识别能力标识
     *
     * @return
     */
    @Override
    public String abilityCode() {
        return EntityAbilityEnum.PLUGIN_RECOGNITION.getCode();
    }

    /**
     * 执行实体识别
     *
     * @param request
     * @param entityDetailList
     * @return
     */
    @Override
    protected List<Entity> doPredict(EntityRecognitionRequest request, List<EntityDetailVO> entityDetailList) {
        //按插件编码进行分组
        Map<String, List<EntityDetailVO>>  pluginEntityDetailMap = Maps.newHashMap();
        entityDetailList.stream().forEach( entityDetailVO -> {
            String pluginCode = getAbilityConfig(entityDetailVO, EntityAbilityEnum.PLUGIN_RECOGNITION).getPluginCode();
            if(!pluginEntityDetailMap.containsKey(pluginCode)){
                pluginEntityDetailMap.put(pluginCode, new ArrayList<>());
            }
            pluginEntityDetailMap.get(pluginCode).add(entityDetailVO);
        });
        //并发调用插件进行实体识别
        List<String> pluginCodes = new ArrayList<>(pluginEntityDetailMap.keySet());
        List<Entity> entities = ParallelUtils.parallelExecute(commonExecutorService, pluginCodes, 60000L, pluginCode -> {
            ExecutePluginApiRequest pluginApiRequest = new ExecutePluginApiRequest();
            pluginApiRequest.setApiId(pluginCode);
            pluginApiRequest.setInputs(buildInputParams(request, pluginEntityDetailMap.get(pluginCode)));
            pluginApiRequest.setSessionId(request.getSessionId());
            Result<ExecutePluginApiResponse> executePluginApiResponseResult = executePluginRpcApi.executeApi(pluginApiRequest);
            String responseParam = executePluginApiResponseResult.getData().getResponseParam();
            List<Entity> entityList = JSON.parseObject(responseParam).getObject("data", new TypeReference<List<Entity>>() {});
            return entityList;
        });
        return entities;
    }

    /**
     * 构造插件输入参数
     * @param request
     * @param entityDetailList
     * @return
     */
    private Map<String, Object> buildInputParams(EntityRecognitionRequest request, List<EntityDetailVO> entityDetailList){
        Map<String, Object> inputParams = new HashMap<>();
        inputParams.put("sessionId", request.getSessionId());
        inputParams.put("messageId", request.getMessageId());
        inputParams.put("query", request.getQuery());
        inputParams.put("entityDetailVOs", entityDetailList);
        return inputParams;
    }
}
