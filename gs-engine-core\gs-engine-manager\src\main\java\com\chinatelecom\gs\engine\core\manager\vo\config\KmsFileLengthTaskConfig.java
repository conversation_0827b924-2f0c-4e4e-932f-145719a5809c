package com.chinatelecom.gs.engine.core.manager.vo.config;

import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年08月05日
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KmsFileLengthTaskConfig implements ConfigValidator {

    /**
     * 是否完成任务扫描
     */
    private Boolean finished = false;

    @Override
    public void validate(Map<String, Object> param) throws BizException {
    }
}
