package com.chinatelecom.gs.engine.channel.service.messagehandler;


import cn.hutool.core.exceptions.ValidateException;
import com.chinatelecom.gs.engine.channel.api.controller.open.OpenApiMessageController;
import com.chinatelecom.gs.engine.channel.api.vo.CommonAuthParam;
import com.chinatelecom.gs.engine.channel.manage.ChannelSecretManagerService;
import com.chinatelecom.gs.engine.channel.manage.OffSiteChannelSwitchCacheService;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Objects;

@Service
@Slf4j
public class ApiMessageAuthCheckService {


    @Resource
    private ChannelSecretManagerService channelSecretManagerService;
    @Autowired
    private OffSiteChannelSwitchCacheService offSiteChannelSwitchCacheService;


    public void checkAuth(CommonAuthParam param, HttpServletRequest request, String content) {
        String sign = request.getHeader("App-Sign");
        String apiKey = request.getHeader("App-Secret");
        if (StringUtils.isBlank(sign)) {
            sign = param.getSign();
        }
        if (StringUtils.isBlank(sign) || Objects.isNull(param.getAuthInfo())) {
            throw new ValidateException("签名为空");
        }
        param.setSign(sign);
        String signContent = this.buildSignContent(param.getRobotCode(), content, param.getUserId(), param.getRequestTime());
        this.validReChannelId(apiKey, signContent, param.getSign());
    }

    public static String buildSignContent(String botCode, String content, String userId, Long requestTime) {
        return "botCode=%s&content=%s&userId=%s&timestamp=%d".formatted(
                botCode, content, userId, requestTime);
    }


    public String validReChannelId(String secretId, String content, String sign) {
        ChannelApiSecretDTO dto = channelSecretManagerService.getSecretWithSecretId(secretId);
        boolean channelOpen = offSiteChannelSwitchCacheService.channelOpen(dto.getAppId(), dto.getChannelId());
        if (!channelOpen) {
            throw new ValidateException(OpenApiMessageController.CHANNEL_NOT_OPEN,"渠道未开启, 验签失败。");
        }

        String secret = dto.getSecret();
        if (ObjectUtils.isEmpty(secret)) {
            throw new ValidateException(OpenApiMessageController.EMPTY_SIGNATURE_CODE,"请求头签名非法, 验签失败。");
        }

        if (ObjectUtils.isEmpty(secret)) {
            throw new ValidateException(OpenApiMessageController.EMPTY_SIGNATURE_CODE,"请求头签名非法, 验签失败。");
        }

        String signContent = "secretKey=%s&content=%s".formatted(secret, content);

        try {
            // 使用 HMAC-SHA256 进行加密
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
            hmacSha256.init(secretKeySpec);

            // 生成本地签名
            byte[] localSignBytes = hmacSha256.doFinal(signContent.getBytes());
            String localSign = Hex.encodeHexString(localSignBytes);

            // 验签
            if (!Objects.equals(sign, localSign)) {
                log.info("requestSign:{} localSign:{}", sign, localSign);
                throw new ValidateException(OpenApiMessageController.INVALID_SIGNATURE_CODE, "appId和appKey非法!");
            }
        } catch (BizException biz) {
            throw biz;
        } catch (Exception e) {
            log.error("签名过程发生错误 ==> {},{},{}", secretId, content, sign, e);
            throw new ValidateException(OpenApiMessageController.SYSTEM_ERROR,"签名过程发生错误");
        }
        return dto.getChannelId();
    }

}
