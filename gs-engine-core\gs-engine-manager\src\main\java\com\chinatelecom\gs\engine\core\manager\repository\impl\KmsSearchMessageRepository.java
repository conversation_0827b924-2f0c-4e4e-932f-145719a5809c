package com.chinatelecom.gs.engine.core.manager.repository.impl;

import com.chinatelecom.gs.engine.common.mq.KmsSearchMessage;
import com.chinatelecom.gs.engine.common.mq.TermQueryCount;
import com.chinatelecom.gs.engine.common.utils.EsPrefixUtils;
import com.chinatelecom.gs.engine.robot.sdk.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.elasticsearch.client.RequestOptions;

import org.springframework.stereotype.Repository;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
public interface KmsSearchMessageRepository extends ElasticsearchRepository<KmsSearchMessage, String>, CustomKmsSearchMessageRepository {
}

interface CustomKmsSearchMessageRepository {
    List<TermQueryCount> findTopQueriesByTenantAndTimeRange(String tenantId, LocalDateTime startTime, LocalDateTime endTime, int limit);
}

@Slf4j
class CustomKmsSearchMessageRepositoryImpl implements CustomKmsSearchMessageRepository {

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private RestHighLevelClient client;

    /**
     * 根据租户ID和时间范围查询热门搜索词
     *
     * @param tenantId  租户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     返回的热门词数量
     * @return 包含查询词和出现次数的列表
     */
    @Override
    public List<TermQueryCount> findTopQueriesByTenantAndTimeRange(String tenantId,
                                                                   LocalDateTime startTime,
                                                                   LocalDateTime endTime,
                                                                   int limit) {
        try {
            // 2. 构建聚合查询
            SearchSourceBuilder sourceBuilder = buildQuery(tenantId, startTime, endTime, limit); // 降序排列
            log.info("执行ES查询：{}", sourceBuilder.toString());

            // 3. 执行查询
            SearchResponse response = client.search(
                    new SearchRequest(EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME))
                            .source(sourceBuilder),
                    RequestOptions.DEFAULT
            );

            // 4. 处理结果（添加空值保护）
            return Optional.ofNullable(response.getAggregations())
                    .map(aggs -> aggs.<ParsedTerms>get("top_queries"))
                    .map(ParsedTerms::getBuckets)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(bucket -> {
                        TermQueryCount termQueryCount = new TermQueryCount();
                        termQueryCount.setQuery(bucket.getKeyAsString());
                        termQueryCount.setCount(bucket.getDocCount());
                        return termQueryCount;
                    })
                    .collect(Collectors.toList());

        } catch (IOException e) {
            log.error("ES查询失败，租户ID：{}，时间范围：{}~{}", tenantId, startTime, endTime, e);
            throw new RuntimeException("查询热门搜索词失败，请检查参数或联系管理员", e);
        }
    }

    // 修正后的查询构建方法
    public SearchSourceBuilder buildQuery(String tenantId, LocalDateTime start, LocalDateTime end, int limit) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        return new SearchSourceBuilder()
                .size(0)
                .fetchSource(new String[]{"query"}, null)
                .query(QueryBuilders.boolQuery()
                        .filter(QueryBuilders.termQuery("tenantId", tenantId))
                        .must(QueryBuilders.rangeQuery("searchTime")
                                .gte(start.format(formatter))
                                .lte(end.format(formatter))
                                .format("yyyy-MM-dd HH:mm:ss.SSS")
                        )
                )
                .aggregation(AggregationBuilders.terms("top_queries")
                        .field("query")
                        .size(limit)
                        .order(BucketOrder.count(false)));
    }
}
