package com.chinatelecom.gs.engine.core.manager.worker;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.log.track.mapper.LogMapper;
import com.chinatelecom.gs.engine.core.manager.service.LogEsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * 日期清理任务
 * @USER: pengmc1
 * @DATE: 2025/5/23 14:18
 */

@Slf4j
@Component
public class LogCleanTask {

    /**
     * 数据库日志
     */
    @Resource
    private LogMapper logMapper;

    /**
     * ES日志
     */
    @Resource
    private LogEsService logEsService;
    /**
     * 配置
     */
    @Resource
    private GsGlobalConfig gsGlobalConfig;

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");

    @Value("${app.db.kms.dbname}")
    private String dbName;


    @Scheduled(cron="0 0 1 * * ?") // 每天凌晨1点执行一次
    public void clearLog() {
        try{
            String dbTableName = getPreNTableName(gsGlobalConfig.getTraceLogProperty().getDbMaxMonth());
            if(logMapper.existTable(dbName, dbTableName) > 0){
                log.info("执行清理任务，删除表名为：" + dbTableName + "的日志表");
                logMapper.deleteTable(dbTableName);
            }
            LocalDateTime preNMonthTime = getPreNMonthTime(gsGlobalConfig.getTraceLogProperty().getEsMaxMonth());
            log.info("执行清理任务，删除时间早于：" + preNMonthTime + "的ES日志数据");
            logEsService.deleteLogsBySendTimeRange(LocalDateTime.MIN, preNMonthTime);
        }catch (Exception e){
            log.error("清理日志数据发生异常！",e);
        }
    }

    private LocalDateTime getPreNMonthTime(Integer n){
        return LocalDateTime.now().minusMonths(n);
    }

    private String getPreNTableName(Integer n){
        YearMonth current = YearMonth.now();
        return "gs_log_" + current.minusMonths(n).format(formatter);
    }
}
