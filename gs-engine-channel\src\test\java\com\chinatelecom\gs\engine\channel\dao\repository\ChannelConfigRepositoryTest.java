package com.chinatelecom.gs.engine.channel.dao.repository;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.baomidou.mybatisplus.extension.service.IService; import com.chinatelecom.gs.engine.channel.dao.po.ChannelConfigPO; import org.junit.jupiter.api.BeforeEach; import org.junit.jupiter.api.Test; import org.mockito.Mock; import org.mockito.MockitoAnnotations;
public class ChannelConfigRepositoryTest {
    @Mock
    private ChannelConfigRepository channelConfigRepository;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testSelectById() {
        // Arrange
        Long id = 1L;
        ChannelConfigPO expectedPo = new ChannelConfigPO();
        when(channelConfigRepository.getById(id)).thenReturn(expectedPo);

        // Act
        ChannelConfigPO result = channelConfigRepository.getById(id);

        // Assert
        assertSame(expectedPo, result);
        verify(channelConfigRepository).getById(id);
    }

    @Test
    public void testInsert() {
        // Arrange
        ChannelConfigPO po = new ChannelConfigPO();
        boolean expectedResult = true;
        when(channelConfigRepository.save(po)).thenReturn(expectedResult);

        // Act
        boolean result = channelConfigRepository.save(po);

        // Assert
        assertTrue(result);
        verify(channelConfigRepository).save(po);
    }

    @Test
    public void testUpdateById() {
        // Arrange
        ChannelConfigPO po = new ChannelConfigPO();
        boolean expectedResult = true;
        when(channelConfigRepository.updateById(po)).thenReturn(expectedResult);

        // Act
        boolean result = channelConfigRepository.updateById(po);

        // Assert
        assertTrue(result);
        verify(channelConfigRepository).updateById(po);
    }

    @Test
    public void testDeleteById() {
        // Arrange
        Long id = 1L;
        boolean expectedResult = true;
        when(channelConfigRepository.removeById(id)).thenReturn(expectedResult);

        // Act
        boolean result = channelConfigRepository.removeById(id);

        // Assert
        assertTrue(result);
        verify(channelConfigRepository).removeById(id);
    }
}