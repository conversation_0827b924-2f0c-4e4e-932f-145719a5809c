package com.chinatelecom.gs.engine.core.manager.vo.config;

import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeDocType;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 内置的功能配置，不会开放给页面修改，只读
 * @date 2025年03月06日
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InternalFunctionConfig implements ConfigValidator {

    public final static Map<KnowledgeDocType, Map<KnowledgeType, Set<String>>> DEFAULT_DOC_INFO = new HashMap<>();


    static {
        for (KnowledgeDocType docType : KnowledgeDocType.values()) {
            Set<KnowledgeType> knowTypeList = docType.getKnowledgeTypes();
            Map<KnowledgeType, Set<String>> knowledgeTypeMap = new HashMap<>();
            for (KnowledgeType knowledgeType : knowTypeList) {
                knowledgeTypeMap.put(knowledgeType, knowledgeType.getSuffix());
            }
            DEFAULT_DOC_INFO.put(docType, knowledgeTypeMap);
        }
    }

    /**
     * 智文支持的类型
     */
    private List<KnowledgeType> supportKnowledgeType;

    /**
     * 默认支持的类型
     */
    private List<KnowledgeType> defaultSupportType;

    /**
     * 智文支持的文件大小（MB）
     * 前端已经不使用了
     */
    @Deprecated
    private Map<KnowledgeDocType, Integer> supportKnowledgeFileSize;

    /**
     * 聊天窗支持的类型
     */
    private List<KnowledgeType> chatSupportKnowledgeType;

    /**
     * 配置支持的文件大小（MB）
     * 智文导入页面使用配置
     */
    private Map<KnowledgeDocType, Integer> supportFileSize;

    /**
     * 聊天窗配置支持的文件大小（MB）
     */
    private Map<KnowledgeDocType, Integer> chatSupportFileSize;

    private Map<KnowledgeDocType, Map<KnowledgeType, Set<String>>> defaultDocInfo = DEFAULT_DOC_INFO;

    private Boolean forbidRelatedTools;

    @Override
    public void validate(Map<String, Object> param) throws BizException {
    }

    public void filterMultimodal(boolean isMultimodal){
        if(!isMultimodal){
            if (CollectionUtils.isNotEmpty(supportKnowledgeType)) {
                supportKnowledgeType = supportKnowledgeType.stream().filter(type -> !KnowledgeType.MULTIMODAL_TYPE.contains(type)).collect(Collectors.toList());
            }

            if (CollectionUtils.isNotEmpty(chatSupportKnowledgeType)) {
                chatSupportKnowledgeType = chatSupportKnowledgeType.stream().filter(type -> !KnowledgeType.MULTIMODAL_TYPE.contains(type)).collect(Collectors.toList());
            }
        }
    }

    public static InternalFunctionConfig defaultConfig() {
        InternalFunctionConfig config = new InternalFunctionConfig();
        config.setSupportKnowledgeType(ImmutableList.of(
                KnowledgeType.PDF,
                KnowledgeType.WORD,
                KnowledgeType.PPT,
                KnowledgeType.EXCEL,
                KnowledgeType.TEXT,
//                KnowledgeType.MD,
                KnowledgeType.AUDIO,
                KnowledgeType.VIDEO,
                KnowledgeType.IMAGE,
                KnowledgeType.FAQ)
        );
        config.setChatSupportKnowledgeType(ImmutableList.of(
                        KnowledgeType.PDF,
                        KnowledgeType.WORD,
                        KnowledgeType.PPT,
                        KnowledgeType.EXCEL,
                        KnowledgeType.TEXT,
//                        KnowledgeType.MD,
                        KnowledgeType.AUDIO,
                        KnowledgeType.VIDEO,
                        KnowledgeType.IMAGE
                )
        );
        config.setSupportFileSize(
                ImmutableMap.of(
                        KnowledgeDocType.DOC, 200,
                        KnowledgeDocType.EXCEL, 50,
                        KnowledgeDocType.MD, 200,
                        KnowledgeDocType.IMG, 10,
                        KnowledgeDocType.VIDEO, 2048,
                        KnowledgeDocType.AUDIO, 2048,
                        KnowledgeDocType.FAQ, 50
                )
        );

        config.setSupportKnowledgeFileSize(
                ImmutableMap.of(
                        KnowledgeDocType.DOC, 200,
                        KnowledgeDocType.EXCEL, 50,
                        KnowledgeDocType.MD, 200,
                        KnowledgeDocType.IMG, 10,
                        KnowledgeDocType.VIDEO, 2048,
                        KnowledgeDocType.AUDIO, 2048,
                        KnowledgeDocType.FAQ, 50
                )
        );

        return config;
    }
}
