package com.chinatelecom.gs.engine.channel.api.controller.open;

import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.channel.api.vo.ChannelApiSecretVO;
import com.chinatelecom.gs.engine.channel.api.vo.WebLinkSecretVO;
import com.chinatelecom.gs.engine.channel.common.constants.ChatConstants;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.manage.OffSiteChannelSwitchCacheService;
import com.chinatelecom.gs.engine.channel.manage.WebLinkConfigService;
import com.chinatelecom.gs.engine.channel.service.dto.AccessTokenData;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.channel.service.messagehandler.ApiMessageAuthCheckService;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.enums.EntryEnum;
import com.chinatelecom.gs.engine.common.enums.OpenApiEnum;
import com.chinatelecom.gs.engine.kms.service.FileManagerAppService;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.QueryChatDataVO;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.center.PlatformDialogCenterApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.utils.IdGenerator;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.FinalMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.MessageRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.SseMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.BotAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.DialogMessageTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelecom.gs.plugin.hub.infra.handler.executor.bi.ChatTaskOpenPoller;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import feign.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;

/**
 * API消息入口控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "通用API接入消息入口")
@RestController
@RequestMapping(value = Constants.CHANNEL_PREFIX + Constants.API_PREFIX + "/message")
public class OpenApiMessageController {

    private static final String DATA_PREFIX = "data:";
    /**
     * 无效的签名
     */
    public static final int INVALID_SIGNATURE_CODE = 1001;
    /**
     * 签名为空
     */
    public static final int EMPTY_SIGNATURE_CODE = 1002;
    /**
     * 渠道未开启, 验签失败。
     */
    public static final int CHANNEL_NOT_OPEN = 1003;

    /**
     * accessToken 失效
     */
    public static final int ACCESS_TOKEN_EXPIRE = 1004;
    /**
     * 系统异常
     */
    public static final int SYSTEM_ERROR = 2000;


    private static final int MAX_RESPONSE_SIZE_MB = 50;
    private static final int MAX_RESPONSE_SIZE_BYTES = MAX_RESPONSE_SIZE_MB * 1024 * 1024;

    @Value("${nl2sql.openapi.chatDataUri:}")
    private String chatDataEndpoint;

    @Value("${nl2sql.host:}")
    private String biHost;

    @Value("${nl2sql.signKey:}")
    private String signKey;

    @Resource
    private PlatformDialogCenterApi platformDialogCenterApi;

    @Resource
    private ApiMessageAuthCheckService apiMessageAuthCheckService;

    @Resource
    @Qualifier("apiChatExecutorPool")
    private ExecutorService apiChatExecutorPool;

    @Autowired
    private WebLinkConfigService webLinkConfigService;

    @Autowired
    private FileManagerAppService fileManagerAppService;
    @Autowired
    private ChannelInfoRepository channelInfoRepository;
    @Autowired
    private OffSiteChannelSwitchCacheService offSiteChannelSwitchCacheService;

    @Autowired
    private AgentBasicConfigService agentBasicConfigService;

    /**
     * 同步鉴权对话接口
     *
     * @param dialogRequest 对话请求数据
     * @param request       HttpServletRequest
     * @return 对话响应结果
     */
    @PostMapping("syncAuthChat")
    @AuditLog(businessType = "通用API接入消息入口", operType = "同步鉴权对话", operDesc = "同步鉴权对话", objId="#dialogRequest.agentCode")
    public Result<FinalMessageResponse> syncAuthChat(@Valid @RequestBody MessageRequest dialogRequest, HttpServletRequest request) {
        log.info("【API】同步鉴权对话接口 请求 {}", JSONUtil.toJsonStr(dialogRequest));

        dialogRequest.setEntry(EntryEnum.API.getCode());
        dialogRequest.setBizName(OpenApiEnum.SYNC_AUTH_CHAT.getCode());
        String channelId = getValidChannelId(dialogRequest, request);
        dialogRequest.setChannelId(channelId);

        Result<FinalMessageResponse> responseResult = platformDialogCenterApi.dialog(dialogRequest);
        log.info("【API】同步鉴权对话接口 返回 {}", JSONUtil.toJsonStr(responseResult));

        return responseResult;
    }

    /**
     * 共享链接查询API密钥
     *
     * @param channelId       渠道ID
     * @param webLinkSecretVO 网页链接密钥VO
     * @return API密钥信息
     */
    @Operation(summary = "共享链接查询api密钥")
    @PostMapping(value = "/link/apisecret/{channelId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @AuditLog(businessType = "通用API接入消息入口", operType = "共享链接查询api密钥", operDesc = "共享链接查询api密钥", objId="#channelId")
    public Result<ChannelApiSecretVO> getApiWithLink(
            @PathVariable(name = ChatConstants.CHANNEL_ID) String channelId,
            @Valid @RequestBody WebLinkSecretVO webLinkSecretVO, HttpServletRequest request) {
        String userId = request.getHeader(HeaderKeys.USER_ID);
        log.info("【API】共享链接查询api密钥 请求 channelId={}, userId={}", channelId, userId);
        ChannelApiSecretDTO apiSecret = webLinkConfigService.getApiWithLink(channelId, webLinkSecretVO);
        ChannelApiSecretVO responseVO = buildApiSecretResponse(apiSecret, userId, channelId);

        return Result.success(responseVO);
    }

    /**
     * SSE鉴权对话接口
     *
     * @param dialogRequest 对话请求数据
     * @param request       HttpServletRequest
     * @return SseEmitter 事件发射器
     */
    @PostMapping("sseAuthChat")
    @AuditLog(businessType = "通用API接入消息入口", operType = "SSE鉴权对话接口", operDesc = "SSE鉴权对话接口", objId="#dialogRequest.agentCode")
    public SseEmitter sseAuthChat(@Valid @RequestBody MessageRequest dialogRequest, HttpServletRequest request) {
        log.info("【API】SSE对话接口 请求 {}", JSONUtil.toJsonStr(dialogRequest));
        dialogRequest.setEntry(EntryEnum.API.getCode());
        dialogRequest.setBizName(OpenApiEnum.SSE_AUTH_CHAT.getCode());
        SseEmitter sseEmitter = new SseEmitter(PlatformDialogCenterApi.TIMEOUT);

        try {
            String channelId = getValidChannelIdForSse(dialogRequest, request, sseEmitter);
            if (channelId == null) {
                // 已经在getValidChannelIdForSse中处理了错误情况，直接返回
                return sseEmitter;
            }

            dialogRequest.setChannelId(channelId);
            apiChatExecutorPool.submit(() -> sendMsg(dialogRequest, platformDialogCenterApi::sseDialog, sseEmitter));

        } catch (Exception e) {
            log.error("SSE对话初始化失败: {}", e.getMessage(), e);
            completeEmitterWithError(sseEmitter, dialogRequest, SYSTEM_ERROR);
        }

        return sseEmitter;
    }

    /**
     * 获取channel文件
     *
     * @param fileKey  文件键
     * @param fileName 文件名
     * @param inline   是否内联显示
     * @param response HttpServletResponse
     * @throws IOException IO异常
     */
    @Operation(summary = "获取channel文件")
    @GetMapping(Apis.FILE_API + Apis.DOWNLOAD_API)
    @AuditLog(businessType = "通用API接入消息入口", operType = "获取channel文件", operDesc = "获取channel文件", objId="#fileKey")
    @HideFromApiTypes(ApiType.OPENAPI)
    public void download(
            @RequestParam String fileKey,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) boolean inline,
            HttpServletResponse response) throws IOException {

        fileManagerAppService.download(fileKey, fileName, inline, response);
    }

    /**
     * 构建API密钥响应
     *
     * @param apiSecret API密钥DTO
     * @param channelId 渠道ID
     * @return 密钥响应VO
     */
    private ChannelApiSecretVO buildApiSecretResponse(ChannelApiSecretDTO apiSecret, String userId, String channelId) {
        ChannelApiSecretVO responseVO = new ChannelApiSecretVO();
        log.info("【API】共享链接查询api密钥 返回 {}", JSONUtil.toJsonStr(apiSecret));
        responseVO.setAgentCode(apiSecret.getAppId());

        if (userId == null) {
            log.warn("API请求异常: 缺少X-UserId请求头 赋默认值test");
            userId = "test";
        }

        // 创建并存储AccessToken
        AccessTokenData tokenData = AccessTokenData.builder()
                .secretId(apiSecret.getSecretId())
                .channelId(channelId)
                .secretName(apiSecret.getName())
                .tenantId(apiSecret.getTenantId())
                .appId(apiSecret.getAppId())
                .appCode(apiSecret.getAppCode())
                .build();

        String accessToken = webLinkConfigService.generateAndStoreAccessToken(tokenData, userId);
        responseVO.setAccessToken(accessToken);

        return responseVO;
    }

    /**
     * 获取有效的渠道ID (用于同步对话)
     *
     * @param dialogRequest 对话请求
     * @param request       原始HTTP请求
     * @return 验证通过的渠道ID
     */
    private String getValidChannelId(MessageRequest dialogRequest, HttpServletRequest request) {
        // 尝试通过accessToken验证
        String accessToken = request.getHeader(HeaderKeys.ACCESS_TOKEN);
        if (StringUtils.isNotBlank(accessToken)) {
            RequestInfo requestInfo = RequestContext.get();
            String channelId = requestInfo.getExtraParamString(ChatConstants.CHANNEL_ID);
            if (StringUtils.isBlank(channelId)) {
                log.warn("API请求被拒绝: 无效的accessToken, URI: {}", request.getRequestURI());
                throw new ValidateException(INVALID_SIGNATURE_CODE, "无效的签名");
            }
            return channelId;
        }

        // 如果accessToken不存在，使用签名验证
        String sign = request.getHeader(HeaderKeys.APP_SIGN);
        String apiKey = request.getHeader(HeaderKeys.APP_SECRET);

        if (StringUtils.isBlank(sign) || StringUtils.isBlank(apiKey)) {
            log.warn("API请求被拒绝: 缺少签名, URI: {}", request.getRequestURI());
            throw new ValidateException(EMPTY_SIGNATURE_CODE, "签名为空");
        }

        // 验证签名
        String signContent = apiMessageAuthCheckService.buildSignContent(
                dialogRequest.getAgentCode(),
                dialogRequest.getContent(),
                dialogRequest.getUserId(),
                dialogRequest.getRequestTime());

        return apiMessageAuthCheckService.validReChannelId(apiKey, signContent, sign);
    }

    /**
     * 获取有效的渠道ID (用于SSE对话)
     *
     * @param dialogRequest 对话请求
     * @param request       原始HTTP请求
     * @param sseEmitter    SSE事件发射器
     * @return 验证通过的渠道ID，如果验证失败则返回null
     */
    private String getValidChannelIdForSse(MessageRequest dialogRequest, HttpServletRequest request, SseEmitter sseEmitter) {
        // 尝试通过accessToken验证
        String accessToken = request.getHeader(HeaderKeys.ACCESS_TOKEN);
        if (StringUtils.isNotBlank(accessToken)) {
            RequestInfo requestInfo = RequestContext.get();
            String channelId = requestInfo.getExtraParamString(ChatConstants.CHANNEL_ID);
            String appId = requestInfo.getExtraParamString(ChatConstants.APP_ID);
            //打印 channelId appId
            boolean channelOpen = offSiteChannelSwitchCacheService.channelOpen(appId, channelId);
            if (!channelOpen) {
                completeEmitterWithError(sseEmitter, dialogRequest, CHANNEL_NOT_OPEN);
                return null;
            }
            if (StringUtils.isBlank(channelId)) {
                log.warn("API请求被拒绝: 无效的accessToken, URI: {}", request.getRequestURI());
                completeEmitterWithError(sseEmitter, dialogRequest, ACCESS_TOKEN_EXPIRE);
                return null;
            }
            return channelId;
        }

        // 如果accessToken不存在，使用签名验证
        String sign = request.getHeader(HeaderKeys.APP_SIGN);
        String apiKey = request.getHeader(HeaderKeys.APP_SECRET);

        if (StringUtils.isBlank(sign) || StringUtils.isBlank(apiKey)) {
            log.warn("API请求被拒绝: 缺少签名, URI: {}", request.getRequestURI());
            completeEmitterWithError(sseEmitter, dialogRequest, EMPTY_SIGNATURE_CODE);
        }

        try {
            // 验证签名
            String signContent = apiMessageAuthCheckService.buildSignContent(
                    dialogRequest.getAgentCode(),
                    dialogRequest.getContent(),
                    dialogRequest.getUserId(),
                    dialogRequest.getRequestTime());

            return apiMessageAuthCheckService.validReChannelId(apiKey, signContent, sign);
        } catch (ValidateException e) {
            log.warn("API请求被拒绝: 签名验证失败, URI: {}, 错误: {}", request.getRequestURI(), e.getMessage());
            completeEmitterWithError(sseEmitter, dialogRequest, e.getStatus());
        } catch (Exception e) {
            log.warn("API请求被拒绝: 签名验证失败, URI: {}, 错误: {}", request.getRequestURI(), e.getMessage());
            completeEmitterWithError(sseEmitter, dialogRequest, SYSTEM_ERROR);
        }
        return null;
    }

    /**
     * 完成SSE发射器，返回错误消息
     *
     * @param sseEmitter       SSE事件发射器
     * @param dialogRequest    对话请求
     * @param errorMessageCode 错误消息错误码
     */
    private void completeEmitterWithError(SseEmitter sseEmitter, MessageRequest dialogRequest, int errorMessageCode) {
        try {
            SseMessageResponse sseMessageResponse = new SseMessageResponse();
            sseMessageResponse.setUpMsgId(dialogRequest.getMessageId());
            sseMessageResponse.setDownMsgId(IdGenerator.getMessageId());
            sseMessageResponse.setMessageType(DialogMessageTypeEnum.ALL);
            sseMessageResponse.setEventType(SendMessageTypeEnum.ERROR);

            BotAnswer botAnswer = new BotAnswer();
            botAnswer.setAnswerType(AnswerTypeEnum.MARKDOWN);
            botAnswer.setNamespace("com.markdown");
            botAnswer.setContent(errorMessageCode);

            sseMessageResponse.setAnswer(botAnswer);
            sseEmitter.send(sseMessageResponse, MediaType.APPLICATION_JSON);
            sseEmitter.complete();
        } catch (IOException ex) {
            log.error("发送SSE错误信息失败: {}", ex.getMessage(), ex);
            sseEmitter.complete();
        }
    }

    /**
     * 发送SSE消息
     *
     * @param chatRequest 对话请求
     * @param function    处理函数
     * @param sseEmitter  SSE事件发射器
     */
    private void sendMsg(MessageRequest chatRequest, Function<MessageRequest, Response> function, SseEmitter sseEmitter) {
        try (Response response = function.apply(chatRequest)) {
            InputStream inputStream = response.body().asInputStream();

            // 检查响应大小
            if (inputStream.available() > MAX_RESPONSE_SIZE_BYTES) {
                log.warn("响应大小超过限制 ({}MB): {}", MAX_RESPONSE_SIZE_MB, chatRequest);
                throw new IOException("响应大小超过限制");
            }

            processResponseStream(inputStream, chatRequest, sseEmitter);

        } catch (IOException e) {
            log.error("从对话服务读取响应时发生错误: {}", e.getMessage(), e);
            sendErrorEvent(sseEmitter, "服务通信错误");
        } catch (Exception e) {
            log.error("处理SSE消息时发生意外错误: {}", e.getMessage(), e);
            sendErrorEvent(sseEmitter, "服务器内部错误");
        } finally {
            safelyCompleteEmitter(sseEmitter, chatRequest);
        }
    }

    /**
     * 处理响应流
     *
     * @param inputStream 输入流
     * @param chatRequest 对话请求
     * @param sseEmitter  SSE事件发射器
     * @throws IOException IO异常
     */
    private void processResponseStream(InputStream inputStream, MessageRequest chatRequest, SseEmitter sseEmitter) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;

        while ((line = reader.readLine()) != null) {
            if (org.springframework.util.StringUtils.hasLength(line)) {
                // 去掉"data:"前缀
                String data = line.startsWith(DATA_PREFIX) ?
                        line.substring(DATA_PREFIX.length()) : line;

                try {
                    // 发送数据到 SseEmitter
                    sseEmitter.send(data, MediaType.TEXT_PLAIN);
                } catch (IOException e) {
                    log.warn("发送SSE数据时发生错误: {}", e.getMessage());
                    break;
                } catch (IllegalStateException e) {
                    log.warn("SSE发射器已不可用: {}", e.getMessage());
                    break;
                }
            }
        }
    }

    /**
     * 发送错误事件
     *
     * @param sseEmitter   SSE事件发射器
     * @param errorMessage 错误消息
     */
    private void sendErrorEvent(SseEmitter sseEmitter, String errorMessage) {
        try {
            sseEmitter.send(SseEmitter.event().name("error").data(errorMessage));
        } catch (Exception e) {
            log.warn("发送错误事件失败: {}", e.getMessage());
        }
    }

    /**
     * 安全地完成SSE发射器
     *
     * @param sseEmitter  SSE事件发射器
     * @param chatRequest 对话请求
     */
    private void safelyCompleteEmitter(SseEmitter sseEmitter, MessageRequest chatRequest) {
        try {
            sseEmitter.complete();
            log.info("SSE发射器已完成: {}", chatRequest.getMessageId());
        } catch (Exception e) {
            log.warn("完成SSE发射器时发生错误: {}", e.getMessage());
        }
    }

    @Operation(summary = "查询bi分页数据")
    @GetMapping("/bi/page")
    @AuditLog(businessType = "通用API接入消息入口", operType = "查询bi分页数据", operDesc = "查询bi分页数据", objId="#request.agentCode")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Object> queryChatPage(@Validated QueryChatDataVO request) {
        log.info("queryChatPage request:{}", request);
        return queryOpenApiChatPage(request);
    }

    /**
     * open api
     *
     * @param request QueryChatDataVO
     * @return Result<Object>
     */
    private Result<Object> queryOpenApiChatPage(QueryChatDataVO request) {
        // 查询机器人创建者
        String createId = agentBasicConfigService.queryAgentCreateId(request.getAgentCode());
        String format = CharSequenceUtil.format(biHost + chatDataEndpoint,
                request.getChatId(), request.getPage(), request.getPageSize(),
                createId, RequestContext.getTenantId());
        JSONObject taskResult = ChatTaskOpenPoller.pollChatTask(format,
                request.getActiveBusiness(), signKey);
        return Result.success(Objects.isNull(taskResult) ? taskResult : taskResult.get("data"));
    }
}
