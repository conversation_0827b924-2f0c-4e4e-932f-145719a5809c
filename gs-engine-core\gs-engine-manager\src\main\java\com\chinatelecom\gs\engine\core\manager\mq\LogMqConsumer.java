package com.chinatelecom.gs.engine.core.manager.mq;

import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.log.track.LogMessage;
import com.chinatelecom.gs.engine.common.log.track.LogTopicConstants;
import com.chinatelecom.gs.engine.core.manager.service.LogAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @USER: pengmc1
 * @DATE: 2025/5/7 9:03
 */

@Component
@Slf4j
public class LogMqConsumer {

    @Resource
    private LogAppService logAppService;

    @KafkaListener(topics = "${gs.system.mqPrefix:}"+"${gs.system.env:}" + LogTopicConstants.AGENT_LOG_MESSAGE_TOPIC, groupId = "${gs.system.mqPrefix:}"+"${gs.system.env:}" + LogTopicConstants.AGENT_LOG_CONSUMER_GROUP)
    protected void consumerMessage(String message) {
        log.debug("消费链路埋点：{}", message);
        LogMessage logMessage = JSON.parseObject(message, LogMessage.class);
        //log.debug("消费链路埋点：{}， 转换后的数据：{}", message, JSON.toJSONString(logMessage));
        if (Objects.isNull(logMessage)) {
            log.info("埋点数据解析为空");
            return;
        }
        try {
            logMessage.setAddTime(LocalDateTime.now());
            logAppService.saveLog(logMessage);
        } catch (Exception e) {
            log.error("消费埋点数据发生异常，数据为：{}", message, e);
        }
    }
}
