package com.chinatelecom.gs.engine.channel.dao.repository;

import com.chinatelecom.gs.engine.channel.common.UidUtils;
import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelInfoPO;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.ChannelInfoVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
public class ChannelInfoRepositoryTest {
    @Mock
    private ChannelInfoRepository channelInfoRepository;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testUpdateByChannelId() {
        // Arrange
        ChannelInfoPO channelInfo = new ChannelInfoPO();
        boolean expectedResult = true;
        when(channelInfoRepository.updateByChannelId(channelInfo)).thenReturn(expectedResult);

        // Act
        boolean result = channelInfoRepository.updateByChannelId(channelInfo);

        // Assert
        assertTrue(result);
        verify(channelInfoRepository).updateByChannelId(channelInfo);
    }

    @Test
    public void testAddChannelInfo() {
        // Arrange
        String agentCode = "agent123";
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.API;
        String expectedResult = "newChannelId";
        when(channelInfoRepository.addChannelInfo(agentCode, channelTypeEnum)).thenReturn(expectedResult);

        // Act
        String result = channelInfoRepository.addChannelInfo(agentCode, channelTypeEnum);

        // Assert
        assertEquals(expectedResult, result);
        verify(channelInfoRepository).addChannelInfo(agentCode, channelTypeEnum);
    }

    @Test
    public void testGetChannelInfo() {
        // Arrange
        String channelId = "channel123";
        String agentCode = "agent123";
        ChannelInfoDTO expectedDto = new ChannelInfoDTO();
        when(channelInfoRepository.getChannelInfo(channelId, agentCode)).thenReturn(expectedDto);

        // Act
        ChannelInfoDTO result = channelInfoRepository.getChannelInfo(channelId, agentCode);

        // Assert
        assertSame(expectedDto, result);
        verify(channelInfoRepository).getChannelInfo(channelId, agentCode);
    }

    @Test
    public void testGetAllChannelInfo() {
        // Arrange
        List<ChannelInfoVO> expectedList = Arrays.asList(new ChannelInfoVO(), new ChannelInfoVO());
        when(channelInfoRepository.getAllChannelInfo()).thenReturn(expectedList);

        // Act
        List<ChannelInfoVO> resultList = channelInfoRepository.getAllChannelInfo();

        // Assert
        assertSame(expectedList, resultList);
        verify(channelInfoRepository).getAllChannelInfo();
    }

    @Test
    public void testChannelOperateEnable() {
        // Arrange
        String agentCode = "agent123";
        String channelTypeEnum = "WECHAT";
        boolean enable = true;
        boolean expectedResult = true;
        when(channelInfoRepository.channelOperateEnable(agentCode, channelTypeEnum, enable)).thenReturn(expectedResult);

        // Act
        boolean result = channelInfoRepository.channelOperateEnable(agentCode, channelTypeEnum, enable);

        // Assert
        assertTrue(result);
        verify(channelInfoRepository).channelOperateEnable(agentCode, channelTypeEnum, enable);
    }

    @Test
    public void testGetChannelInfoList() {
        // Arrange
        String agentCode = "agent123";
        List<String> channelTypeList = Arrays.asList("WECHAT", "SMS");
        List<ChannelInfoDTO> expectedList = Arrays.asList(new ChannelInfoDTO(), new ChannelInfoDTO());
        when(channelInfoRepository.getChannelInfoList(agentCode, channelTypeList)).thenReturn(expectedList);

        // Act
        List<ChannelInfoDTO> resultList = channelInfoRepository.getChannelInfoList(agentCode, channelTypeList);

        // Assert
        assertSame(expectedList, resultList);
        verify(channelInfoRepository).getChannelInfoList(agentCode, channelTypeList);
    }

    @Test
    public void testRemoveByChannelId() {
        // Arrange
        String channelId = "channel123";
        doNothing().when(channelInfoRepository).removeByChannelId(channelId);

        // Act
        channelInfoRepository.removeByChannelId(channelId);

        // Assert
        verify(channelInfoRepository).removeByChannelId(channelId);
    }

    @Test
    public void testGetDefaultChannel() {
        // Arrange
        String agentCode = "agent123";
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.API;
        String channelId = UidUtils.randomString();
        ChannelInfoPO expectedPo = new ChannelInfoPO();
        expectedPo.setChannelName(channelTypeEnum.getDesc());
        expectedPo.setChannelType(channelTypeEnum);
        expectedPo.setChannelId(channelId);
        expectedPo.setAgentCode(agentCode);
        expectedPo.setEnable(false);

        // Act
        ChannelInfoPO result = ChannelInfoRepository.getDefaultChannel(agentCode, channelTypeEnum);

        // Assert
        assertEquals(expectedPo.getChannelName(), result.getChannelName());
        assertEquals(expectedPo.getChannelType(), result.getChannelType());
        assertEquals(expectedPo.getAgentCode(), result.getAgentCode());
        assertFalse(result.isEnable());
    }

    @Test
    public void testGetDefaultChannelWithNullChannelId() {
        // Arrange
        String agentCode = "agent123";
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.API;
        ChannelInfoPO expectedPo = new ChannelInfoPO();
        expectedPo.setChannelName(channelTypeEnum.getDesc());
        expectedPo.setChannelType(channelTypeEnum);
        expectedPo.setChannelId(null);
        expectedPo.setAgentCode(agentCode);
        expectedPo.setEnable(false);

        // Act
        ChannelInfoPO result = ChannelInfoRepository.getDefaultChannelWithNullChannelId(agentCode, channelTypeEnum);

        // Assert
        assertEquals(expectedPo.getChannelName(), result.getChannelName());
        assertEquals(expectedPo.getChannelType(), result.getChannelType());
        assertNull(result.getChannelId());
        assertEquals(expectedPo.getAgentCode(), result.getAgentCode());
        assertFalse(result.isEnable());
    }
}