package com.chinatelecom.gs.engine.common.constants;

public interface Apis {

    String ROOT_GS = "/ais";

    String ID_PATH = "/{id}";

    String CODE_PATH = "/{code}";

    String WEB_API = "/web";

    String BASE_PREFIX = "/base";

    String MANAGER = WEB_API + "/manage";

    String RPC_PREFIX = "/rpc";

    String OPENAPI = "/openapi";

    String CONFIG = "/config";

    String SEARCH_ANALYZER_CONFIG = "/searchAnalyzerConfig";

    String FILE_API = "/file";

    String UPLOAD = "/upload";
    String IMPORT = "/import";
    String BATCH = "/batch";

    String SYNC = "/sync";

    String CHECK_MD5_API = "/checkMd5";

    String DOWNLOAD_API = "/download";

    String DELETE_API = "/delete";

    String COMMON_PREFIX = "/common";

    String ENTITY_API = "/entity";

    String VARIABLE_API = "/variable";

    String PLATFORM = "/platform";

    String SYSTEM = "/system";

    String INFO = "/info";

    String ALL = "/all";

    String EVENT = "/event";

    String LICENSE_CHECK = "/system/license/check";

    String OPEN = "/open";

    String FORM = "/form";

}
