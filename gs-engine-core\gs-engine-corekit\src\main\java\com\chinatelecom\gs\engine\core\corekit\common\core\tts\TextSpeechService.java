package com.chinatelecom.gs.engine.core.corekit.common.core.tts;

import com.chinatelecom.gs.engine.robot.sdk.answer.InternalSpeechTts;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.speech.Speech;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.speech.SpeechTypeEnum;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;

@Component
public class TextSpeechService {

    @Resource
    private SpeechCheckService speechCheckService;


    public Speech genSpeech(String text){
//        if(!this.speechCheckService.check(text)){
//            return null;
//        }

        Speech speech = new Speech();
        InternalSpeechTts internalSpeechTts = new InternalSpeechTts();
        internalSpeechTts.setText(this.speechCheckService.filter(text));

        speech.setType(SpeechTypeEnum.INTERNAL.getCode());
        speech.setTts(internalSpeechTts);
        return speech;
    }

    public Speech genSpeechWithAudio(String text, List<String> audioUrls){
        if(!this.speechCheckService.check(text)){
            return null;
        }

        Speech speech = new Speech();
        InternalSpeechTts internalSpeechTts = new InternalSpeechTts();
        internalSpeechTts.setAudioUrls(audioUrls);
        internalSpeechTts.setText(text);
        speech.setType(SpeechTypeEnum.INTERNAL.getCode());
        speech.setTts(internalSpeechTts);
        return speech;
    }
}
