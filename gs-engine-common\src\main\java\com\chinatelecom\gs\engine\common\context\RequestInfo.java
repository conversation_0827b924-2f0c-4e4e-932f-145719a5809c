package com.chinatelecom.gs.engine.common.context;

import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月03日
 */
@Getter
@Setter
public class RequestInfo implements Serializable {

    public static final Integer IS_MANAGER = 1;

    public static final Integer NOT_MANAGER = 0;

    private String userId;
    private String userName;
    private String tenantId;
    private String appCode;
    private List<TeamInfo> team;

    private String thirdUserId;

    /**
     * 关联用户ID
     * 目前会用于分享聊天窗对用户多模态资源权限的检查
     */
    private String associatedUserid;

    /**
     * 是否为租户用户，true为租户用户，false为非租户用户(用于mybatis中sql插件过滤条件)
     */
    private Boolean tenant;
    /**
     * 执行操作的应用类型,可能为空，通过Referer请求头进行判断
     */
    private AppSourceType appSourceType;

    /**
     * 记录请求是来自WEB、RPC、OPENAPI
     */
    private RequestSourceType requestSourceType;

    /** 是否是管理员 */
    private Boolean isManager;

    /** 是否是超级管理员 */
    private Boolean isAdmin;

    /**
     * 埋点信息
     */
    private String sessionId;
    /**
     * 埋点信息
     */
    private String messageId;
    /**
     * 日志ID
     */
    private String logId;
    /**
     * 机器人编码
     */
    private String agentCode;
    /**
     * 入口
     */
    private String entry;
    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 扩展字段
     */
    private Map<String, Object> extraParamMap = new HashMap<>();

    /**
     * 是否是超级租户
     */
    private Boolean isSuperTenant = false;

    /**
     * 是否核查知识库的权限
     */
    private Boolean checkRole = true;

    public void putExtraParam(String key, Object value) {
        if (extraParamMap == null) {
            extraParamMap = new java.util.HashMap<>();
        }
        extraParamMap.put(key, value);
    }

    public Object getExtraParam(String key) {
        return extraParamMap.get(key);
    }

    public String getExtraParamString(String key) {
        Object value = extraParamMap.get(key);
        if (value instanceof String string) {
            return string;
        }
        return null;
    }
}
