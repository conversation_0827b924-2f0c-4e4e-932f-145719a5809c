package com.chinatelecom.gs.engine.channel.common;

import com.chinatelecom.gs.engine.common.cache.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/12/25 14:07
 * @description
 */
@Slf4j
@Service
public class SessionIdHolder {

    @Autowired
    private RedisService redisService;

    private static final String SESSION_KEY_PREFIX = "channel_session_id_";

    private static final Long SESSION_KEY_EXPIRE_IN_SECONDS = 5 * 60L;

    /**
     * 生成sessionId， 5分钟超时时间
     * @param channelId
     * @param customerId
     * @return
     */
    public String getSessionId(String channelId, String customerId) {
        String sessionKey = "%s_%s_%s".formatted(SESSION_KEY_PREFIX, channelId, customerId);
        String sessionId = redisService.get(sessionKey);
        if (StringUtils.isEmpty(sessionId)) {
            sessionId = UidUtils.randomString();
            redisService.setEx(sessionKey, sessionId, SESSION_KEY_EXPIRE_IN_SECONDS, TimeUnit.SECONDS);
        }else{
            redisService.expire(sessionKey, SESSION_KEY_EXPIRE_IN_SECONDS, TimeUnit.SECONDS);
        }
        return sessionId;
    }
}
