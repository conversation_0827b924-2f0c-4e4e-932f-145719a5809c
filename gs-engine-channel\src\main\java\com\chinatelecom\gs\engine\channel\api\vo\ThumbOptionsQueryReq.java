package com.chinatelecom.gs.engine.channel.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 根据点踩/点踩查询可支持类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThumbOptionsQueryReq implements Serializable {

    @Schema(description = "agent编码")
    private String agentCode;

    @Schema(description = "点赞:up/点踩:down")
    @NotBlank(message = "thumbType不能为空")
    private String thumbType;

    @Schema(description = "用户ID")
    private String userId;

    /**
     * 请求时间
     */
    @Schema(description = "请求时间")
    private Long requestTime;

    @Schema(description = "请求内容")
    private String content;
}
