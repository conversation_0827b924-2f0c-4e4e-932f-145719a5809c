package com.chinatelecom.gs.engine.common.s3.impl;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.s3.CloudStorageDao;
import com.chinatelecom.gs.engine.common.s3.FileMetadataRes;
import com.chinatelecom.gs.engine.common.s3.ObjectMetadataReq;
import com.chinatelecom.gs.engine.common.s3.ObjectMetadataRes;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class CephCloudDao implements CloudStorageDao {

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    private String bucketName;

    private AmazonS3 amazonS3;

    @PostConstruct
    public void init() {
        GsGlobalConfig.S3Config s3 = gsGlobalConfig.getS3();
        bucketName = s3.getBucketName();
        AWSCredentials awsCredentials = new BasicAWSCredentials(s3.getAccessKey(), s3.getSecretKey());
        try {
            this.amazonS3 = AmazonS3ClientBuilder.standard()
                    .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                    .withEndpointConfiguration(
                            new AwsClientBuilder.EndpointConfiguration(s3.getEndPoint(), ""))
                    .build();
        } catch (Exception e) {
            log.error("S3 Client init error", e);
        }

        createBucket(bucketName);
    }

    public void createBucket(String bucketName) {

        if (existBucket(bucketName)) {
            return;
        }
        Bucket bucket = getS3Client().createBucket(bucketName);
        try {
            BucketLifecycleConfiguration lifecycleConfiguration = new BucketLifecycleConfiguration(ImmutableList.of(
                    new BucketLifecycleConfiguration.Rule()
                            .withId("multipart-upload-rule")
                            .withAbortIncompleteMultipartUpload(new AbortIncompleteMultipartUpload().withDaysAfterInitiation(7))
                            .withStatus(BucketLifecycleConfiguration.ENABLED)
            ));
            getS3Client().setBucketLifecycleConfiguration(bucketName, lifecycleConfiguration);
        } catch (Exception e) {
            log.error("创建bucket后置处理异常，可忽略，bucketName：{}", bucketName, e);
        }
    }

    public boolean existBucket(String bucketName) {
        try {
            return getS3Client().doesBucketExistV2(bucketName);
        } catch (AmazonS3Exception e) {
            if (e.getStatusCode() == 404) {
                return false;
            }
            throw e;
        }
    }

    @Override
    public String upload(String uploadKey, InputStream fileInputStream, long len, String contentTye) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(contentTye);
        objectMetadata.setContentLength(len);
        PutObjectRequest request = new PutObjectRequest(bucketName, uploadKey, fileInputStream, objectMetadata);
        amazonS3.putObject(request);
//        amazonS3.setObjectAcl(bucketName, uploadKey, CannedAccessControlList.PublicReadWrite);
        return uploadKey;
    }

    /**
     * 新增方法：从本地文件系统上传到Ceph
     *
     * @param localFilePath 本地文件路径
     * @param cephKey       Ceph存储键
     */
    public String uploadFromLocalFile(String localFilePath, String cephKey) {
        File file = new File(localFilePath);
        String contentType = null;
        try {
            contentType = Files.probeContentType(Path.of(localFilePath));
        } catch (IOException e) {
            log.error("Failed to determine content type for file: {}", localFilePath, e);
            throw new RuntimeException(e);
        }
        try (InputStream is = Files.newInputStream(file.toPath())) {
            return this.upload(cephKey, is, file.length(), contentType);
        } catch (IOException e) {
            throw new BizException("A0083", "本地文件读取失败: " + e.getMessage());
        }
    }


    @Override
    public void remove(String fileKey) {
        amazonS3.deleteObject(bucketName, fileKey);
    }

    @Override
    public void removeByPrefix(String prefix) {
        if (!StringUtils.endsWith(prefix, "/")) {
            prefix = prefix + "/";
        }

        ListObjectsRequest request = new ListObjectsRequest(bucketName, prefix, null, "/", 1000);
        ObjectListing objectListing = amazonS3.listObjects(request);

        List<S3ObjectSummary> s3ObjList = objectListing.getObjectSummaries();
        List<DeleteObjectsRequest.KeyVersion> delKeys = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(s3ObjList)) {
            for (S3ObjectSummary obj : s3ObjList) {
                delKeys.add(new DeleteObjectsRequest.KeyVersion(obj.getKey()));
            }
        }
        DeleteObjectsRequest delRequest = new DeleteObjectsRequest(bucketName);
        delRequest.setKeys(delKeys);
        amazonS3.deleteObjects(delRequest);
    }

    @Override
    public InputStream download(String fileKey) {
        S3Object s3Object = null;
        try {
            s3Object = amazonS3.getObject(bucketName, fileKey);
        } catch (AmazonS3Exception e) {
            if (e.getStatusCode() == 404) {
                throw new BizException("A0080", "文件不存在");
            } else {
                throw new BizException(e, "A0081", "下载异常");
            }
        } catch (Exception e) {
            throw new BizException(e, "A0081", "下载异常");
        }

        S3ObjectInputStream inputStream = s3Object.getObjectContent();
        return inputStream;
    }

    @Override
    public void copy(String sourceKey, String targetKey) {
        amazonS3.copyObject(bucketName, sourceKey, bucketName, targetKey);
    }


    @Override
    public Boolean bucketExists(String bucketName){
        return amazonS3.doesBucketExistV2(bucketName);
    }

    @Override
    public void makeBucket(String bucketName){
        CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);
        createBucketRequest.setCannedAcl(CannedAccessControlList.PublicReadWrite);
        amazonS3.createBucket(createBucketRequest);
    }

    @Override
    public void removeBucket(String bucketName){
        amazonS3.deleteBucket(bucketName);
    }

    @Override
    public String upload(String bucketName, String uploadKey, InputStream fileInputStream, String orgFileName, long len,
                         String contentTye){
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(contentTye);
        objectMetadata.setContentLength(len);
        PutObjectRequest request = new PutObjectRequest(bucketName, uploadKey, fileInputStream, objectMetadata);
        amazonS3.putObject(request);
//        amazonS3.setObjectAcl(bucketName, uploadKey, CannedAccessControlList.PublicReadWrite);
        return uploadKey + "?fileName=" + orgFileName;
    }

    @Override
    public String startPartUpload(String uploadKey){
        InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(bucketName, uploadKey);
        InitiateMultipartUploadResult initiateMultipartUploadResult = amazonS3.initiateMultipartUpload(initRequest);
        return initiateMultipartUploadResult.getUploadId();
    }

    @Override
    public String uploadPart(String uploadKey, String uploadId, InputStream fileInputStream, long partSize, int partNum){
        UploadPartRequest request = new UploadPartRequest()
                .withBucketName(bucketName)
                .withInputStream(fileInputStream)
                .withPartSize(partSize)
                .withPartNumber(partNum)
                .withUploadId(uploadId)
                .withKey(uploadKey);
        UploadPartResult uploadPartResult = amazonS3.uploadPart(request);
        return uploadPartResult.getETag();
    }

    @Override
    public String completePartUpload(String uploadId, String uploadKey, List<PartETag> partETags){
        try{
            CompleteMultipartUploadRequest request = new CompleteMultipartUploadRequest(bucketName, uploadKey, uploadId, partETags);
            CompleteMultipartUploadResult completeMultipartUploadResult = amazonS3.completeMultipartUpload(request);
            return completeMultipartUploadResult.getKey();
        }catch (Exception e){
            log.error("合并文件分片失败", e);
        }
        return null;
    }

    @Override
    public List<PartETag> listParts(String uploadId, String uploadKey){
        try{
            ListPartsRequest listPartsRequest = new ListPartsRequest(bucketName, uploadKey, uploadId);
            PartListing partListing = amazonS3.listParts(listPartsRequest);
            List<PartETag> partETags = partListing.getParts().stream()
                    .map(part -> new PartETag(part.getPartNumber(), part.getETag()))
                    .collect(Collectors.toList());
            return partETags;
        }catch (Exception e){
            log.error("文件分片不存在", e);
        }
        return Collections.emptyList();
    }

    @Override
    public ObjectMetadata getObjectMetadata(String uploadKey){
        GetObjectMetadataRequest getObjectRequest = new GetObjectMetadataRequest(bucketName, uploadKey);
        ObjectMetadata objectMetadata = amazonS3.getObjectMetadata(getObjectRequest);
        return objectMetadata;
    }

    @Override
    public void abortMultipartUpload(String uploadId, String fileKey) {
        try{
            AbortMultipartUploadRequest request = new AbortMultipartUploadRequest(bucketName, fileKey, uploadId);
            amazonS3.abortMultipartUpload(request);
        }catch (Exception e){
            throw new BizException("AA070", "取消上传失败，文件分片不存在");
        }
    }

    @Override
    public void remove(List<String> fileKeys) {
        if (CollectionUtils.isEmpty(fileKeys)) {
            return;
        }
        List<DeleteObjectsRequest.KeyVersion> keyVersions = fileKeys.stream().map(key -> new DeleteObjectsRequest.KeyVersion(key)).collect(Collectors.toList());
        DeleteObjectsRequest request = new DeleteObjectsRequest(bucketName).withQuiet(true).withKeys(keyVersions);
        amazonS3.deleteObjects(request);
    }

    @Override
    public String uploadByBytes(String bucketName, String uploadKey, byte[] fileBytes){
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(fileBytes.length); // 设置对象的大小（字节数）
        PutObjectRequest request = new PutObjectRequest(bucketName, uploadKey, new ByteArrayInputStream(fileBytes),
                metadata);
        amazonS3.putObject(request);
//        amazonS3.setObjectAcl(bucketName, uploadKey, CannedAccessControlList.PublicReadWrite);
        return uploadKey;
    }

    @Override
    public void remove(String bucketName, String fileKey){
        amazonS3.deleteObject(bucketName, fileKey);
    }

    @Override
    public InputStream download(String bucketName, String fileKey){
        S3Object s3Object = amazonS3.getObject(bucketName, fileKey);
        return s3Object.getObjectContent();
    }

    private String getUrl(String bucketName, String keyName){
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, keyName);
        URL url = amazonS3.generatePresignedUrl(request);
        return url.toString();
    }

    @Override
    public void move(String sourceKey, String targetKey) {
        copy(sourceKey, targetKey);
        remove(sourceKey);
    }

    @Override
    public ObjectMetadataRes download(ObjectMetadataReq req) {
        AmazonS3 conn = getS3Client();
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, req.getFileKey());
        if ( req.getEnd() == null) {
            getObjectRequest.setRange(req.getStart());
        } else {
            getObjectRequest.setRange(req.getStart(), req.getEnd());
        }
        S3Object s3Object = conn.getObject(getObjectRequest);
        Long[] contentRange = s3Object.getObjectMetadata().getContentRange();

        ObjectMetadataRes res = new ObjectMetadataRes();
        res.setInputStream(s3Object.getObjectContent());
        res.setFirstBytePos(contentRange[0]);
        res.setLastBytePos(contentRange[1]);
        res.setEntityLength(s3Object.getObjectMetadata().getInstanceLength());
        return res;
    }

    @Override
    public FileMetadataRes queryMetadata(String fileKey) {
        AmazonS3 s3Client = getS3Client();
        // 创建GetObjectMetadata请求
        GetObjectMetadataRequest request = new GetObjectMetadataRequest(bucketName, fileKey);
        // 发送请求获取元数据
        ObjectMetadata metadata = s3Client.getObjectMetadata(request);
        // 获取文件大小
        long fileSize = metadata.getContentLength();
        return new FileMetadataRes(fileSize);
    }

    public AmazonS3 getS3Client() {
        return amazonS3;
    }
}
