package com.chinatelecom.gs.engine.channel.service.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 文件消息
 *
 * <AUTHOR>
 * @date 2024/1/16 14:34
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileMessageDTO extends BaseSendMessageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7418299971163244788L;

    private JSONObject file;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
