package com.chinatelecom.gs.engine.common.config.aspect;


import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.EvaluationException;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * DebugLogAspect
 *
 * <AUTHOR>
 * @date 2022-11-13 15:48
 */
@Slf4j
@Aspect
@Component
@Order(value = 1)
public class DebugLogAspect {

    @Resource
    private GsGlobalConfig gsGlobalConfig;


    /**
     * 用于SpEL表达式解析.
     */
    private SpelExpressionParser spelExpressionParser = new SpelExpressionParser();
    /**
     * 用于获取方法参数定义名字.
     */
    private DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();


    @Around("@within(debugLog) || @annotation(debugLog)")
    public Object aroundLock(ProceedingJoinPoint point, DebugLog debugLog) throws Throwable {
        if (gsGlobalConfig.getSystem().getLogEnabled()) {
            if (debugLog == null) {
                // 获取类上的注解
                debugLog = point.getTarget().getClass().getDeclaredAnnotation(DebugLog.class);
            }


            Map<String, Object> allArgs = new HashMap<>();
            try {
                try {
                    Object[] args = point.getArgs();

                    MethodSignature methodSignature = (MethodSignature) point.getSignature();
                    String[] paramNames = nameDiscoverer.getParameterNames(methodSignature.getMethod());
                    setArgsInfo(allArgs, paramNames, args);

                    if (args != null) {
                        List<Object> objectList = Arrays.stream(args).filter(o -> {
                            // 偏出一些不需要显示的参数
                            if (o instanceof MultipartFile || o instanceof ServletResponse || o instanceof ServletRequest) {
                                return false;
                            }
                            return true;
                        }).collect(Collectors.toList());

                        args = objectList.toArray();
                    }

                    log.info("{}|request|{}", debugLog.operation(), JsonUtils.toJsonString(args));
                } catch (Throwable e) {
                    log.error("打印日志异常,", e);
                }

                // 执行业务处理
                Object result = point.proceed();

                try {
                    log.info("{}|response|{}", debugLog.operation(), JsonUtils.toJsonString(result));
                } catch (Throwable e) {
                    log.error("打印日志异常,", e);
                }

                return result;
            } catch (Throwable e) {
                throw e;
            }
        } else {
            return point.proceed();
        }
    }

    private void setArgsInfo(Map<String, Object> allArgs, String[] paramNames, Object[] args) {
        for (int i = 0; i < args.length; i++) {
            allArgs.put(paramNames[i], args[i]);
        }
    }

    /**
     * 解析spEL表达式
     */
    private String getValBySpEL(String spEL, Map<String, Object> allArgs) {
        if (StringUtils.isBlank(spEL) || MapUtils.isEmpty(allArgs)) {
            return null;
        }
        Expression expression = spelExpressionParser.parseExpression(spEL);
        // spring的表达式上下文对象
        EvaluationContext context = new StandardEvaluationContext();
        for (Map.Entry<String, Object> entry : allArgs.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }

        try {
            Object obj = expression.getValue(context);
            return String.valueOf(obj);
        } catch (EvaluationException e) {
            log.error("表达解析异常", e);
        } catch (Exception e) {
            log.error("计算EL表达式异常", e);
        }
        return null;
    }
}
