<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.chinatelecom</groupId>
        <artifactId>gs-engine</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.chinatelecom</groupId>
    <artifactId>gs-engine-channel</artifactId>
    <version>2.0.0-SNAPSHOT</version>

    <dependencies>
<!--        升级redisson-->
<!--        <dependency>-->
<!--            <groupId>org.redisson</groupId>-->
<!--            <artifactId>redisson</artifactId>-->
<!--            <version>3.25.2</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-kms</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-workflow-app</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-robot-dialog</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-plugin</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bladejava</groupId>
            <artifactId>blade-kit</artifactId>
            <version>1.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>

        <!-- Mockito -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>5.19.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-reflect</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>7.5.1</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
