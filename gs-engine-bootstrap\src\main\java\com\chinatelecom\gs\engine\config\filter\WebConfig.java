package com.chinatelecom.gs.engine.config.filter;

import com.chinatelecom.gs.engine.config.filter.interceptor.PermissionTagFilter;
import com.chinatelecom.gs.engine.config.filter.interceptor.TraceIdInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import jakarta.annotation.Resource;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Resource
    private TraceIdInterceptor traceIdInterceptor;

    @Resource
    private PermissionTagFilter permissionTagFilter;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(traceIdInterceptor).addPathPatterns("/**").order(Integer.MIN_VALUE);
        registry.addInterceptor(permissionTagFilter).addPathPatterns("/**/web/**").order(2);
    }
}
