package com.chinatelecom.gs.engine.common.es.repositiry.impl;

import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.chinatelecom.gs.engine.common.es.repositiry.AgentEsOperationRepository;
import com.chinatelecom.gs.engine.common.es.repositiry.BaseEsOperationRepository;
import com.chinatelecom.gs.engine.common.es.service.BaseCrudService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.ByQueryResponse;
import org.springframework.data.elasticsearch.core.query.Query;

import jakarta.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

public abstract class BaseEsOperationRepositoryImpl<SE extends BaseCrudService<PO, ID>, DTO, PO, ID extends Serializable>
        extends
        BaseEsRepositoryImpl<SE, DTO, PO, ID> implements BaseEsOperationRepository<DTO, PO, ID> {

    private static final Logger log = LoggerFactory.getLogger(BaseEsOperationRepositoryImpl.class);

    protected Class<PO> poClass = currentPoClass();

    @Resource
    private AgentEsOperationRepository esOperation;

    private Class<PO> currentPoClass(){
        return (Class<PO>) ReflectionKit.getSuperClassGenericType(this.getClass(), BaseEsOperationRepositoryImpl.class,
                2);
    }

    @Override
    public boolean createIndex(String index){
        return esOperation.createIndex(index, poClass);
    }

    @Override
    public boolean deleteIndex(String index){
        return esOperation.deleteIndex(index);
    }

    @Override
    public boolean indexExist(String index){
        return esOperation.exist(index);
    }

    @Override
    public void saveOrUpdate(String index, DTO dtoClass){
        try{
            esOperation.save(index, convertToPo(dtoClass));
        }catch(Exception e){
            log.info("save es error: {}", e.getMessage());
        }
    }

    @Override
    public void bulkSaveOrUpdate(String index, List<DTO> dataList){
        esOperation.bulkSaveOrUpdate(index, convertToPo(dataList));
    }

    @Override
    public void bulkSaveOrUpdate(String index, List<Map<String, Object>> dataList, String idKey){
        esOperation.bulkSaveOrUpdate(index, dataList, map->map.get(idKey).toString());
    }

    @Override
    public void bulkUpdate(String index, List<DTO> dataList){
        esOperation.bulkUpdate(index, convertToPo(dataList));
    }

    @Override
    public void bulkUpdate(String index, List<Map<String, Object>> dataList, String idKey){
        esOperation.bulkUpdate(index, dataList, map->map.get(idKey).toString());
    }

//    @Override
//    public SearchHits<PO> search(String index, QueryBuilder queryBuilder){
//        SearchHits<PO> searchHits = esOperation.search(index, poClass, queryBuilder);
//        return searchHits;
//    }
//
//    @Override
//    public SearchHits<PO> search(String index, QueryBuilder queryBuilder, Pageable pageable){
//        return esOperation.search(index, poClass, queryBuilder, pageable);
//    }

    @Override
    public SearchHits<PO> search(String index, Query query){
        return esOperation.search(index, poClass, query);
    }

    @Override
    public SearchHit<PO> searchOne(String index, Query query){
        return esOperation.searchOne(index, poClass, query);
    }

    public ByQueryResponse delete(String index, Query query){
        return esOperation.delete(index, poClass, query);
    }

}
