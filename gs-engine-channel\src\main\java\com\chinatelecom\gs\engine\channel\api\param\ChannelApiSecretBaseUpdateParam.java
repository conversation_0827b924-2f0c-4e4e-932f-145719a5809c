package com.chinatelecom.gs.engine.channel.api.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @author: xktang
 * @date: 2024/7/26 上午9:29
 * @version: 1.0
 */
@Data
public class ChannelApiSecretBaseUpdateParam {
    @Schema(description = "应用id （机器人id）")
    @NotBlank(message = "应用id不能为空")
    private String appId;
    @Schema(description = "密钥id")
    @NotBlank(message = "secretId不能为空")
    private String secretId;
}
