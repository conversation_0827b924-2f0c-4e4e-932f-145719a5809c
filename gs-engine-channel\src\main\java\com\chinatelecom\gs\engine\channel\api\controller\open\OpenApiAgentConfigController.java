package com.chinatelecom.gs.engine.channel.api.controller.open;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.channel.service.messagehandler.ApiMessageAuthCheckService;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.agent.client.AgentInfoRpcApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.SimpleAgentConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.response.AgentInfoResponse;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;

/**
 * Agent配置信息API控制器
 *
 * @author: xktang
 * @version: 1.0
 */
@Slf4j
@Tag(name = "通用API接入消息入口")
@RestController
@RequestMapping(value = Constants.CHANNEL_PREFIX + Constants.API_PREFIX + "/agent")
public class OpenApiAgentConfigController {

    private static final String AGENT_NOT_FOUND_MSG = "没有找到对应智能体";

    private static final String oldFilePath = "base/web/file/download";
    private static final String newFilePath = "channel/openapi/message/file/download";

    private final AgentInfoRpcApi agentInfoRpcApi;

    private final ApiMessageAuthCheckService apiMessageAuthCheckService;

    public OpenApiAgentConfigController(AgentInfoRpcApi agentInfoRpcApi, ApiMessageAuthCheckService apiMessageAuthCheckService) {
        this.agentInfoRpcApi = agentInfoRpcApi;
        this.apiMessageAuthCheckService = apiMessageAuthCheckService;
    }

    /**
     * 查询Agent配置信息
     *
     * @param agentCode   Agent编码
     * @param requestTime 请求时间戳
     * @param test        是否为测试环境，1表示测试环境
     * @param request     HTTP请求
     * @return Agent配置信息
     */
    @Operation(summary = "获取Agent配置信息")
    @GetMapping(value = "queryAgent", produces = MediaType.APPLICATION_JSON_VALUE)
    @AuditLog(businessType = "获取Agent配置信息", operType = "获取Agent配置信息", operDesc = "获取Agent配置信息", objId="#agentCode")
    public Result<SimpleAgentConfig> queryAgent(
            @RequestParam(value = "agentCode") String agentCode,
            @RequestParam(value = "requestTime", required = false) Long requestTime,
            @RequestParam(value = "test", required = false) Integer test,
            HttpServletRequest request) {

        log.info("【API】获取agent信息请求，agentCode={}", agentCode);

        // 验证请求权限
        validateRequest(agentCode, requestTime, request);

        // 获取Agent信息
        boolean isTestEnv = (test != null && test == 1);
        Result<AgentInfoResponse> result = agentInfoRpcApi.queryAgentInfo(agentCode, isTestEnv);

        if (!result.isSuccess() || result.getData() == null) {
            return Result.failed(AGENT_NOT_FOUND_MSG);
        }

        // 处理并返回Agent配置
        return Result.success(processAgentResponse(result.getData()));
    }

    /**
     * 验证请求权限
     */
    private void validateRequest(String agentCode, Long requestTime, HttpServletRequest request) {
        String accessToken = request.getHeader("accessToken");

        if (StringUtils.isEmpty(accessToken)) {
            validateSignature(agentCode, requestTime, request);
        }
    }

    /**
     * 验证请求签名
     */
    private void validateSignature(String agentCode, Long requestTime, HttpServletRequest request) {
        String sign = request.getHeader(HeaderKeys.APP_SIGN);
        String apiKey = request.getHeader(HeaderKeys.APP_SECRET);
        String userId = request.getHeader(HeaderKeys.USER_ID);

        String signContent = apiMessageAuthCheckService.buildSignContent(
                agentCode, agentCode, userId, requestTime);

        apiMessageAuthCheckService.validReChannelId(apiKey, signContent, sign);
    }

    /**
     * 处理Agent响应并转换为SimpleAgentConfig
     */
    private SimpleAgentConfig processAgentResponse(AgentInfoResponse response) {
        SimpleAgentConfig config = new SimpleAgentConfig();
        BeanUtils.copyProperties(response, config);

        // 替换文件路径为对外访问路径
        config.setAgentPicture(replaceFilePath(config.getAgentPicture()));
        config.setBackgroundImageUrl(replaceFilePath(config.getBackgroundImageUrl()));

        return config;
    }

    /**
     * 替换文件URL路径
     */
    private String replaceFilePath(String url) {
        if (url == null) {
            return null;
        }
        return url.replace(oldFilePath, newFilePath);
    }
}