package com.chinatelecom.gs.engine.core.corekit.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinatelecom.gs.engine.common.infra.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 指令管理
 * </p>
 */
@Getter
@Setter
@TableName("gs_instruct")
public class RobotAiInstructPO extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 应用code
     */
    @TableField(value = "app_code", fill = FieldFill.INSERT)
    private String appCode;

    /**
     * 指令标识
     */
    private String instructCode;

    /**
     * 是否系统指令 0-否 1-是
     */
    private Integer isSystem;

    /**
     * 指令描述
     */
    private String instructDesc;

    /**
     * 指令参数（Json数组字符串）
     */
    private String instructParam;

}
