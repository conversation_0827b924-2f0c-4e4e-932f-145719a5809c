package com.chinatelecom.gs.engine.channel.api.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * @author: xktang
 * @date: 2024/8/1 上午10:38
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema
public class ChannelApiSecretUpdateParam extends ChannelApiSecretBaseUpdateParam{
    @Size(max = 50, message = "密钥名称 长度限制为50个字符")
    @Schema(description = "密钥名称")
    @NotBlank(message = "密钥名称不能为空")
    private String secretName;
}
