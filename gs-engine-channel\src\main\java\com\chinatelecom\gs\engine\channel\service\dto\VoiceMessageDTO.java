package com.chinatelecom.gs.engine.channel.service.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 语音消息
 *
 * <AUTHOR>
 * @date 2024/1/16 14:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoiceMessageDTO extends BaseSendMessageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1059776127268678694L;

    private JSONObject voice;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
