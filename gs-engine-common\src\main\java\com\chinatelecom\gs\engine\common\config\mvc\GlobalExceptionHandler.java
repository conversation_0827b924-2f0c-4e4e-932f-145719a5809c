package com.chinatelecom.gs.engine.common.config.mvc;

import cn.hutool.http.ContentType;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolationException;
import java.io.IOException;

/**
 * GlobalExceptionHandler
 *
 * <AUTHOR>
 * @date 2022-10-15 21:50
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    BizException DEFAULT_EXCEPTION = new BizException("B0001");
    BizException DEFAULT_VALID_EXCEPTION = new BizException("A0001");

    @ExceptionHandler(BizException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public Result handlerBizException(BizException e) {
        String code = e.getCode();
        // 来源于用户的错误不打印错误日志,其他打印错误日志
        if (StringUtils.startsWith(code, "A")) {
            log.warn("业务异常", e);
        } else {
            log.error("业务异常", e);
        }

        return Result.builder()
                .code(e.getCode())
                .message(getMessage(e.getMessage(), e.getUserTip()))
                .userTip(e.getUserTip())
                .build();
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public Result handlerException(Exception e) {
        log.error("系统异常", e);
        return Result.builder()
                .code(DEFAULT_EXCEPTION.getCode())
                .message(getMessage(e.getMessage(), DEFAULT_EXCEPTION.getUserTip()))
                .userTip(DEFAULT_EXCEPTION.getUserTip())
                .build();
    }

    @ExceptionHandler({IllegalArgumentException.class, HttpMessageNotReadableException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public Result handlerParamErrorException(Exception e) {
        log.warn("参数校验异常", e);
        return Result.builder()
                .code(DEFAULT_VALID_EXCEPTION.getCode())
                .message(getMessage(e.getMessage(), DEFAULT_VALID_EXCEPTION.getUserTip()))
                .userTip(DEFAULT_VALID_EXCEPTION.getUserTip())
                .build();
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public Result handlerException(MethodArgumentNotValidException e) {
        return getResponse(e.getBindingResult().getFieldError(), e);
    }

    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public Result validationErrorHandler(BindException ex) {
        return getResponse(ex.getFieldError(), ex);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public Result validationErrorHandler(ConstraintViolationException ex) {
        log.warn("参数校验异常", ex);
        return Result.builder()
                .code(DEFAULT_VALID_EXCEPTION.getCode())
                .message(getMessage(ex.getMessage(), DEFAULT_VALID_EXCEPTION.getUserTip()))
                .userTip(DEFAULT_VALID_EXCEPTION.getUserTip())
                .build();
    }

    @ExceptionHandler(IOException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public void handlerIOException(IOException e, HttpServletResponse response) throws IOException {
        if (response.getContentType() != null && response.getContentType().equals(ContentType.EVENT_STREAM.getValue())) {
            // 如果是 SSE 连接中断，直接关闭响应流
            log.warn("sse IO异常，可能是用户手动中断或网络原因，该错误将忽略");
        } else {
            // 其他类型的异常处理
            log.error("IO异常", e);
            InterceptorUtils.writeError(response, "B0001", "网络异常");
        }
    }

    private Result getResponse(FieldError fieldError, Throwable throwable) {
        String errorMessage = null;
        String userTip = DEFAULT_VALID_EXCEPTION.getUserTip();
        if (fieldError == null) {
            errorMessage = throwable.getMessage();
        } else {
            String field = fieldError.getField();
            String message = fieldError.getDefaultMessage();
            errorMessage = "[%s] - [%s]".formatted(field, message);
            // 参数检查较为常见,以明确的提示进行处理
            userTip = message;
        }
        return Result.builder()
                .code(DEFAULT_VALID_EXCEPTION.getCode())
                .message(getMessage(errorMessage, userTip))
                .userTip(userTip)
                .build();
    }

    /**
     * 获取前端消息内容
     * @param errormessage String
     * @param userTip String
     * @return String
     */
    private String getMessage(String errormessage, String userTip) {
        if (StringUtils.equalsAny(gsGlobalConfig.getSystem().getEnv(), "dev")) {
            return errormessage;
        } else {
            return userTip;
        }
    }
}
