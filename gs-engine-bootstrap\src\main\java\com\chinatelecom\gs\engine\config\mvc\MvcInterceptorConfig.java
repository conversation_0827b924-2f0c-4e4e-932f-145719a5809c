package com.chinatelecom.gs.engine.config.mvc;

import com.chinatelecom.gs.engine.config.mvc.interceptor.VisitsInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.web.servlet.handler.MappedInterceptor;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年04月07日
 */
@Configuration
public class MvcInterceptorConfig {

    @Resource
    private VisitsInterceptor visitsInterceptor;

    @Order(2)
    @Bean
    public MappedInterceptor loginInterceptor() {
        return new MappedInterceptor(new String[]{"/**"}, new String[]{"/ping"}, visitsInterceptor);
    }

}
