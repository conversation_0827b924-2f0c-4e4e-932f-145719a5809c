package com.chinatelecom.gs.engine.core.entity.ability.recognition.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.chinatelecom.gs.engine.common.cache.memory.SessionHistoryCacheService;
import com.chinatelecom.gs.engine.common.utils.VariableParseUtils;
import com.chinatelecom.gs.engine.core.entity.ability.recognition.AbstractEntityRecognitionAbilityService;
import com.chinatelecom.gs.engine.core.entity.common.utils.ParallelUtils;
import com.chinatelecom.gs.engine.robot.sdk.dto.SimpleChatLog;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.StreamingChatLanguageModel;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.LLMRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.MessageRoleEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.ModelProviderEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptTemplateVO;
import com.chinatelecom.gs.engine.kms.service.PromptTemplateAppService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
import com.chinatelecom.gs.engine.robot.sdk.dto.EntityContent;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 * 大模型实体识别能力
 *
 * @USER: pengmc1
 * @DATE: 2025/1/17 18:37
 */

@Slf4j
@Service
public class LlmEntityRecognitionAbilityService extends AbstractEntityRecognitionAbilityService {

    @Resource
    private ExecutorService commonExecutorService;

    @Resource
    private ModelServiceClient remoteServiceClient;

    @Resource
    private StreamingChatLanguageModel streamingChatLanguageModel;

    @Resource
    protected PromptTemplateAppService promptTemplateAppService;

    @Resource
    private SessionHistoryCacheService sessionHistoryCacheService;


    //大模型实体识别prompt模板
    private static final String LLM_ENTITY_RECOGNIZE_PROMPT_TEMPLATE = "LLM_ENTITY_RECOGNIZE_PROMPT_TEMPLATE";

    /**
     * 识别能力标识
     *
     * @return
     */
    @Override
    public String abilityCode() {
        return EntityAbilityEnum.BIG_MODEL_RECOGNITION.getCode();
    }
    /**
     * 执行实体识别
     *
     * @param request
     * @param entityDetailList
     * @return
     */
    @Override
    protected List<Entity> doPredict(EntityRecognitionRequest request, List<EntityDetailVO> entityDetailList) {
        List<Entity> entityList = ParallelUtils.parallelExecute(commonExecutorService, entityDetailList, 60000L, entityDetailVO -> {
            String modelCode = getAbilityConfig(entityDetailVO, EntityAbilityEnum.BIG_MODEL_RECOGNITION).getModelCode();
            ModelPageListParam modelParam = remoteServiceClient.queryByModelCode(modelCode);
            if (modelParam == null) {
                log.error("【实体识别】【{}】未找到模型：{}", abilityCode(), modelCode);
                return null;
            }
            try {
                LLMRequest llmRequest = buildLLMRequest(modelParam, request, entityDetailVO);
                log.info("【实体识别】【{}】大模型请求参数为：{}", abilityCode(), JSON.toJSONString(llmRequest));
                Response<String> llmResponse = streamingChatLanguageModel.syncGenerate(llmRequest);
                log.info("【实体识别】【{}】大模型识别结果为：{}", abilityCode(),llmResponse.content());
                List<Entity> entities = parseResult(llmResponse.content(),entityDetailVO);
                return filterConvertEntity(entityDetailVO, entities);
            } catch (Exception e) {
                log.error("【实体识别】【{}】大模型请求异常", abilityCode(), e);
            }
            return null;
        });
        return entityList;
    }

    /**
     * 构造大模型请求
     * @param modelConfig
     * @param request
     * @return
     */
    private LLMRequest buildLLMRequest(ModelPageListParam modelConfig, EntityRecognitionRequest request, EntityDetailVO entityDetailVO) {
        LLMRequest llmRequest = new LLMRequest();
        llmRequest.getLlmModelInfo().setProvider(ModelProviderEnum.from(modelConfig.getModelProvider()));
        llmRequest.getLlmModelInfo().setModelApi(modelConfig.getApiKey());
        llmRequest.getLlmModelInfo().setModelSecret(modelConfig.getModelSecret());
        llmRequest.getLlmModelInfo().setModelName(modelConfig.getModelCallName());
        llmRequest.getLlmModelInfo().setModelUrl(modelConfig.getExternalModelUrl());
        llmRequest.setStreaming(0);
        llmRequest.setEnableThinking(false);
        llmRequest.setText(buildPrompt(request, entityDetailVO));
        llmRequest.setUserId(IdUtil.fastSimpleUUID());
        if(Objects.nonNull(modelConfig.getExtraDataVO())){
            llmRequest.getLlmModelInfo().setMaxToken(modelConfig.getExtraDataVO().getMaxInputToken());
            llmRequest.getLlmModelInfo().setTransformerType(modelConfig.getExtraDataVO().getTransformerType());
            llmRequest.getLlmModelInfo().setNativeCall(modelConfig.getExtraDataVO().getNativeCall());
            llmRequest.getLlmModelInfo().setNativeCallUrl(modelConfig.getExtraDataVO().getNativeCallUrl());
        }
        return llmRequest;
    }

    /**
     * 构造prompt
     * 实体识别prompt设置支持部分变量
     * {{query}} 文本内容，包含上下文信息
     * {{entityList}} 实体列表描述数据
     * @param request
     * @param entityDetailVO
     * @return
     */
    private String buildPrompt(EntityRecognitionRequest request, EntityDetailVO entityDetailVO){
        PromptTemplateVO promptTemplate = promptTemplateAppService.get(LLM_ENTITY_RECOGNIZE_PROMPT_TEMPLATE);
        if (Objects.isNull(promptTemplate)) {
            throw new BizException("BA010", "未找到大模型实体识别大模型prompt配置");
        }
        Map<String,Object> entity = new HashMap<>();
        entity.put("entityCode", entityDetailVO.getEntityCode());
        entity.put("entityName", entityDetailVO.getEntityName());
        entity.put("entityDesc", entityDetailVO.getEntityDesc());
        entity.put("entityPrompt",buildEntityRecognitionPrompt(request, entityDetailVO));
        //实体值限定范围
        if(CollectionUtils.isNotEmpty(entityDetailVO.getEntityDataList())){
            List<String> entityValues = Lists.newArrayList();
            entityDetailVO.getEntityDataList().forEach( entityData -> {
                entityValues.add(entityData.getEntityValue());
                if(StringUtils.isNotBlank(entityData.getEntitySynonym())){
                    entityValues.addAll(Arrays.asList(entityData.getEntitySynonym().split(",")));
                }
            });
            entity.put("entityValues", entityValues);
        }
        Map<String, Object> variableMap = Maps.newHashMap();
        //获取对话轮次
        StringBuilder queryBuilder = new StringBuilder();
        Integer contextTurns = entityDetailVO.getContextTurns();
        if(Objects.nonNull(contextTurns) && contextTurns > 0){
            //优先取入参中的历史消息
            if(CollectionUtils.isNotEmpty(request.getHistoryMessages())){
                for(SimpleChatLog simpleChatLog : request.getHistoryMessages()){
                    if(MessageRoleEnum.assistant.getCode().equalsIgnoreCase(simpleChatLog.getRole())){
                        queryBuilder.append("机器人：").append(simpleChatLog.getContent()).append("\n");
                    }else if(MessageRoleEnum.user.getCode().equalsIgnoreCase(simpleChatLog.getRole())){
                        queryBuilder.append("用户：").append(simpleChatLog.getContent()).append("\n");
                    }else if(MessageRoleEnum.seat.getCode().equalsIgnoreCase(simpleChatLog.getRole())){
                        queryBuilder.append("坐席：").append(simpleChatLog.getContent()).append("\n");
                    }
                }
            }else{
                List<WrapLLMMessage> llmMessages = sessionHistoryCacheService.getChatHistoryFromCache(request.getSessionId(), request.getMessageId(), contextTurns + 1,true,false);
                if(CollectionUtils.isNotEmpty(llmMessages) && llmMessages.size() > 0){
                    llmMessages = llmMessages.subList(1, llmMessages.size()); //去掉第一个用户说的话
                    if(CollectionUtils.isNotEmpty(llmMessages)){
                        for(WrapLLMMessage llmMessage : llmMessages){
                            if(StringUtils.isNotBlank(llmMessage.getContent())){
                                if(MessageRoleEnum.assistant.getCode().equalsIgnoreCase(llmMessage.getRole())){
                                    queryBuilder.append("机器人：").append(llmMessage.getContent()).append("\n");
                                }else if(MessageRoleEnum.user.getCode().equalsIgnoreCase(llmMessage.getRole())){
                                    queryBuilder.append("用户：").append(llmMessage.getContent()).append("\n");
                                }
                            }
                        }
                    }
                }
            }
        }
        queryBuilder.append("用户：").append(request.getQuery());
        variableMap.put("query", queryBuilder.toString());
        variableMap.put("entityList", JSON.toJSONString(Arrays.asList(entity)));
        return VariableParseUtils.parseVariable(promptTemplate.getContent(), variableMap);
    }

    /**
     * 构建实体识别提示词,支撑如下变量
     * {{entityCode}} 实体编码
     * {{entityName}} 实体名称
     * {{entityDesc}} 实体描述
     * {{query}} 用户query 如果开启上下文，会自动带上
     * @param request
     * @param entityDetailVO
     * @return
     */
    private String buildEntityRecognitionPrompt(EntityRecognitionRequest request, EntityDetailVO entityDetailVO){
        String recognitionPromptTemplate = getRecognitionPromptTemplate(entityDetailVO);
        Map<String, Object> variableMap = Maps.newHashMap();
        variableMap.put("entityCode", entityDetailVO.getEntityCode());
        variableMap.put("entityName", entityDetailVO.getEntityName());
        variableMap.put("entityDesc", entityDetailVO.getEntityDesc());
        variableMap.put("query", request.getQuery());
        return VariableParseUtils.parseVariable(recognitionPromptTemplate, variableMap);
    }

    private String getRecognitionPromptTemplate(EntityDetailVO entityDetailVO) {
        String recognitionPromptTemplate = null;
        if (Objects.nonNull(entityDetailVO.getAbilityConfig())) {
            recognitionPromptTemplate = entityDetailVO.getAbilityConfig().getPrompt();
        } else if (Objects.nonNull(entityDetailVO.getAbilityConfigMap()) && Objects.nonNull(entityDetailVO.getAbilityConfigMap().get(abilityCode()))) {
            recognitionPromptTemplate = entityDetailVO.getAbilityConfigMap().get(abilityCode()).getPrompt();
        }
        return recognitionPromptTemplate;
    }

    /**
     * 解析大模型JSON答案
     * @param content 大模型返回的json格式
     * @return
     */
    private List<Entity> parseResult(String content, EntityDetailVO entityDetailVO){
        List<Entity> entityList = Lists.newArrayList();
        if(StringUtils.isBlank(content)){
            return entityList;
        }
        content = content.replace("```json","").replace("```","").trim();
        if(JSON.isValidArray(content)){
            JSONArray jsonArray = JSON.parseArray(content);
            for(int i=0; i< jsonArray.size(); i++){
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if(entityDetailVO.getEntityCode().equals(jsonObject.getString("entityCode"))
                        && jsonObject.containsKey("entityValues")){
                    JSONArray entityValues = jsonObject.getJSONArray("entityValues");
                    List<EntityContent> entityContents = Lists.newArrayList();
                    for(int j=0; j< entityValues.size(); j++){
                        EntityContent entityContent = new EntityContent();
                        String entityValue = entityValues.getString(j);
                        entityContent.setRawValue(entityValue);
                        entityContent.setValue(entityValue);
                        entityContents.add(entityContent);
                    }
                    if(CollectionUtils.isNotEmpty(entityContents)){
                        Entity entity = new Entity();
                        entity.setEntityCode(entityDetailVO.getEntityCode());
                        entity.setEntityName(entityDetailVO.getEntityName());
                        entity.setTenantId(entityDetailVO.getTenantId());
                        entity.setEntityType(entityDetailVO.getEntityType());
                        entity.setEntityContents(entityContents);
                        entityList.add(entity);
                    }
                }
            }
        }
        return entityList;
    }
}
