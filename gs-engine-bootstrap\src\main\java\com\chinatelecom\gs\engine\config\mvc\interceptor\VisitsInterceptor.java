package com.chinatelecom.gs.engine.config.mvc.interceptor;

import com.chinatelecom.gs.engine.common.enums.VisitsDimensionEnum;
import com.chinatelecom.gs.engine.kms.model.vo.VisitsCreateParam;
import com.chinatelecom.gs.engine.kms.service.VisitsAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年04月07日
 */
@Slf4j
@Component
public class VisitsInterceptor implements HandlerInterceptor {

    @Resource
    private VisitsAppService visitsAppService;

    @Resource
    @Qualifier("defaultPoolExecutor")
    private ExecutorService defaultPoolExecutor;

    @Value("${gs.system.visitsEnabled:true}")
    private Boolean visitsEnabled;


    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           @Nullable ModelAndView modelAndView) throws Exception {
        // 数据库记录
        try {
            if (visitsEnabled) {
                defaultPoolExecutor.submit(() -> {
                    saveRequest(request);
                });
            }
        } catch (Exception e) {
            log.warn("记录用户访问信息异常,错误将忽略", e);
        }
    }

    private void saveRequest(HttpServletRequest request) {
        String bestMatchingPattern = (String) request.getAttribute(
                org.springframework.web.servlet.HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
        String method = request.getMethod();
        log.debug("匹配method:{}, MVC路径：{}", method, bestMatchingPattern);

        VisitsCreateParam param = new VisitsCreateParam();
//            param.setName();
        param.setDimension(VisitsDimensionEnum.USER);
        param.setMethod(method);
        param.setUri(bestMatchingPattern);
        visitsAppService.create(param);
    }

}
