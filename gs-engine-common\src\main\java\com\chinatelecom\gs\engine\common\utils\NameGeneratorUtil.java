package com.chinatelecom.gs.engine.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.function.Function;
import java.util.regex.Pattern;

/**
 * 名称生成工具类
 */
@Slf4j
public class NameGeneratorUtil {

    /**
     * 生成下一个可用的名称
     *
     * @param defaultName 默认名称前缀
     * @param candidates  候选名称列表
     * @param nameGetter  获取名称的函数
     * @param <T>         候选对象类型
     * @return 新的名称
     */
    public static <T> String generateNextAvailableName(String defaultName, List<T> candidates, Function<T, String> nameGetter) {
        // 构建正则表达式模式：前缀 + 数字
        String regex = "^%s[0-9]+$".formatted(Pattern.quote(defaultName));
        Pattern pattern = Pattern.compile(regex);

        // 找到最大的数字后缀
        int maxNumber = 0;
        if (!CollectionUtils.isEmpty(candidates)) {
            for (T candidate : candidates) {
                String name = nameGetter.apply(candidate);
                if (name != null) {
                    // 使用正则表达式匹配
                    if (pattern.matcher(name).matches()) {
                        try {
                            // 提取数字部分
                            String numberStr = name.substring(defaultName.length());
                            int currentNumber = Integer.parseInt(numberStr);
                            maxNumber = Math.max(maxNumber, currentNumber);
                        } catch (NumberFormatException e) {
                            // 忽略非数字后缀的情况
                            log.warn("Invalid number format in name: {}", name);
                        }
                    }
                }
            }
        }

        // 生成新的名称
        return defaultName + (maxNumber + 1);
    }

    /**
     * 查找符合正则表达式的第一个匹配项
     *
     * @param pattern    正则表达式模式
     * @param candidates 候选对象列表
     * @param nameGetter 获取名称的函数
     * @param <T>        候选对象类型
     * @return 匹配的对象，如果没有找到则返回null
     */
    public static <T> T findFirstMatch(String pattern, List<T> candidates, Function<T, String> nameGetter) {
        if (CollectionUtils.isEmpty(candidates)) {
            return null;
        }

        Pattern compiledPattern = Pattern.compile(pattern);
        for (T candidate : candidates) {
            String name = nameGetter.apply(candidate);
            if (name != null && compiledPattern.matcher(name).matches()) {
                return candidate;
            }
        }

        return null;
    }
} 