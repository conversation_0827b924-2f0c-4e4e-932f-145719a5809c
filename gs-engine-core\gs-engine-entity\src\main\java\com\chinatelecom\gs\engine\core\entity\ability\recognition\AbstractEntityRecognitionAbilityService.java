package com.chinatelecom.gs.engine.core.entity.ability.recognition;


import com.alibaba.fastjson2.JSON;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.AbilityConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.core.entity.service.EntityService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
import com.chinatelecom.gs.engine.robot.sdk.dto.EntityContent;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityTypeEnum;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @USER: pengmc1
 * @DATE: 2025/1/17 18:07
 */
@Slf4j
public abstract class AbstractEntityRecognitionAbilityService implements EntityRecognitionAbilityService {

    @Resource
    private EntityService entityService;

    /**
     * 实体识别
     *
     * @param request
     * @return
     */
    @Override
    public List<Entity> predict(EntityRecognitionRequest request) {
        try{
            List<EntityDetailVO> entityDetailList = entityService.getEntityDetailList(request.getEntityCodes());
            if(CollectionUtils.isEmpty(entityDetailList)){
                log.info("【实体识别】【{}】未查询到实体信息，实体编码列表：{}", abilityCode(),JSON.toJSONString(request.getEntityCodes()));
                return null;
            }
            log.info("【实体识别】【{}】请求参数为：{}，实体详情：{}", abilityCode(),JSON.toJSONString(request),JSON.toJSONString(entityDetailList));
            List<Entity> entities = doPredict(request, entityDetailList);
            log.info("【实体识别】【{}】返回结果为：{}", abilityCode(), JSON.toJSONString(entities));
            return entities;
        }catch (Exception e){
            log.error("【实体识别】【{}】实体识别发生异常！", abilityCode(), e);
        }
        return null;
    }

    /**
     * 执行实体识别
     * @param request
     * @param entityDetailList
     * @return
     */
    protected abstract List<Entity> doPredict(EntityRecognitionRequest request, List<EntityDetailVO> entityDetailList);

    /**
     * 转换枚举实体值
     * @param entityDetailVO
     * @param entities
     * @return
     */
    protected List<Entity> filterConvertEntity(EntityDetailVO entityDetailVO, List<Entity> entities){
        //识别结果为空 或 为通用实体时，不进行转换
        if(CollectionUtils.isEmpty(entities) || EntityTypeEnum.NORMAL_ENTITY.getCode().equalsIgnoreCase(entityDetailVO.getEntityType().getCode())){
            return entities;
        }
        //同义词Map映射
        Map<String,String> synonymMap = Maps.newHashMap();
        //实体值Map
        Set<String> valueSet = Sets.newHashSet();
        if(CollectionUtils.isNotEmpty(entityDetailVO.getEntityDataList())){
            entityDetailVO.getEntityDataList().forEach( entityData -> {
                valueSet.add(entityData.getEntityValue());
                if(StringUtils.isNotBlank(entityData.getEntitySynonym())){
                    String[] synonyms = entityData.getEntitySynonym().split(",");
                    for(String synonym : synonyms){
                        synonymMap.put(synonym,entityData.getEntityValue());
                    }
                }
            });
        }
        List<Entity> finalEntities = Lists.newArrayList();
        for(Entity entity : entities){
            List<EntityContent> entityContents = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(entity.getEntityContents())){
                for(EntityContent entityContent : entity.getEntityContents()){
                    String entityValue = entityContent.getValue();
                    if(synonymMap.containsKey(entityValue)){
                        entityContent.setValue(synonymMap.get(entityValue));
                        entityContents.add(entityContent);
                    }else if(valueSet.contains(entityValue)){
                        entityContent.setValue(entityValue);
                        entityContents.add(entityContent);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(entityContents)){
                entity.setEntityContents(entityContents);
                finalEntities.add(entity);
            }
        }
        log.info("【实体识别】转换前实体列表：{}，转换后实体列表：{}", JSON.toJSONString(entities), JSON.toJSONString(finalEntities));
        return finalEntities;
    }

    /**
     * 获取能力配置
     * @return
     */
    public AbilityConfig getAbilityConfig(EntityDetailVO entityDetailVO, EntityAbilityEnum abilityEnum){
        AbilityConfig abilityConfig = null;
        if (Objects.nonNull(entityDetailVO.getAbilityConfigMap()) && Objects.nonNull(entityDetailVO.getAbilityConfigMap().get(abilityEnum.getCode()))) {
            abilityConfig = entityDetailVO.getAbilityConfigMap().get(abilityEnum.getCode());
        }
        //兼容历史数据
        if(Objects.isNull(abilityConfig) && Objects.nonNull(entityDetailVO.getAbilityConfig())){
            abilityConfig = entityDetailVO.getAbilityConfig();
        }
        return abilityConfig;
    }
}
