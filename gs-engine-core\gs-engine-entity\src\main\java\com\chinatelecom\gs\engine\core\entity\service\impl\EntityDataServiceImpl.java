package com.chinatelecom.gs.engine.core.entity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.infra.base.impl.BaseExtendServiceImpl;
import com.chinatelecom.gs.engine.core.entity.domain.po.EntityDataPO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDataDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.core.entity.mapper.EntityDataMapper;
import com.chinatelecom.gs.engine.core.entity.service.EntityDataService;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EntityDataServiceImpl extends BaseExtendServiceImpl<EntityDataMapper, EntityDataPO> implements EntityDataService {

    @Resource
    private EntityDataMapper entityDataMapper;

    @Override
    public Boolean saveEntityData(EntityDetailVO query) {
        List<EntityDataDetailVO> entityDataList = query.getEntityDataList();
        if (EntityTypeEnum.ENUM_ENTITY.equals(query.getEntityType()) && !CollectionUtils.isEmpty(entityDataList)) {
            //清除旧数据
            LambdaQueryWrapper<EntityDataPO> dataPOWrapper = new LambdaQueryWrapper<>();
            dataPOWrapper.eq(EntityDataPO::getEntityCode, query.getEntityCode());
            dataPOWrapper.eq(EntityDataPO::getTenantId, RequestContext.getTenantId());
            entityDataMapper.delete(dataPOWrapper);
            List<EntityDataPO> poList = entityDataList.stream().map(o -> {
                EntityDataPO po = new EntityDataPO();
                BeanUtils.copyProperties(o, po);
                po.setEntityCode(query.getEntityCode());
                po.setTenantId(RequestContext.getTenantId());
                po.setId(null);
                return po;
            }).collect(Collectors.toList());
            return !entityDataMapper.insert(poList).isEmpty();
        }
        return true;
    }

    @Override
    public Boolean delete(List<String> entityCodes) {
        LambdaQueryWrapper<EntityDataPO> dataQueryWrapper = new LambdaQueryWrapper<>();
        dataQueryWrapper.in(EntityDataPO::getEntityCode, entityCodes);
        dataQueryWrapper.eq(EntityDataPO::getTenantId, RequestContext.getTenantId());
        return entityDataMapper.delete(dataQueryWrapper) > 0;
    }

    @Override
    public List<EntityDataPO> getEntityDataList(String entityCode, String tenantId) {
        LambdaQueryWrapper<EntityDataPO> dataQueryWrapper = new LambdaQueryWrapper<>();
        dataQueryWrapper.eq(EntityDataPO::getEntityCode, entityCode);
        dataQueryWrapper.eq(EntityDataPO::getTenantId, tenantId);
        return entityDataMapper.selectList(dataQueryWrapper);
    }
}
