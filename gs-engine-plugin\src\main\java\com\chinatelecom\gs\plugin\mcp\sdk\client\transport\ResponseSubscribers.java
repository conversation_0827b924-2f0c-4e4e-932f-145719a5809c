package com.chinatelecom.gs.plugin.mcp.sdk.client.transport;

import lombok.Getter;
import lombok.ToString;
import org.apache.hc.core5.http.ClassicHttpResponse;
import org.reactivestreams.Subscription;
import reactor.core.publisher.BaseSubscriber;
import reactor.core.publisher.FluxSink;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

class ResponseSubscribers {

    @ToString
    static class SseEvent {
        private String id;
        private String event;
        private String data;

        public SseEvent(String id, String event, String data) {
            this.id = id;
            this.event = event;
            this.data = data;
        }
        public SseEvent() {
        }

        public String getId() { return id; }
        public String getEvent() { return event; }
        public String getData() { return data; }
    }

    public interface ResponseEvent {
        ClassicHttpResponse  responseInfo();
    }

    static class DummyEvent implements ResponseEvent {
        private final ClassicHttpResponse  responseInfo;

        public DummyEvent(ClassicHttpResponse  responseInfo) {
            this.responseInfo = responseInfo;
        }

        @Override
        public ClassicHttpResponse  responseInfo() {
            return responseInfo;
        }
    }

    @ToString
    static class SseResponseEvent implements ResponseEvent {
        private final ClassicHttpResponse  responseInfo;

        @Getter
        private final SseEvent sseEvent;

        public SseResponseEvent(ClassicHttpResponse  responseInfo, SseEvent sseEvent) {
            this.responseInfo = responseInfo;
            this.sseEvent = sseEvent;
        }

        @Override
        public ClassicHttpResponse  responseInfo() {
            return responseInfo;
        }
    }

    static class AggregateResponseEvent implements ResponseEvent {
        private final ClassicHttpResponse  responseInfo;
        @Getter
        private final String data;

        public AggregateResponseEvent(ClassicHttpResponse  responseInfo, String data) {
            this.responseInfo = responseInfo;
            this.data = data;
        }

        @Override
        public ClassicHttpResponse  responseInfo() {
            return responseInfo;
        }

    }


    static void sseToBodySubscriber(ClassicHttpResponse  responseInfo, FluxSink<ResponseEvent> sink) throws IOException {
        InputStream content = responseInfo.getEntity().getContent();
        new SseLineSubscriber(responseInfo, sink).processStream(content);
    }

    static void aggregateBodySubscriber(ClassicHttpResponse  responseInfo, FluxSink<ResponseEvent> sink) throws IOException {
        new AggregateSubscriber(responseInfo, sink).processStream(responseInfo.getEntity().getContent());
    }

    static void bodilessBodySubscriber(ClassicHttpResponse  responseInfo, FluxSink<ResponseEvent> sink) {
        new BodilessResponseLineSubscriber(responseInfo, sink).onSubscribe();
    }

    static class SseLineSubscriber extends BaseSubscriber<String> {
        private static final Pattern EVENT_DATA_PATTERN = Pattern.compile("^data:(.+)$", Pattern.MULTILINE);
        private static final Pattern EVENT_ID_PATTERN = Pattern.compile("^id:(.+)$", Pattern.MULTILINE);
        private static final Pattern EVENT_TYPE_PATTERN = Pattern.compile("^event:(.+)$", Pattern.MULTILINE);

        private final FluxSink<ResponseEvent> sink;
        private final StringBuilder eventBuilder;
        private final AtomicReference<String> currentEventId = new AtomicReference<>();
        private final AtomicReference<String> currentEventType = new AtomicReference<>();
        private final ClassicHttpResponse  responseInfo;

        public SseLineSubscriber(ClassicHttpResponse  responseInfo, FluxSink<ResponseEvent> sink) {
            this.responseInfo = responseInfo;
            this.sink = sink;
            this.eventBuilder = new StringBuilder();
        }

        public void processStream(InputStream inputStream) throws IOException {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("ping")) {
                        break;
                    }
                    hookOnNext(line);
                }
                hookOnComplete();
            }
        }

        @Override
        protected void hookOnSubscribe(Subscription subscription) {
            sink.onRequest(n -> subscription.request(n));
            sink.onDispose(subscription::cancel);
        }

        @Override
        protected void hookOnNext(String line) {
            if (line.isEmpty()) {
                if (eventBuilder.length() > 0) {
                    String eventData = eventBuilder.toString();
                    SseEvent sseEvent = new SseEvent(currentEventId.get(), currentEventType.get(), eventData.trim());
                    sink.next(new SseResponseEvent(responseInfo, sseEvent));
                    eventBuilder.setLength(0);
                }
            } else {
                if (line.startsWith("data:")) {
                    Matcher matcher = EVENT_DATA_PATTERN.matcher(line);
                    if (matcher.find()) {
                        eventBuilder.append(matcher.group(1).trim()).append("\n");
                    }
                } else if (line.startsWith("id:")) {
                    Matcher matcher = EVENT_ID_PATTERN.matcher(line);
                    if (matcher.find()) {
                        currentEventId.set(matcher.group(1).trim());
                    }
                } else if (line.startsWith("event:")) {
                    Matcher matcher = EVENT_TYPE_PATTERN.matcher(line);
                    if (matcher.find()) {
                        currentEventType.set(matcher.group(1).trim());
                    }
                }
            }
        }

        @Override
        protected void hookOnComplete() {
            if (eventBuilder.length() > 0) {
                String eventData = eventBuilder.toString();
                SseEvent sseEvent = new SseEvent(currentEventId.get(), currentEventType.get(), eventData.trim());
                sink.next(new SseResponseEvent(responseInfo, sseEvent));
            }
            sink.complete();
        }

        @Override
        protected void hookOnError(Throwable throwable) {
            sink.error(throwable);
        }
    }

    static class AggregateSubscriber extends BaseSubscriber<String> {
        private final FluxSink<ResponseEvent> sink;
        private final StringBuilder eventBuilder;
        private ClassicHttpResponse  responseInfo;

        public AggregateSubscriber(ClassicHttpResponse  responseInfo, FluxSink<ResponseEvent> sink) {
            this.responseInfo = responseInfo;
            this.eventBuilder = new StringBuilder();
            this.sink = sink;
        }

        public void processStream(InputStream inputStream) throws IOException {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    hookOnNext(line);
                }
                hookOnComplete();
            }
        }

        @Override
        protected void hookOnSubscribe(Subscription subscription) {
            sink.onRequest(n -> subscription.request(n));
            sink.onDispose(subscription::cancel);
        }

        @Override
        protected void hookOnNext(String line) {
            eventBuilder.append(line).append("\n");
        }

        @Override
        protected void hookOnComplete() {
            if (eventBuilder.length() > 0) {
                sink.next(new AggregateResponseEvent(responseInfo, eventBuilder.toString()));
            }
            sink.complete();
        }

        @Override
        protected void hookOnError(Throwable throwable) {
            sink.error(throwable);
        }
    }

    static class BodilessResponseLineSubscriber extends BaseSubscriber<String> {
        private final FluxSink<ResponseEvent> sink;
        private final ClassicHttpResponse  responseInfo;

        public BodilessResponseLineSubscriber(ClassicHttpResponse  responseInfo, FluxSink<ResponseEvent> sink) {
            this.responseInfo = responseInfo;
            this.sink = sink;
        }

        public void onSubscribe() {
            sink.next(new DummyEvent(responseInfo));
            sink.complete();
        }
    }
}