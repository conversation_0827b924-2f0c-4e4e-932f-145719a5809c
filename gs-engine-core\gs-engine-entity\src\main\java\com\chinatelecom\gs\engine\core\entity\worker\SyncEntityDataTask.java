package com.chinatelecom.gs.engine.core.entity.worker;

import com.chinatelecom.gs.engine.core.entity.ability.recognition.impl.TrieEntityRecognitionAbilityService;
import com.chinatelecom.gs.engine.core.entity.service.EntityService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @USER: pengmc1
 * @DATE: 2025/1/21 14:00
 */

@Component
public class SyncEntityDataTask {

    @Resource
    private EntityService entityService;

    @Resource
    private TrieEntityRecognitionAbilityService trieEntityRecognitionAbilityService;

    @Scheduled(cron="0 0/1 * * * ?") // 1分钟执行一次
    public void syncEntityData() {
        List<String> tenantIds = entityService.queryAllTenant();
        for(String tenantId : tenantIds){
            trieEntityRecognitionAbilityService.doBuild(tenantId);
        }
    }

}
