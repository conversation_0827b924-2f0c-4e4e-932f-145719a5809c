package com.chinatelecom.gs.engine.core.entity.service;

import com.chinatelecom.gs.engine.common.infra.base.BaseExtendService;
import com.chinatelecom.gs.engine.core.entity.domain.po.EntityPO;
import com.chinatelecom.gs.engine.core.entity.domain.query.EntityQuery;
import com.chinatelecom.gs.engine.core.entity.domain.request.EntityDeleteRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.Page;

import java.util.List;

/**
 * @USER: pengmc1
 * @DATE: 2025/1/16 19:44
 */
public interface EntityService extends BaseExtendService<EntityPO> {
    /**
     * 获取实体基本信息
     * @param entityCode
     * @return
     */
    EntityVO getEntity(String entityCode);

    /**
     * 批量获取实体基本信息
     * @param entityCodes
     * @return
     */
    List<EntityVO> getEntityList(List<String> entityCodes);

    /**
     * 查询实体详情
     * @param entityCode
     * @return
     */
    EntityDetailVO getEntityDetail(String entityCode);

    /**
     * 批量查询实体详情
     * @param entityCodes
     * @return
     */
    List<EntityDetailVO> getEntityDetailList(List<String> entityCodes);

    /**
     * 查询实体详情列表
     * @param query
     * @return
     */
    Page<EntityDetailVO> queryEntityDetailList(EntityQuery query);

    /**
     * 查询实体详情列表
     * @param tenantId
     * @param entityType
     * @param pageNum
     * @param pageSize
     * @return
     */
    Page<EntityDetailVO> queryEntityDetailList(String tenantId,String entityType, Integer pageNum, Integer pageSize);
    /**
     * 获取所有租户ID
     * @return
     */
    List<String> queryAllTenant();

    /**
     * 添加实体
     * @param query
     * @return
     */
    Boolean addEntity(EntityDetailVO query);

    /**
     * 更新实体
     * @param query
     * @return
     */
    Boolean updateByCode(EntityDetailVO query);

    /**
     * 删除实体
     * @param query
     * @return
     */
    Boolean delete(EntityDeleteRequest query);
}
