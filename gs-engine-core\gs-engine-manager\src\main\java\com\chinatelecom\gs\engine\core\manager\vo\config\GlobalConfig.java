package com.chinatelecom.gs.engine.core.manager.vo.config;

import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GlobalConfig implements ConfigValidator {
    private String appLogo;
    private String appName;
    private String appImage;
    /**
     * 横幅开关
     */
    private Boolean appBannerEnable = true;
    /**
     * 横幅地址
     */
    private String appBanner;


    /**
     * 导出按钮隐藏
     */
    private Boolean exportButtonHidden = false;

    /**
     * pdf展示是否降级,默认不降级，只有指定明确需要降级的才进行降级处理
     */
    private Boolean pdfDemotion = false;

    /**
     * 首页地址
     */
    private String homePage = "";

    @Override
    public void validate(Map<String, Object> param) throws BizException {
//        if (StringUtils.isAnyBlank(appLogo, appName, appImage)) {
//            throw new BizException("A0001", "配置参数不能为空");
//        }
    }
}
