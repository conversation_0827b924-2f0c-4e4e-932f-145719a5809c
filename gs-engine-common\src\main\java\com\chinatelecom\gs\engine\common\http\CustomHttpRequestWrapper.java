package com.chinatelecom.gs.engine.common.http;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;

public class CustomHttpRequestWrapper extends HttpServletRequestWrapper {

    private final String headerName;

    private final String headerValue;

    public CustomHttpRequestWrapper(HttpServletRequest request, String headerName, String headerValue){
        super(request);
        this.headerName = headerName;
        this.headerValue = headerValue;
    }


    @Override
    public String getHeader(String name){
        if(this.headerName.equalsIgnoreCase(name)){
            return this.headerValue;
        }
        return super.getHeader(name);
    }

    @Override
    public Enumeration<String> getHeaders(String name){
        if(this.headerName.equalsIgnoreCase(name)){
            // Create an enumeration containing just the new header value.
            return Collections.enumeration(Collections.singletonList(this.headerValue));
        }
        return super.getHeaders(name);
    }

    @Override
    public Enumeration<String> getHeaderNames(){
        // Add the new header to the list of header names.
        Enumeration<String> names = super.getHeaderNames();
        List<String> namesList = new ArrayList<>();
        while(names.hasMoreElements()){
            namesList.add(names.nextElement());
        }
        namesList.add(this.headerName);
        return Collections.enumeration(namesList);
    }
}
