package com.chinatelecom.gs.engine.core.manager.search.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.IPageUtils;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.core.manager.convert.SearchAnalyzerConfigConvert;
import com.chinatelecom.gs.engine.core.manager.param.CodeParam;
import com.chinatelecom.gs.engine.core.manager.search.AnalyzerTypeEnum;
import com.chinatelecom.gs.engine.core.manager.search.SearchAnalyzerConfigRepository;
import com.chinatelecom.gs.engine.core.manager.search.SearchAnalyzerConfigService;
import com.chinatelecom.gs.engine.core.manager.search.dto.SearchAnalyzerConfigCreateParam;
import com.chinatelecom.gs.engine.core.manager.search.dto.SearchAnalyzerConfigDTO;
import com.chinatelecom.gs.engine.core.manager.search.dto.SearchAnalyzerConfigQueryParam;
import com.chinatelecom.gs.engine.core.manager.search.po.SearchAnalyzerConfigPO;
import com.chinatelecom.gs.engine.core.manager.search.vo.SearchAnalyzerConfigVO;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@Service
public class SearchAnalyzerConfigServiceImpl implements SearchAnalyzerConfigService {

    @Resource
    private SearchAnalyzerConfigRepository repository;

    protected SearchAnalyzerConfigConvert converter() {
        return SearchAnalyzerConfigConvert.INSTANCE;
    }


    @Override
    public List<SearchAnalyzerConfigDTO> getByDimensionAndType(DimensionEnum dimension, AnalyzerTypeEnum analyzerType) {
        return repository.selectByDimensionAndType(dimension, analyzerType);
    }


    @Override
    public List<SearchAnalyzerConfigDTO> getByDimensionsAndTypeWithBusinessNo(List<DimensionEnum> dimensions, AnalyzerTypeEnum analyzerType, String tenantId) {
        return repository.selectByDimensionsAndTypeWithBusinessNo(dimensions, analyzerType, tenantId);
    }

    @Override
    public List<SearchAnalyzerConfigDTO> getAllConfigs() {
        return repository.listByMap(new HashMap<>());
    }

    protected Wrapper<SearchAnalyzerConfigPO> pageQueryWrapper(SearchAnalyzerConfigQueryParam query) {
        LambdaQueryWrapper<SearchAnalyzerConfigPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(StringUtils.isNotBlank(query.getConfigValue()), SearchAnalyzerConfigPO::getConfigValue, query.getConfigValue());
        lambdaQueryWrapper.eq(query.getAnalyzerType() != null, SearchAnalyzerConfigPO::getAnalyzerType, query.getAnalyzerType());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(query.getBusinessNo()), SearchAnalyzerConfigPO::getBusinessNo, query.getBusinessNo());
        return lambdaQueryWrapper;
    }

    @Override
    public Page<SearchAnalyzerConfigVO> pageQuery(SearchAnalyzerConfigQueryParam query) {
        IPage<SearchAnalyzerConfigDTO> page = new PageDTO<>(query.getPageNum(), query.getPageSize());
        Wrapper<SearchAnalyzerConfigPO> queryWrapper = pageQueryWrapper(query);
        page = repository.page(page, queryWrapper);
        return IPageUtils.convert(page, dto -> converter().convertVO(dto));
    }

    /**
     * 验证配置格式是否符合要求
     * @param analyzerType 分析器类型
     * @param configValue 配置值
     */
    private void validateConfigFormat(AnalyzerTypeEnum analyzerType, String configValue) {
        BizAssert.notEmpty(configValue, "AA106","配置值不能为空");
        if (AnalyzerTypeEnum.REPLACE_SYNONYM == analyzerType) {
            // REPLACE_SYNONYM类型必须包含 => 符号
            if (!configValue.contains("=>")) {
                BizAssert.throwBizException("AA106","替换同义词格式必须为word1=>word2,word3格式，例如债券=>股票");
            }
        } else if (AnalyzerTypeEnum.MUTUAL_SYNONYM == analyzerType) {
            // MUTUAL_SYNONYM类型必须包含逗号，且不能包含 => 符号
            if (!configValue.contains(",")) {
                BizAssert.throwBizException("AA106","互为同义词格式必须包含逗号，如word1,word2,word3");

            }
            if (configValue.contains("=>")) {
                BizAssert.throwBizException("AA106","互为同义词格式不允许包含=>符号");

            }
        }
    }

    @Override
    public void saveConfig(SearchAnalyzerConfigCreateParam param) {
        // 验证配置格式
        validateConfigFormat(param.getAnalyzerType(), param.getConfigValue());
        
        SearchAnalyzerConfigDTO dto = new SearchAnalyzerConfigDTO();
        dto.setCode(IdGenerator.getId(StringUtils.EMPTY));
        dto.setAnalyzerType(param.getAnalyzerType());
        dto.setDimension(param.getDimension());
        dto.setConfigValue(param.getConfigValue());
        dto.setDescription(param.getDescription());

        // 设置业务编号
        if (param.getDimension() == DimensionEnum.SYSTEM) {
            dto.setBusinessNo(DimensionEnum.SYSTEM.toString());
        } else if (param.getDimension() == DimensionEnum.TENANT && StringUtils.isBlank(param.getBusinessNo())) {
            dto.setBusinessNo(RequestContext.getTenantId());
        } else {
            BizAssert.notEmpty(param.getBusinessNo(), "AA055", "业务标识不能为空");
            dto.setBusinessNo(param.getBusinessNo());
        }
        repository.save(dto);
    }

    @Override
    public Boolean updateConfig(SearchAnalyzerConfigCreateParam param) {
        // 验证配置格式
        validateConfigFormat(param.getAnalyzerType(), param.getConfigValue());
        
        SearchAnalyzerConfigDTO dto = converter().convertCreate(param);
        dto.setId(null);
        return repository.updateOne(dto, Wrappers.<SearchAnalyzerConfigPO>lambdaUpdate().eq(SearchAnalyzerConfigPO::getCode, param.getCode()));
    }

    @Override
    public Boolean delete(CodeParam codes) {
        return repository.remove(Wrappers.<SearchAnalyzerConfigPO>lambdaUpdate().in(SearchAnalyzerConfigPO::getCode, codes.getCodes()));
    }


}