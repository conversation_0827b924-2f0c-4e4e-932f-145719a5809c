package com.chinatelecom.gs.engine.core.corekit.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.log.track.LogTopicConstants;
import com.chinatelecom.gs.engine.common.mq.impl.KafkaMQService;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.corekit.domain.dto.EventMqDTO;
import com.chinatelecom.gs.engine.core.corekit.domain.request.EventRequest;
import com.chinatelecom.gs.engine.core.corekit.service.EventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 事件服务实现类
 * @USER: pengmc1
 * @DATE: 2025/5/20 14:19
 */

@Slf4j
@Service
@RefreshScope
public class EventServiceImpl implements EventService {

    @Resource
    private KafkaMQService kafkaMQService;

    @Value("${send.pageMsg:false}")
    private Boolean sendPageMsg;


    /**
     * 发送事件消息
     *
     * @param eventRequest
     * @return
     */
    @Override
    public Boolean sendEvent(EventRequest eventRequest) {
        if (Boolean.FALSE.equals(sendPageMsg)) {
            return false;
        }
        if(Objects.isNull(eventRequest)){
            return false;
        }
        EventMqDTO eventMqDTO = convertToMqDTO(eventRequest);
        try{
            eventMqDTO.setSendTime(LocalDateTime.now());
            kafkaMQService.sendMessage(LogTopicConstants.PAGE_OPERATE_TOPIC, JsonUtils.toJsonString(eventMqDTO));
        } catch (Exception e) {
            log.error("【页面事件埋点】发送页面事件埋点消息发生异常！", e);
            return false;
        }
        return true;
    }

    /**
     * 转换为埋点MQ消息
     * @param eventRequest
     * @return
     */
    private EventMqDTO convertToMqDTO(EventRequest eventRequest){
        EventMqDTO eventMqDTO = new EventMqDTO();
        eventMqDTO.setLogId(eventRequest.getLogId());
        eventMqDTO.setAppCode(eventRequest.getAppCode());
        eventMqDTO.setSource(RequestContext.getAppSourceType().name());
        eventMqDTO.setTenantId(RequestContext.getTenantId());
        eventMqDTO.setUserId(RequestContext.getUserId());
        eventMqDTO.setEventType(eventRequest.getEventType());
        eventMqDTO.setTerminal(eventRequest.getTerminal());
        eventMqDTO.setBrowser(eventRequest.getBrowser());
        eventMqDTO.setRefer(eventRequest.getRefer());
        eventMqDTO.setUrl(eventRequest.getUrl());
        if(Objects.nonNull(eventRequest.getMeta())){
            eventMqDTO.setPageName(eventRequest.getMeta().getA());
            eventMqDTO.setModuleName(eventRequest.getMeta().getB());
            eventMqDTO.setButtonName(eventRequest.getMeta().getC());
            eventMqDTO.setParams(JSON.toJSONString(eventRequest.getMeta().getParams()));
        }
        eventMqDTO.setTimestamp(eventRequest.getTimestamp());
        return eventMqDTO;
    }
}
