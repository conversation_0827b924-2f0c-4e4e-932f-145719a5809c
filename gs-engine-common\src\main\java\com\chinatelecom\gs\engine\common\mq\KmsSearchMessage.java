package com.chinatelecom.gs.engine.common.mq;

import com.chinatelecom.gs.engine.robot.sdk.constant.CommonConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;

/**
 * 知识库搜索MQ消息
 * @USER: pengmc1
 * @DATE: 2025/5/21 14:35
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(indexName = "#{@environment.getProperty('gs.system.esPrefix') + @environment.getProperty('gs.system.env')}" + CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME)
public class KmsSearchMessage {
    /**
     * 日志ID
     */
    @Id
    private String logId;
    /**
     * 租户ID
     */
    @Field(type = FieldType.Keyword)
    private String tenantId;
    /**
     * 用户ID
     */
    @Field(type = FieldType.Keyword)
    private String userId;
    /**
     * 用户Query
     */
    @Field(type = FieldType.Keyword)
    private String query;
    /**
     * 参数
     */
    @Field(type = FieldType.Text)
    private String params;
    /**
     * 搜索时间
     */
    @Field(type = FieldType.Date, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime searchTime;
}
