package com.chinatelecom.gs.engine.channel.foundation;

import com.chinatelecom.gs.engine.channel.service.dto.RobotConfigDTO;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.center.PlatformDialogCenterApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.ChatTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.FinalMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.MessageRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.BotAnswer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
public class BotPlatformDialogTest {
    @Mock
    private PlatformDialogCenterApi platformDialogCenterApi;

    @InjectMocks
    private BotPlatformDialog botPlatformDialog;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testChatSuccess() {
        // Arrange
        String userId = "user1";
        String sessionId = "session1";
        String messageId = "message1";
        String message = "Hello, World!";
        RobotConfigDTO robotConfig = new RobotConfigDTO();
        robotConfig.setBotCode("bot1");

        MessageRequest request = new MessageRequest();
        request.setChatType(ChatTypeEnum.CHAT.getCode());
        request.setAgentCode(robotConfig.getBotCode());
        request.setContent(message);
        request.setSessionId(sessionId);
        request.setMessageId(messageId);
        request.setUserId(userId);

        FinalMessageResponse dialogResponse = new FinalMessageResponse();
        List<BotAnswer> answers = Collections.singletonList(new BotAnswer());
        // 使用setter方法确保answers被正确设置
        dialogResponse.setAnswers(answers);

        Result<FinalMessageResponse> result = new Result<>();
        result.setCode("00000"); // 确保结果标记为成功
        result.setData(dialogResponse);

        when(platformDialogCenterApi.dialog(request)).thenReturn(result);

        // Act
        List<BotAnswer> resultAnswers = botPlatformDialog.chat(userId, sessionId, messageId, message, robotConfig);

        // Assert
        assertEquals(1, resultAnswers.size());
        assertNotNull(resultAnswers.get(0));
    }


    @Test
    public void testChatFailure() {
        // Arrange
        String userId = "user1";
        String sessionId = "session1";
        String messageId = "message1";
        String message = "Hello, World!";
        RobotConfigDTO robotConfig = new RobotConfigDTO();
        robotConfig.setBotCode("bot1");

        MessageRequest request = new MessageRequest();
        request.setChatType(ChatTypeEnum.CHAT.getCode());
        request.setAgentCode(robotConfig.getBotCode());
        request.setContent(message);
        request.setSessionId(sessionId);
        request.setMessageId(messageId);
        request.setUserId(userId);

        Result<FinalMessageResponse> result = new Result<>();

        when(platformDialogCenterApi.dialog(request)).thenReturn(result);

        // Act
        List<BotAnswer> resultAnswers = botPlatformDialog.chat(userId, sessionId, messageId, message, robotConfig);

        // Assert
        assertTrue(resultAnswers.isEmpty());
    }

    @Test
    public void testChatException() {
        // Arrange
        String userId = "user1";
        String sessionId = "session1";
        String messageId = "message1";
        String message = "Hello, World!";
        RobotConfigDTO robotConfig = new RobotConfigDTO();
        robotConfig.setBotCode("bot1");

        MessageRequest request = new MessageRequest();
        request.setChatType(ChatTypeEnum.CHAT.getCode());
        request.setAgentCode(robotConfig.getBotCode());
        request.setContent(message);
        request.setSessionId(sessionId);
        request.setMessageId(messageId);
        request.setUserId(userId);

        doThrow(new RuntimeException("Test Exception")).when(platformDialogCenterApi).dialog(request);

        // Act
        List<BotAnswer> resultAnswers = botPlatformDialog.chat(userId, sessionId, messageId, message, robotConfig);

        // Assert
        assertTrue(resultAnswers.isEmpty());
    }
}