package com.chinatelecom.gs.engine.channel.api.vo;

import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/25 14:34
 * @description
 */
@Data
@Schema(description = "渠道对象")
public class ChannelInfoVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -559888949117161960L;

    @Schema(description = "渠道名称")
    private String channelName;

    @Schema(description = "渠道类型")
    private ChannelTypeEnum channelType;

    @Schema(description = "渠道id")
    private String channelId;

    /**
     * 机器人code
     */
    @Schema(description = "机器人code")
    private String agentCode;
    /**
     * 渠道是否启用
     */
    @Schema(description = "渠道是否启用")
    private boolean enable;

    @Schema(description = "渠道配置是否存在")
    private boolean isConfigExist;
}
