package com.chinatelecom.gs.engine.channel.common;

import com.alibaba.fastjson.JSONObject;
import com.chinatelecom.gs.engine.channel.common.cache.localcache.ChannelCaches;
import com.chinatelecom.gs.engine.channel.common.utils.HttpUtils;
import com.chinatelecom.gs.engine.channel.service.dto.QywxConfigDTO;
import com.chinatelecom.gs.engine.channel.service.dto.WeixinGetTokenRespDTO;
import com.chinatelecom.gs.engine.common.cache.RedisService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/1/16 15:18
 */
@Service
@Slf4j
public class AccessTokenUtil {

    @Value("${weixin.token.get.url:}")
    private String weixinTokenGetUrl;

    @Resource
    private ChannelCaches caches;

    @Autowired(required = false)
    private RedisService redisService;

    private static final String CACHE_KEY = "csrobot_channel_accesstoken_";

    /**
     * 获取accessToken
     *
     * @param channelId 渠道ID
     */
    public String getAccessToken(String channelId) {
        String key = CACHE_KEY + channelId;
        //先从缓存取，没有就去取新的
        String token = redisService.get(key);
        if (StringUtils.isEmpty(token)) {
            token = getTokenByAppId(channelId);
            if (StringUtils.isNotEmpty(token)) {
                redisService.setEx(key, token, 60L, TimeUnit.MINUTES);
            }
        }
        return token;
    }

    public String getTokenByAppId(String channelId) {
        try {
            QywxConfigDTO configDTO = caches.getWxCryptConfig(channelId);
            if (configDTO == null) {
                return null;
            }
            String url = weixinTokenGetUrl + "?corpid=" + configDTO.getCorpId() + "&corpsecret=" + configDTO.getSecret();
            Response response = HttpUtils.doGet(url, new HashMap<>());
            if (response == null || response.body() == null) {
                return null;
            }
            String resStr = response.body().string();
            log.info("weixin get token res:{}", resStr);
            WeixinGetTokenRespDTO respDTO = JSONObject.parseObject(resStr, WeixinGetTokenRespDTO.class);
            if (respDTO != null) {
                return respDTO.getAccessToken();
            }
        } catch (Exception e) {
            log.error("weixin get token error", e);
        }
        return null;
    }
}
