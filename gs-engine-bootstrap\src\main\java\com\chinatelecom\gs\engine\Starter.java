package com.chinatelecom.gs.engine;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(scanBasePackages = {"com.chinatelecom.gs.engine", "com.chinatelecom.gs.workflow", "com.chinatelecom.gs.plugin", "com.chinatelelcom.gs.engine.sdk", "com.telecom.ais.telephone"})
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableTransactionManagement(proxyTargetClass = true, order = 1000)
@EnableRetry(proxyTargetClass = true)
@EnableScheduling
@EnableFeignClients(basePackages = "com.chinatelecom.gs.plugin")
public class Starter {

    public static void main(String[] args) {
        SpringApplication.run(Starter.class, args);
    }
}
