package com.chinatelecom.gs.engine.channel.openai.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
public class ChatCompletionRequestTest {
    private ChatCompletionRequest chatCompletionRequest;

    @BeforeEach
    public void setUp() {
        chatCompletionRequest = new ChatCompletionRequest();
    }


    @Test
    public void testSettersAndGetters() {
        // Arrange
        String model = "gpt-3.5";
        String sessionId = "session123";
        List<ChatMessage> messages = Arrays.asList(new ChatMessage(), new ChatMessage());
        Float temperature = 0.7f;
        Float top_p = 0.9f;
        Boolean stream = true;
        List<String> stop = Arrays.asList("stop1", "stop2");
        Integer max_tokens = 100;
        Float presence_penalty = -1.5f;
        Float frequency_penalty = 1.5f;
        String user = "user123";
        Integer n = 2;
        Map<String, Object> extraData = new HashMap<>();
        extraData.put("key1", "value1");

        // Act
        chatCompletionRequest.setModel(model);
        chatCompletionRequest.setSessionId(sessionId);
        chatCompletionRequest.setMessages(messages);
        chatCompletionRequest.setTemperature(temperature);
        chatCompletionRequest.setTop_p(top_p);
        chatCompletionRequest.setStream(stream);
        chatCompletionRequest.setStop(stop);
        chatCompletionRequest.setMax_tokens(max_tokens);
        chatCompletionRequest.setPresence_penalty(presence_penalty);
        chatCompletionRequest.setFrequency_penalty(frequency_penalty);
        chatCompletionRequest.setUser(user);
        chatCompletionRequest.setN(n);
        chatCompletionRequest.setExtraData(extraData);

        // Assert
        assertThat(chatCompletionRequest.getModel()).isEqualTo(model);
        assertThat(chatCompletionRequest.getSessionId()).isEqualTo(sessionId);
        assertThat(chatCompletionRequest.getMessages()).isEqualTo(messages);
        assertThat(chatCompletionRequest.getTemperature()).isEqualTo(temperature);
        assertThat(chatCompletionRequest.getTop_p()).isEqualTo(top_p);
        assertThat(chatCompletionRequest.getStream()).isEqualTo(stream);
        assertThat(chatCompletionRequest.getStop()).isEqualTo(stop);
        assertThat(chatCompletionRequest.getMax_tokens()).isEqualTo(max_tokens);
        assertThat(chatCompletionRequest.getPresence_penalty()).isEqualTo(presence_penalty);
        assertThat(chatCompletionRequest.getFrequency_penalty()).isEqualTo(frequency_penalty);
        assertThat(chatCompletionRequest.getUser()).isEqualTo(user);
        assertThat(chatCompletionRequest.getN()).isEqualTo(n);
        assertThat(chatCompletionRequest.getExtraData()).isEqualTo(extraData);
    }

}