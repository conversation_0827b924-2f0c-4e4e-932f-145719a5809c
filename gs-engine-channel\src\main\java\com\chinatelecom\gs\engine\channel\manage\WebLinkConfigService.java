package com.chinatelecom.gs.engine.channel.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinatelecom.gs.engine.channel.api.controller.open.PasswordGenerator;
import com.chinatelecom.gs.engine.channel.api.vo.WebLinkSecretVO;
import com.chinatelecom.gs.engine.channel.common.enums.ConfigKeyEnums;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelApiSecretPO;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelConfigPO;
import com.chinatelecom.gs.engine.channel.dao.po.YN;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelApiSecretRepository;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelConfigRepository;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.service.dto.AccessTokenData;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.channel.service.dto.WebLinkConfigDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.ResultCode;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.corekit.common.core.RedisKey;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class WebLinkConfigService {

    @Resource
    private ChannelConfigRepository configRepository;
    @Resource
    private ChannelInfoRepository channelInfoRepository;

    @Autowired
    private ChannelConfigService channelConfigService;

    @Value("${platform.client.api-url:}")
    private String urlPrefix;

    @Autowired
    private ChannelApiSecretRepository channelApiSecretRepository;

    private static final String urlSuffix = "?channelId=";
    private static final String webSecretSuffix = "_web_";

    @Autowired
    private ChannelSecretManagerService channelSecretManagerService;

    @Autowired
    private RedisKey redisKey;

    @Autowired
    private RedissonClient redissonClient;

    private static final Duration ACCESS_TOKEN_EXPIRATION = Duration.ofMinutes(30);

    /**
     * 添加网页链接渠道配置
     *
     * @param channelId        String
     * @param agentCode        String
     * @param webLinkConfigDTO WebLinkConfigDTO
     * @return Boolean
     */
//    @AgentCodeCheck
    public Boolean addConfig(String channelId, String agentCode, WebLinkConfigDTO webLinkConfigDTO) {
//        checkParam(agentCode, webLinkConfigDTO);

        ChannelInfoDTO channelInfo = channelInfoRepository.getChannelInfo(channelId, agentCode);
        if (channelInfo == null) {
            throw new BizException("A0049", "渠道信息不正确，请校验参数");
        }
        ChannelConfigPO channelConfigPO = getChannelConfigPO(channelId);
        if (channelConfigPO != null) {
            // 修改
            channelConfigPO.setConfigValue(JsonUtils.toJsonString(webLinkConfigDTO));
            return configRepository.updateById(channelConfigPO);
        }
        // 创建
        ChannelConfigPO configPO = new ChannelConfigPO();
        configPO.setChannelId(channelId);
        configPO.setConfigKey(ConfigKeyEnums.WEB_LINK.getCode());
        configPO.setConfigValue(JsonUtils.toJsonString(webLinkConfigDTO));
        return configRepository.save(configPO);
    }


    /**
     * 查询网页链接配置
     *
     * @param channelId String
     * @param agentCode String
     * @return WebLinkConfigDTO
     */
//    @AgentCodeCheck
    public WebLinkConfigDTO getWebLinkConfig(String channelId, String agentCode) {
//        userAuthorizationCheckService.valid(agentCode, false);
        ChannelInfoDTO channelInfo = channelInfoRepository.getChannelInfo(channelId, agentCode);
        if (channelInfo == null) {
            throw new BizException("A0049", "渠道信息不正确，请校验参数");
        }
        ChannelConfigPO configPO = getChannelConfigPO(channelId);
        if (configPO == null) {
            return null;
        }
        //处理历史数据
        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getChannelId, channelId);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        ChannelApiSecretPO po = channelApiSecretRepository.getOne(condition);
        po.setSourceSystem(RequestContext.getAppSourceType());
        channelApiSecretRepository.update(po, condition);

        return JsonUtils.parseObject(configPO.getConfigValue(), WebLinkConfigDTO.class);
    }

    /**
     * 重置网页链接密码
     *
     * @param channelId
     * @param agentCode
     * @return
     */
    public WebLinkConfigDTO regenerateWebLinkSecret(String channelId, String agentCode) {
        WebLinkConfigDTO webLinkConfig = getWebLinkConfig(channelId, agentCode);
        if (webLinkConfig == null) {
            throw new BizException("A0049", "渠道信息不正确，请校验参数");
        }
        webLinkConfig.setSecret(webLinkSecretGenerate());
        webLinkConfig.setSecretOn(true);
        String webLinkChannelSecretErrorTimesKey = redisKey.getWebLinkChannelSecretErrorTimesKey(channelId);
        RAtomicLong atomicLong = redissonClient.getAtomicLong(webLinkChannelSecretErrorTimesKey);
        //如果redis存在key为webLinkChannelSecretErrorTimesKey的原子计数器，则删除
        if (atomicLong.isExists() && atomicLong.get() > 0) {
            atomicLong.set(0);
        }
        addConfig(channelId, agentCode, webLinkConfig);
        return webLinkConfig;
    }

    /**
     * 设置网页链接密码开关
     *
     * @param channelId
     * @param agentCode
     * @param secretOn
     * @return
     */
    public WebLinkConfigDTO setWebLinkSecretOn(String channelId, String agentCode, boolean secretOn) {
        WebLinkConfigDTO webLinkConfig = getWebLinkConfig(channelId, agentCode);
        if (webLinkConfig == null) {
            throw new BizException("A0049", "渠道信息不正确，请校验参数");
        }
        webLinkConfig.setSecretOn(secretOn);
        addConfig(channelId, agentCode, webLinkConfig);
        return webLinkConfig;
    }

    public void generateWebLinkConfig(String channelName, String channelId, String agentCode) {
        ChannelApiSecretDTO secretWithChannelId = channelSecretManagerService.getSecretWithChannelId(channelId);
        if (secretWithChannelId != null) {
            channelSecretManagerService.removeSecretWithChannelId(channelId);
        }
        WebLinkConfigDTO webLinkConfigDTO = new WebLinkConfigDTO();
        webLinkConfigDTO.setUrl(urlPrefix + urlSuffix + channelId);
        //生成密码
        webLinkConfigDTO.setSecret(webLinkSecretGenerate());
        String random = UUID.randomUUID().toString().replaceAll("-", "");
        String secretName = channelName + webSecretSuffix + random;
        channelSecretManagerService.create(agentCode, channelId, secretName, ApiSecretType.WEB_LINK);
        addConfig(channelId, agentCode, webLinkConfigDTO);
    }

    /**
     * 生成密码
     *
     * @return
     */
    public String webLinkSecretGenerate() {
        return PasswordGenerator.generateStrongPassword(12);
    }

    /**
     * 获取网页链接配置
     *
     * @param channelId String
     * @return ChannelConfigPO
     */
    private ChannelConfigPO getChannelConfigPO(String channelId) {
        LambdaQueryWrapper<ChannelConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelConfigPO::getChannelId, channelId)
                .eq(ChannelConfigPO::getConfigKey, ConfigKeyEnums.WEB_LINK.getCode());
        return configRepository.getOne(queryWrapper);
    }

    public ChannelApiSecretDTO getApiWithLink(String channelId, WebLinkSecretVO webLinkSecretVO) {
        ChannelInfoDTO channelInfo = channelInfoRepository.getChannelInfo(channelId, null);
        String channelConfigValue = channelConfigService.getChannelConfigValue(channelId, ConfigKeyEnums.WEB_LINK.getCode());
        WebLinkConfigDTO webLinkConfigDTO = JsonUtils.parseObject(channelConfigValue, WebLinkConfigDTO.class);
        if (webLinkConfigDTO == null) {
            throw new BizException("A0053", "渠道id不正确");
        }
        // 检查密码开关是否开启
        if (!webLinkConfigDTO.getSecretOn()) {
            log.warn("Web link secret is disabled for channelId: {}", channelId);
            throw new BizException("A0056", "密码已失效，请获取新密码。");
        }
        // 校验密码
        if (!webLinkConfigDTO.getSecret().equals(webLinkSecretVO.getSecret())) {
            log.warn("Incorrect web link secret provided for channelId: {}", channelId);
            String webLinkChannelSecretErrorTimesKey = redisKey.getWebLinkChannelSecretErrorTimesKey(channelId);
            RAtomicLong atomicLong = redissonClient.getAtomicLong(webLinkChannelSecretErrorTimesKey);
            long errorTimes = atomicLong.incrementAndGet();
            // 错误次数达到阈值，禁用密码
            if (errorTimes >= 5) {
                log.warn("Web link secret disabled for channelId: {} due to too many incorrect attempts.", channelId);
                this.setWebLinkSecretOn(channelId, null, false);
                atomicLong.delete(); // 删除计数器
                throw new BizException("A0056", "密码已失效，请获取新密码。");
            }
            // 设置计数器过期时间（例如1小时）
            if (errorTimes == 1) { // 第一次错误时设置过期时间
                atomicLong.expire(Duration.ofHours(1));
            }
            throw new BizException("A0057", "密码错误，请重新输入，剩余重试" + (5 - errorTimes) + "次");
        }

        // 密码正确，重置错误计数器
        String webLinkChannelSecretErrorTimesKey = redisKey.getWebLinkChannelSecretErrorTimesKey(channelId);
        RAtomicLong atomicLong = redissonClient.getAtomicLong(webLinkChannelSecretErrorTimesKey);
        if (atomicLong.isExists()) {
            atomicLong.delete();
        }

        // 检查渠道信息和启用状态
        if (channelInfo == null || !channelInfo.isEnable()) {
            log.warn("Web link access attempt for disabled or non-existent channelId: {} or agent: {}", channelId, channelInfo != null ? channelInfo.getAgentCode() : "N/A");
            throw new BizException("A0058", "该agent网页链接分享未启用或渠道不存在");
        }

        // 获取API密钥信息
        ChannelApiSecretDTO apiSecret = channelSecretManagerService.getSecretWithChannelId(channelId);
        if (apiSecret == null) {
            log.error("Failed to retrieve API secret for channelId: {}", channelId);
            throw new BizException("A0059", "无法获取API密钥信息"); // 或者更具体的错误码
        }
        return apiSecret;
    }

    // --- 新增 AccessToken 相关方法 ---

    /**
     * 生成并存储 Access Token 到 Redis，如果已存在则续命
     *
     * @param data   包含需要存储的数据的对象
     * @param userId 用户ID，作为Redis的key
     * @return 生成的 Access Token
     */
    public String generateAndStoreAccessToken(AccessTokenData data, String userId) {
        // 检查必要字段
        if (data == null) {
            log.error("Access token data cannot be null");
            throw new BizException(ResultCode.PARAM_EXCEPTION.getCode(), "Access Token 数据不能为空");
        }

        // 检查用户是否已有访问令牌
        String redisKeyStr = redisKey.getAccessTokenKeyByUserId(userId);
        RBucket<AccessTokenData> bucket = redissonClient.getBucket(redisKeyStr);
        AccessTokenData existingData = bucket.get();

        String accessToken;

        if (existingData != null) {
            // 已存在，使用原有token并续命
            accessToken = existingData.getAccessToken();
            data.setAccessToken(accessToken);
            log.info("Extending access token validity for userId: {}, token ending with: ...{}",
                    userId, accessToken.substring(accessToken.length() - 6));
        } else {
            // 生成新token
            accessToken = UUID.randomUUID().toString().replace("-", "");
            data.setAccessToken(accessToken);
            log.info("Generated new access token for userId: {}, channelId: {}",
                    userId, data.getChannelId());
        }

        // 存储到 Redis
        try {
            bucket.set(data, ACCESS_TOKEN_EXPIRATION.toMillis(), TimeUnit.MILLISECONDS);
            return accessToken;
        } catch (Exception e) {
            log.error("Failed to store access token in Redis for userId: {} and channelId: {}. Error: {}",
                    userId, data.getChannelId(), e.getMessage());
            throw new BizException(ResultCode.PARAM_EXCEPTION.getCode(), "无法存储 Access Token 到 Redis");
        }
    }

    /**
     * 验证 Access Token 并获取关联的数据。
     *
     * @param accessToken 要验证的 Access Token
     * @param userId      用户ID，用于获取Redis中的数据
     * @return 如果 Token 有效，返回关联的 AccessTokenData；否则返回 null
     */
    public AccessTokenData validateAndGetTokenData(String accessToken, String userId) {
        if (!StringUtils.hasText(accessToken) || !StringUtils.hasText(userId)) {
            log.warn("Access token or userId is empty");
            return null;
        }

        String redisKeyStr = redisKey.getAccessTokenKeyByUserId(userId);
        try {
            RBucket<AccessTokenData> bucket = redissonClient.getBucket(redisKeyStr);
            AccessTokenData data = bucket.get();

            if (data == null) {
                log.warn("No access token data found for userId: {}", userId);
                return null;
            }

            // 验证stored token与传入的token是否匹配
            if (!accessToken.equals(data.getAccessToken())) {
                log.warn("Access token mismatch for userId: {}", userId);
                return null;
            }

            // 续命操作
            bucket.expireAsync(ACCESS_TOKEN_EXPIRATION);
            log.debug("Access token validated and extended for userId: {}", userId);
            return data;
        } catch (Exception e) {
            log.error("Error validating access token from Redis for userId: {}. Error: {}",
                    userId, e.getMessage(), e);
            return null;
        }
    }
}
