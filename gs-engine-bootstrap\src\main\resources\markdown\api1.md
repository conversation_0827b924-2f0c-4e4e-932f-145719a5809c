## OpenAPI接口鉴权

## 一、 背景

对OpenAPI需要的调用需要鉴权，网关服务（apixsix）能够提供统一的鉴权模式，apisix提供了多种鉴权方式，这里使用aksk的模式进行鉴权，即hmac\_auth

## 二、 描述

hmac-auth 插件支持 HMAC（基于哈希的消息认证码）身份验证，启用后，插件会验证请求的 Authorization 标头中的 HMAC 签名，并检查传入请求是否来自可信来源。如果密钥 ID 有效且存在，APISIX 会使用请求的 Date 标头和密钥生成 HMAC 签名。如果生成的签名与 Authorization 标头中提供的签名匹配，则请求经过身份验证并转发到上游服务。

## 三、 使用

### 1、 hmac\_auth

在hmac\_auth中:

● ak等价于key\_id

● sk等价于secret\_key

### 2、 流程示意图

![示例图片](data:image/png;base64,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)


### 3、 接口调用方

#### a) 总体步骤

```
● 获取keyId以及secretKey

             ● 确定需要调用的接口

             ● 计算签名

             ● 将签名和日期放入http请求头中发起调用
```

#### b) 签名计算

公式为

signature = HMAC-SHAx-HEX(secret\_key, signing\_string)

签名字符串的格式如下，请注意换行以及空格

```
{key_id}
{request_method} {request_path}
date: {gmt_time}
```

请求头中的Date格式为

```
GMT格式，即：
E, dd MMM yyyy HH:mm:ss 'GMT'
```

请求头中的Authorization格式为

```
Signature keyId="{key_id}",algorithm="{algorithm}",'headers="@request-target date",'signature="{signature_base64}"
```

signature\_base64为计算出的sign经过base64后得到的字符串

algorithm为算法，目前统一使用: hmac-sha256

#### c) 请求示例

```
curl -X GET "http://127.0.0.1:9080/get" \
  -H "Date: Fri, 06 Sep 2024 06:41:29 GMT" \
  -H 'Authorization: Signature keyId="john-key",algorithm="hmac-sha256",headers="@request-target date",signature="wWfKQvPDr0wHQ4IHdluB4IzeNZcj0bGJs2wvoCOT5rM="'
```

## 四、 参考代码

### 1、 签名计算-JAVA

加密类代码如下：

```java
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class SignUtils {
    private static final List<String> SIGNED_HEADERS = new ArrayList<>();
    private static final String algorithm = "HmacSHA256";
    private static final String hmacAlgorithm = "hmac-sha256";

    static {
        SIGNED_HEADERS.add("x-tenantid");
        SIGNED_HEADERS.add("x-userid");
        SIGNED_HEADERS.add("x-source");
    }

    public SignUtils() {
    }

    public static String generateAuth(String ak, String sk, String method, String uri,
                                      Map<String, String> headers, Map<String, String> params, String body)
            throws NoSuchAlgorithmException, InvalidKeyException {
        String formattedUtcTime = headers.get("Date");
        StringBuilder str = new StringBuilder();
        str.append(ak).append("\n");
        str.append(method.toUpperCase(Locale.ROOT)).append(" ");
        str.append(uri).append("\n");
        str.append("date").append(": ").append(formattedUtcTime).append("\n");
        for (String signHeader : SIGNED_HEADERS) {
            str.append(signHeader).append(": ").append(headers.get(signHeader)).append("\n");
        }

        // 创建签名
        Mac hasher = Mac.getInstance(algorithm);
        hasher.init(new SecretKeySpec(sk.getBytes(), algorithm));
        byte[] hash = hasher.doFinal(str.toString().getBytes());
        String signatureBase64 = DatatypeConverter.printBase64Binary(hash);

        String headerStr = "@request-target date " + StringUtils.join(SIGNED_HEADERS, ' ');

        return String.format("Signature keyId=\"%s\",algorithm=\"%s\",headers=\"%s\",signature=\"%s\"",
                ak, hmacAlgorithm, headerStr, signatureBase64);

    }
}

```

请求示例:

```java
import com.openapi.kms.demo.utils.SignUtils;
import okhttp3.*;
import org.testng.annotations.Test;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class SearchDemo {

    private static Map<String, String> buildHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-userid", SignParam.userid);
        headers.put("x-tenantid", SignParam.tenantid);
        headers.put("x-source", SignParam.source);
        LocalDateTime utcTime = LocalDateTime.now(ZoneOffset.UTC);
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("E, dd MMM yyyy HH:mm:ss 'GMT'", Locale.ENGLISH);
        // 格式化时间并输出
        String formattedUtcTime = utcTime.format(formatter);
        headers.put("Date", formattedUtcTime);
        return headers;
    }

    @Test
    public void searchTest() throws Exception {

        String body = "\n" +
                "{\n" +
                "  \"query\": \"\",\n" +
                "  \"env\": \"TEST\",\n" +
                "  \"filter\": {\n" +
                "    \"sourceSystem\": \"KS\"\n" +
                "\n" +
                "  },\n" +
                "  \"pageNum\": 1,\n" +
                "  \"pageSize\": 10,\n" +
                "  \"range\": \"all\",\n" +
                "  \"orderBy\": \"DEFAULT\"\n" +
                "}";

        String method = "POST";
        String host = SignParam.host;
        String uri = SignParam.searchUri;
        String accessKey = SignParam.accessKey;
        String secretKey = SignParam.secretKey;

        String url = host + uri;
        Map<String, String> headers = buildHeaders();
        String sign = SignUtils.generateAuth(accessKey, secretKey, method, uri, headers, new HashMap<>(), body);

        headers.put("Authorization", sign);

        System.out.println(method);
        System.out.println(host + uri);
        System.out.println(headers);
        System.out.println(body);


        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);

        Request request = new Request.Builder()
                .url(url)
                .headers(Headers.of(headers))
                .post(requestBody)
                .build();
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(50, TimeUnit.SECONDS)
                .readTimeout(50, TimeUnit.MINUTES)
                .build();


        // 同步请求
        Response response = client.newCall(request).execute();

        if (!response.isSuccessful()) {
            throw new IOException("意外的状态码: " + response.code() + " - " + response.message());
        }

        System.out.println(response.body().string());

        //        异步调用
//        client.newCall(request).enqueue(new Callback() {
//            @Override
//            public void onFailure(Call call, IOException e) {
//                e.printStackTrace();
//            }
//
//            @Override
//            public void onResponse(Call call, Response response) throws IOException {
//                if (!response.isSuccessful()) {
//                    throw new IOException("Unexpected code " + response);
//                }
//                System.out.println(response.body().string());
//            }
//        });
    }

    private class SignParam {
        public static final String host = "http://127.0.0.1:8092";
        public static final String searchUri = "/ais/kms/openapi/search/v1";

        public static final String secretKey = " ";
        public static final String accessKey = " ";
        public static final String userid = " ";
        public static final String tenantid = " ";
        public static final String source = " ";

    }
}


```





### 2、 签名计算-Python

```
import hmac
import hashlib
import base64
from datetime import datetime, timezone

key_id = "john-key"                # 密钥 ID
secret_key = b"john-secret-key"    # 秘密密钥
request_method = "GET"             # HTTP 方法
request_path = "/hmac"              # 路由 URI
algorithm= "hmac-sha256"           # 可以在 allowed_algorithms 中使用其他算法

# 获取当前的 GMT 日期时间
# 注意：时钟偏差后签名将失效（默认 300s）
# 签名失效后可以重新生成，或者增加时钟
# 倾斜以延长建议的安全边界内的有效性
gmt_time = datetime.now(timezone.utc).strftime('%a, %d %b %Y %H:%M:%S GMT')

# 构造签名字符串（有序）
# 日期和任何后续的自定义标头应小写并用
# 单空格字符，即 `<key>:<space><value>`
# https://datatracker.ietf.org/doc/html/draft-cavage-http-signatures-12#section-2.1.6
signing_string = (
  f"{key_id}\n"
  f"{request_method} {request_path}\n"
  f"date: {gmt_time}\n"
)

# 创建签名
signature = hmac.new(secret_key, signing_string.encode('utf-8'), hashlib.sha256).digest()
signature_base64 = base64.b64encode(signature).decode('utf-8')

# 构造请求头
headers = {
  "Date": gmt_time,
  "Authorization": (
    f'Signature keyId="{key_id}",algorithm="{algorithm}",'
    f'headers="@request-target date",'
    f'signature="{signature_base64}"'
  )
}

# 打印请求头
print(headers)
```

