package com.chinatelecom.gs.engine.channel.dao.repository;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.baomidou.mybatisplus.extension.service.IService; import com.chinatelecom.gs.engine.channel.dao.po.ChannelMediaPO; import org.junit.jupiter.api.BeforeEach; import org.junit.jupiter.api.Test; import org.mockito.Mock; import org.mockito.MockitoAnnotations;
public class ChannelMediaRepositoryTest {
    @Mock
    private ChannelMediaRepository channelMediaRepository;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testSelectById() {
        // Arrange
        Long id = 1L;
        ChannelMediaPO expectedPo = new ChannelMediaPO();
        when(channelMediaRepository.getById(id)).thenReturn(expectedPo);

        // Act
        ChannelMediaPO result = channelMediaRepository.getById(id);

        // Assert
        assertSame(expectedPo, result);
        verify(channelMediaRepository).getById(id);
    }

    @Test
    public void testInsert() {
        // Arrange
        ChannelMediaPO po = new ChannelMediaPO();
        boolean expectedResult = true;
        when(channelMediaRepository.save(po)).thenReturn(expectedResult);

        // Act
        boolean result = channelMediaRepository.save(po);

        // Assert
        assertTrue(result);
        verify(channelMediaRepository).save(po);
    }

    @Test
    public void testUpdateById() {
        // Arrange
        ChannelMediaPO po = new ChannelMediaPO();
        boolean expectedResult = true;
        when(channelMediaRepository.updateById(po)).thenReturn(expectedResult);

        // Act
        boolean result = channelMediaRepository.updateById(po);

        // Assert
        assertTrue(result);
        verify(channelMediaRepository).updateById(po);
    }

    @Test
    public void testDeleteById() {
        // Arrange
        Long id = 1L;
        boolean expectedResult = true;
        when(channelMediaRepository.removeById(id)).thenReturn(expectedResult);

        // Act
        boolean result = channelMediaRepository.removeById(id);

        // Assert
        assertTrue(result);
        verify(channelMediaRepository).removeById(id);
    }
}