package com.chinatelecom.gs.engine.common.mq;

import lombok.Getter;

import jakarta.validation.constraints.NotNull;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

public class DelayTask implements Delayed, Runnable {

    @Getter
    private long executeMillis;
    private long delaySecond;
    private final Runnable runnable;

    public DelayTask(long delaySecond, Runnable runnable) {
        this.delaySecond = delaySecond;
        this.executeMillis = System.currentTimeMillis() + (this.delaySecond * 1000);
        this.runnable = runnable;
    }

    @Override
    public void run() {
        runnable.run();
    }

    @Override
    public long getDelay(@NotNull TimeUnit unit) {
        long diff = executeMillis - System.currentTimeMillis();
        return unit.convert(diff, TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(@NotNull Delayed o) {
        return Long.compare(this.getExecuteMillis(), ((DelayTask) o).getExecuteMillis());
    }
}