package com.chinatelecom.gs.engine.core.manager.vo.config.base;


import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelelcom.gs.engine.sdk.common.PageParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BaseConfigQueryParam extends PageParam {
    /**
     * 业务配置名称
     */
    private String name;

    /**
     * 配置维度
     */
    private DimensionEnum dimension;
    /**
     * 业务配置场景
     */
    @NotBlank(message = "配置类型不能为空")
    private String configType;

    /**
     * 业务配置场景说明
     */
    private String description;

    /**
     * 业务场景配置唯一标识
     */
    @NotBlank(message = "业务标识不能为空")
    private String businessNo;

    /**
     * 配置json格式value值
     */
    private String configData;


}
