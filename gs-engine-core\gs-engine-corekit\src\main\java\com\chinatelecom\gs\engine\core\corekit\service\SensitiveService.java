package com.chinatelecom.gs.engine.core.corekit.service;

import com.chinatelecom.gs.engine.common.infra.base.BaseExtendService;
import com.chinatelecom.gs.engine.core.corekit.domain.po.SensitivePO;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitivePageRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.response.SensitiveResponse;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface SensitiveService extends BaseExtendService<SensitivePO> {

    /**
     * 保存敏感词
     * @param request 敏感词请求参数
     * @return
     */
    Boolean saveSensitive(SensitiveRequest request);

    /**
     * 删除敏感词
     * @param ids 敏感词ID
     * @return
     */
    Boolean delete(List<String> ids);

    /**
     * 敏感词分页查询
     * @param request 分页参数
     * @return
     */
    Page<SensitiveResponse> pageQuery(SensitivePageRequest request);

    /**
     * 敏感词详情
     * @param id
     * @return
     */
    SensitiveResponse detail(Long id);

    /**
     * 敏感词匹配
     * @param content
     * @return
     */
    List<String> matchSensitiveValue(String content);

    /**
     * 敏感词匹配（不受开关控制）
     * @param content
     * @return
     */
    List<String> matchSensitiveValueWithoutSwitch(String content);

    /**
     * 敏感词导入模版下载
     * @param response
     */
    void downloadTemplate(HttpServletResponse response) throws IOException;

    /**
     * 敏感词导入
     * @param file 文件流
     * @return
     */
    Boolean upload(MultipartFile file) throws Exception;

    /**
     * 敏感词同步
     */
    void syncSensitiveData();

    /**
     * 初始化敏感词
     */
    void initSensitiveData();

    /**
     * 开启/关闭敏感词
     * @param enable
     * @return
     */
    Boolean enableSensitive(Boolean enable);
}
