package com.chinatelecom.gs.engine.core.corekit.controller.rpc;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.core.corekit.service.GlobalVariableAppService;
import com.chinatelecom.gs.engine.core.sdk.request.GlobalVariableQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.rpc.GlobalVariableRpcApi;
import com.chinatelecom.gs.engine.core.sdk.vo.GlobalVariableDataDetailVO;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

@Slf4j
@RestController
@RequestMapping(Apis.BASE_PREFIX + Apis.RPC_PREFIX + "/variable")
public class GlobalVariableRpcController implements GlobalVariableRpcApi {

    @Resource
    private GlobalVariableAppService globalVariableAppService;

    @Override
    @GetMapping("/getDetail")
    @AuditLog(businessType = "查询变量详情", operType = "查询变量详情", operDesc = "查询变量详情", objId="#variableName")
    public Result<GlobalVariableDataDetailVO> getDetail(@RequestParam("variableName") String variableName) {
        return Result.success(globalVariableAppService.getDetailByName(variableName));
    }

    @Override
    @PostMapping("/page")
    @AuditLog(businessType = "分页查询变量", operType = "分页查询变量", operDesc = "分页查询变量", objId="null")
    public PageImpl<GlobalVariableDataDetailVO> page(@RequestBody @Valid GlobalVariableQueryRequest pageRequest) {
        return (PageImpl<GlobalVariableDataDetailVO>) globalVariableAppService.page(pageRequest);
    }
}
