package com.chinatelecom.gs.engine.core.corekit.service;

import com.chinatelecom.gs.engine.common.infra.base.BaseExtendService;
import com.chinatelecom.gs.engine.core.corekit.domain.po.FormPO;
import com.chinatelecom.gs.engine.core.sdk.request.FormDelRequest;
import com.chinatelecom.gs.engine.core.sdk.request.FormQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.request.FormSaveRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.FormVO;

import java.util.List;

/**
 * @USER: pengmc1
 * @DATE: 2025/7/21 11:12
 */
public interface FormService extends BaseExtendService<FormPO> {
    /**
     * 保存表单
     * @param request
     * @return
     */
    Boolean saveForm(FormSaveRequest request);

    /**
     * 发布表单
     * @param request
     * @return
     */
    Boolean publishForm(FormSaveRequest request);

    /**
     * 删除表单
     * @param request
     * @return
     */
    Boolean deleteForm(FormDelRequest request);

    /**
     * 获取表单详情
     * @param formCode
     * @param source
     * @return
     */
    FormVO getDetail(String formCode, String source);

    /**
     * 获取最新发布表单详情
     * @param formCode
     * @param source
     * @return
     */
    FormVO getLastPublishDetail(String formCode, String source);

    /**
     * 根据版本获取表单详情
     * @param formCode
     * @param source
     * @param version
     * @return
     */
    FormVO getDetailByVersion(String formCode, String source, Long version);

    /**
     * 获取表单列表
     * @param request
     * @return
     */
    List<FormVO> queryFormList(FormQueryRequest request);
}
