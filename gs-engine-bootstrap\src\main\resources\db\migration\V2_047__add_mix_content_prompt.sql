INSERT INTO agent_default_config(config_key, config_value, type, category, yn) VALUES ('botStrategy.llm.mixContentPromptNotChat', '{ \"value\":\"请根据以下的上下文：\r\n#{context}，回答以下用户的问题：#{question}\r\n\r\n回答输出的时候请注意以下几点，\r\n一.每个来源可能回答了用户的部分问题，若涉及表格，请参考summary回答，汇总生成最终答案；\r\n二.你的回答必须用中文书写，注意尽量用原文中的内容回答问题\r\n三.如果不能利用上下文回答问题，请直接输出\\\"#{noAnswerScript}\\\"，而不要再生成其他任何话\" }', 1, 1, 0);


INSERT INTO agent_default_config(config_key, config_value, type, category, yn) VALUES ('botStrategy.llm.mixContentPrompt', '{ \"value\":\"请根据以下的上下文：\r\n#{context}，回答以下用户的问题：#{question}\r\n\r\n回答输出的时候请注意以下两点，\r\n一.每个来源可能回答了用户的部分问题，若涉及表格，请参考summary回答，汇总生成最终答案；\r\n二.你的回答必须用中文书写，注意尽量用原文中的内容回答问题\r\n三.如果不能利用上下文回答问题，请直接根据模型自身知识回答。否则，输出根据提供的引用内容来回答问题\r\n\" }', 1, 1, 0);
