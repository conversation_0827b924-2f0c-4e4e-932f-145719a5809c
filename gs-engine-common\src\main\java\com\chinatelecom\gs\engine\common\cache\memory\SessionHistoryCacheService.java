package com.chinatelecom.gs.engine.common.cache.memory;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.cache.RedisService;
import com.chinatelecom.gs.engine.common.utils.CacheKeyUtils;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.Function;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.ToolCall;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SessionHistoryCacheService {

    @Resource
    private RedisService redisService;

    public void addSessionMessageRequest(String sessionId, MessageRequestCache messageRequestCache) {
        String cacheKey = CacheKeyUtils.sessionMessageCacheKey(sessionId);
        log.info("【{}】保存请求到上下文：{}", sessionId, JSON.toJSONString(messageRequestCache));
        this.redisService.lRightPush(cacheKey, JsonUtils.toJsonString(messageRequestCache));
        this.redisService.expire(cacheKey, 30, TimeUnit.MINUTES);
    }

    public void addSessionMessageResponse(String sessionId,  String upMessageId, MessageResponseCache messageResponseCache) {
        String cacheKey = CacheKeyUtils.sessionMessageResCacheKey(sessionId, upMessageId);
        log.info("【{}】保存响应到上下文：{}", sessionId, JSON.toJSONString(messageResponseCache));
        this.redisService.lRightPush(cacheKey, JsonUtils.toJsonString(messageResponseCache));
        this.redisService.expire(cacheKey, 30, TimeUnit.MINUTES);
    }

    public List<WrapLLMMessage> getChatHistoryFromCache(String sessionId, String currentMessageId, Integer limit, boolean ignoreCurrent, boolean containTool) {
        String sessionMsgKey = CacheKeyUtils.sessionMessageCacheKey(sessionId);
        Long len = this.redisService.lLen(sessionMsgKey);
        if (len == null || len == 0) {
            return new ArrayList<>();
        }

        long start = 0;
        long end = len;
        if (Objects.nonNull(limit)) {
            start = Math.max(len - limit, 0);

        }

        List<String> messageRequestCaches = this.redisService.lRange(sessionMsgKey, start, end);
        if (CollectionUtils.isEmpty(messageRequestCaches)) {
            return new ArrayList<>();
        }

        List<WrapLLMMessage> llmMessages = new ArrayList<>();
        for (String messageRequestCache : messageRequestCaches) {
            MessageRequestCache requestCache = JsonUtils.parseObject(messageRequestCache, MessageRequestCache.class);
            if (Objects.isNull(requestCache) || Objects.isNull(requestCache.getMessageId()) || (requestCache.getMessageId().equalsIgnoreCase(currentMessageId) && ignoreCurrent)) {
                continue;
            }


            String resCacheKey = CacheKeyUtils.sessionMessageResCacheKey(sessionId, requestCache.getMessageId());
            Long resLen = this.redisService.lLen(resCacheKey);
            List<String> messageResponseCaches = this.redisService.lRange(resCacheKey, 0, resLen);
            if (CollectionUtils.isEmpty(messageResponseCaches)) {
                continue;
            }

            WrapLLMMessage requestMessage = BeanUtil.toBean(requestCache, WrapLLMMessage.class);
            if (containTool) {
                requestMessage.setTool_call_id(requestCache.getToolName());
            }
            llmMessages.add(requestMessage);

            for (String messageResponseCache : messageResponseCaches) {
                MessageResponseCache responseCache = JsonUtils.parseObject(messageResponseCache, MessageResponseCache.class);
                if (Objects.isNull(responseCache)) {
                    continue;
                }
                if (!CollectionUtils.isEmpty(responseCache.getToolCalls())) {
                    List<ToolCall> newToolCalls = responseCache.getToolCalls().stream().map(toolCall -> {
                        Function function = toolCall.getFunction();
                        if (Objects.isNull(function.getArguments())) {
                            //这里需要给大模型补null，不然会报错
                            function.setArguments("null");
                        }
                        toolCall.setFunction(function);
                        return toolCall;
                    }).collect(Collectors.toList());
                    requestCache.setToolCalls(newToolCalls);
                }
                WrapLLMMessage responseMessage = BeanUtil.toBean(responseCache, WrapLLMMessage.class);
                llmMessages.add(responseMessage);
            }
        }

        return llmMessages;
    }

}
