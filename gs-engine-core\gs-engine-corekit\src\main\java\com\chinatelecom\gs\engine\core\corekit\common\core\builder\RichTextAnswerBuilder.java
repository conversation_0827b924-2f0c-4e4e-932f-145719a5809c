package com.chinatelecom.gs.engine.core.corekit.common.core.builder;

import com.chinatelecom.gs.engine.core.corekit.common.core.tts.TextSpeechService;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.ContentTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.Answer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.entry.RichTextCard;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.entry.RichTextOptionCard;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.option.OptionCallback;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.option.OptionCard;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.option.OptionConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.option.OptionEntry;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.reason.Reason;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.request.AnswerBuildRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.speech.Speech;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class RichTextAnswerBuilder implements AnswerBuilder {

    @Resource
    private TextSpeechService textSpeechService;


    @Override
    public boolean valid(AnswerBuildRequest answerBuildRequest) {
        return answerBuildRequest.getAnswerTypeEnum().equals(AnswerTypeEnum.SIMPLE_RICH_TEXT)
                || answerBuildRequest.getAnswerTypeEnum().equals(AnswerTypeEnum.RICH_TEXT);
    }


    @Override
    public Answer build(AnswerBuildRequest answerBuildRequest) {
        Answer answer = new Answer();
        if (Objects.nonNull(answerBuildRequest.getContent())) {
            String content = answerBuildRequest.getContent().toString();
            if (!CollectionUtils.isEmpty(answerBuildRequest.getOptions())) {
                RichTextOptionCard richTextFoldCard = getRichTextFoldCard(answerBuildRequest);
                answer.setContent(richTextFoldCard);
                answer.setAnswerType(AnswerTypeEnum.RICH_TEXT_OPTION);
                answer.setContentType(ContentTypeEnum.ALL.getCode());
                answer.setNamespace("com.richText.option");
                answer.setVersion(answer.getAnswerType().getVersion());
            } else {
                answer.setAnswerType(AnswerTypeEnum.SIMPLE_RICH_TEXT);
                answer.setContentType(ContentTypeEnum.PART.getCode());
                answer.setContent(content);
                answer.setNamespace("com.richText");
                answer.setVersion(answer.getAnswerType().getVersion());
            }
            Speech speech = this.textSpeechService.genSpeech(content);
            if(Objects.nonNull(speech)){
                speech.setEnableSmartInterruption(answerBuildRequest.getEnableSmartInterruption());
            }
            answer.setSpeech(speech);
        }

        if (!StringUtils.isEmpty(answerBuildRequest.getReasoningContent())) {
            Reason reason = new Reason();
            reason.setReasoningContent(answerBuildRequest.getReasoningContent());
            answer.setReasoning(reason);
        }
        answer.setInstructions(answerBuildRequest.getInstructions());
        return answer;
    }


    @NotNull
    private RichTextOptionCard getRichTextFoldCard(AnswerBuildRequest answerBuildRequest) {
        RichTextCard richTextCard = new RichTextCard();
        richTextCard.setHtml(answerBuildRequest.getContent().toString());

        RichTextOptionCard richTextFoldCard = new RichTextOptionCard();
        richTextFoldCard.setShowCard(richTextCard);

        OptionCard<RichTextCard> option = new OptionCard<>();

        OptionConfig optionConfig = new OptionConfig();

        option.setConfig(optionConfig);

        List<OptionEntry<RichTextCard>> optionEntries = getOptionEntries(answerBuildRequest);

        option.setEntries(optionEntries);

        richTextFoldCard.setOption(option);

        return richTextFoldCard;
    }

    @NotNull
    private List<OptionEntry<RichTextCard>> getOptionEntries(AnswerBuildRequest answerBuildRequest) {
        List<OptionEntry<RichTextCard>> optionEntries = new ArrayList<>();

        int index = 1;
        for (String optionText : answerBuildRequest.getOptions()) {
            RichTextCard optionShowCard = new RichTextCard();
            optionShowCard.setHtml(optionText);

            OptionEntry<RichTextCard> optionEntry = new OptionEntry<>();
            optionEntry.setShowCard(optionShowCard);
            optionEntry.setIndex(index);

            OptionCallback optionCallback = new OptionCallback();
            optionCallback.setDetailContent(optionText);
            optionEntry.setCallback(optionCallback);

            optionEntries.add(optionEntry);
            index++;
        }
        return optionEntries;
    }

}
