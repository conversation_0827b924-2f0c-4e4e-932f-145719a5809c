package com.chinatelecom.gs.engine.core.entity.ability.validator.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.util.StringUtils;
import com.chinatelecom.gs.engine.common.utils.VariableParseUtils;
import com.chinatelecom.gs.engine.core.entity.domain.request.EntityValidatorRequest;
import com.chinatelecom.gs.engine.core.sdk.enums.EntityValidatorEnum;
import com.chinatelecom.gs.engine.core.entity.ability.validator.AbstractEntityValidatorAbilityService;
import com.chinatelecom.gs.engine.core.model.toolkit.StreamingChatLanguageModel;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.LLMRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.ModelProviderEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptTemplateVO;
import com.chinatelecom.gs.engine.kms.service.PromptTemplateAppService;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 基于大模型的实体校验
 *
 * @USER: pengmc1
 * @DATE: 2025/1/21 8:47
 */

@Slf4j
@Service
public class LlmEntityValidatorAbilityService extends AbstractEntityValidatorAbilityService {

    @Resource
    private ModelServiceClient remoteServiceClient;

    @Resource
    private StreamingChatLanguageModel streamingChatLanguageModel;

    @Resource
    protected PromptTemplateAppService promptTemplateAppService;

    //大模型实体校验prompt模板
    private static final String LLM_ENTITY_VALIDATOR_PROMPT_TEMPLATE = "LLM_ENTITY_VALIDATOR_PROMPT_TEMPLATE";

    /**
     * 校验编码
     *
     * @return
     */
    @Override
    public String validatorCode() {
        return EntityValidatorEnum.LLM_VALIDATOR.getCode();
    }

    /**
     * 执行实体校验
     * @param request
     * @return
     */
    @Override
    protected Boolean doValidator(EntityValidatorRequest request) {
        ModelPageListParam modelParam = remoteServiceClient.queryByModelCode(request.getEntityVO().getValidatorModelCode());
        if (modelParam == null) {
            log.error("【实体校验】【{}】未找到模型：{}", validatorCode(), request.getEntityVO().getValidatorModelCode());
            return true;
        }
        try {
            LLMRequest llmRequest = buildLLMRequest(modelParam, request);
            Response<String> llmResponse = streamingChatLanguageModel.syncGenerate(llmRequest);
            return parseResult(llmResponse.content());
        } catch (Exception e) {
            log.error("【实体校验】【{}】大模型请求异常", validatorCode(), e);
        }
        //异常情况下，默认校验通过
        return true;
    }

    /**
     * 构造大模型请求
     * @param modelConfig
     * @param request
     * @return
     */
    private LLMRequest buildLLMRequest(ModelPageListParam modelConfig, EntityValidatorRequest request) {
        LLMRequest llmRequest = new LLMRequest();
        llmRequest.getLlmModelInfo().setProvider(ModelProviderEnum.from(modelConfig.getModelProvider()));
        llmRequest.getLlmModelInfo().setModelApi(modelConfig.getApiKey());
        llmRequest.getLlmModelInfo().setModelSecret(modelConfig.getModelSecret());
        llmRequest.getLlmModelInfo().setModelName(modelConfig.getModelCallName());
        llmRequest.getLlmModelInfo().setModelUrl(modelConfig.getExternalModelUrl());
        llmRequest.setStreaming(0);
        llmRequest.setEnableThinking(false);
        llmRequest.setText(buildPrompt(request));
        llmRequest.setUserId(IdUtil.fastSimpleUUID());
        if(Objects.nonNull(modelConfig.getExtraDataVO())){
            llmRequest.getLlmModelInfo().setMaxToken(modelConfig.getExtraDataVO().getMaxInputToken());
            llmRequest.getLlmModelInfo().setTransformerType(modelConfig.getExtraDataVO().getTransformerType());
            llmRequest.getLlmModelInfo().setNativeCall(modelConfig.getExtraDataVO().getNativeCall());
            llmRequest.getLlmModelInfo().setNativeCallUrl(modelConfig.getExtraDataVO().getNativeCallUrl());
        }
        return llmRequest;
    }

    /**
     * 构造prompt
     * 实体识别prompt设置支持部分变量
     * {{entityValue}} 实体值
     * {{validatorPrompt}} 实体校验提示词
     * @param request
     * @return
     */
    private String buildPrompt(EntityValidatorRequest request){
        PromptTemplateVO promptTemplate = promptTemplateAppService.get(LLM_ENTITY_VALIDATOR_PROMPT_TEMPLATE);
        if (Objects.isNull(promptTemplate)) {
            throw new BizException("BA010", "未找到大模型实体校验大模型prompt配置");
        }
        Map<String, Object> variableMap = Maps.newHashMap();
        variableMap.put("entityValue", request.getEntity().getEntityContents().get(0).getValue());
        variableMap.put("validatorPrompt", buildEntityValidatorPrompt(request));
        return VariableParseUtils.parseVariable(promptTemplate.getContent(), variableMap);
    }

    /**
     * 构建实体校验提示词,支撑如下变量
     * {{entityCode}} 实体编码
     * {{entityName}} 实体名称
     * {{entityDesc}} 实体描述
     * {{query}} 用户query 如果开启上下文，会自动带上
     * @param request
     * @return
     */
    private String buildEntityValidatorPrompt(EntityValidatorRequest request){
        String validatorPromptTemplate = request.getEntityVO().getValidatorPrompt();
        Map<String, Object> variableMap = Maps.newHashMap();
        variableMap.put("entityCode", request.getEntityVO().getEntityCode());
        variableMap.put("entityName", request.getEntityVO().getEntityName());
        variableMap.put("entityDesc", request.getEntityVO().getEntityDesc());
        //todo 调用历史会话接口，获取历史消息
        variableMap.put("query", request.getQuery());
        return VariableParseUtils.parseVariable(validatorPromptTemplate, variableMap);
    }

    /**
     * 解析大模型JSON答案
     * @param content 大模型返回的json格式
     * @return
     */
    private Boolean parseResult(String content){
        if(StringUtils.isNotBlank(content) && "是".equals(content.trim())){
            return true;
        }
        return false;
    }
}
