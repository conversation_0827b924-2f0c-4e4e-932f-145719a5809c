package com.chinatelecom.gs.engine.core.corekit.common.core;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2023/6/7 19:07
 **/
@Component
public class RedisKey {

    private String redisKeyPrefix;

    private String chatSessionMessagePrefix;

    private String labelDataClusterQueryPrefix;

    private String addKnowledgePrefix;

    private String offSiteSwitchPrefix;

    private String webLinkChannelSecretErrorTimesPrefix;

    private String accessTokenForUserIdPrefix;

    @Value("${spring.redis.key-prefix:keyPrefix}")
    public void setRedisKeyPrefix(String redisKeyPrefix) {
        this.redisKeyPrefix = redisKeyPrefix;
    }

    @PostConstruct
    public void init() {
        chatSessionMessagePrefix = redisKeyPrefix + ":chat:%s";
        labelDataClusterQueryPrefix = redisKeyPrefix + ":label-data-cluster-query:%s";
        addKnowledgePrefix = redisKeyPrefix + ":add-knowledge:%s:%s:%s";
        offSiteSwitchPrefix = redisKeyPrefix + ":off-site-switch:%s:%s";
        webLinkChannelSecretErrorTimesPrefix = redisKeyPrefix + ":web-link-channel-error-times:%s";
        accessTokenForUserIdPrefix = redisKeyPrefix + ":access-token-for-user:%s";
    }

    public String getChatMessageKey(String upId) {
        return chatSessionMessagePrefix.formatted(upId);
    }

    public String getLabelDataClusterQueryPrefix(Long taskId) {
        return labelDataClusterQueryPrefix.formatted(taskId);
    }

    public String getAddKnowledgeKey(String kmsCode, String fatherDirCode, String title) {
        return addKnowledgePrefix.formatted(kmsCode, fatherDirCode, title);
    }

    public String getOffSiteSwitchKey(String agentCode, Long editVersion) {
        return offSiteSwitchPrefix.formatted(agentCode, editVersion);
    }

    public String getWebLinkChannelSecretErrorTimesKey(String channelId) {
        return this.webLinkChannelSecretErrorTimesPrefix.formatted(channelId);
    }

    public String getAccessTokenKeyByUserId(String userId) {
        return this.accessTokenForUserIdPrefix.formatted(userId);
    }


}
