package com.chinatelecom.gs.engine.common.log.track;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson2.JSON;
import com.chinatelecom.gs.engine.common.context.LocalContext;
import com.chinatelecom.gs.engine.common.context.LocalInfo;
import com.chinatelecom.gs.engine.common.enums.EntryEnum;
import com.chinatelecom.gs.engine.common.mq.impl.KafkaMQService;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.enums.EnvTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.agent.client.AgentInfoRpcApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.response.AgentInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ExecutorService;

/**
 * @USER: pengmc1
 * @DATE: 2025/5/7 8:45
 */

@Service
@Slf4j
@RefreshScope
public class LogMqProducer {

    @Resource
    private KafkaMQService kafkaMQService;

    /**
     * 是否发送对话结果消息（mq）
     */
    @Value("${send.dialogResultMsg:false}")
    private Boolean sendDialogResultMsg;

    /**
     * 是否发送对话过程消息（mq）
     */
    @Value("${send.invokeProcessMsg:false}")
    private Boolean sendInvokeProcessMsg;

    @Resource
    private AgentInfoRpcApi agentInfoRpcApi;
    /**
     * 公共线程池
     */
    @Resource
    private ExecutorService commonExecutorService;

    /**
     * 发送日志消息
     *
     * @param logMessage
     */
    public void sendLogMessage(LogMessage logMessage) {
        if (Objects.isNull(logMessage)) {
            log.info("消息为空，不发送日志消息");
            return;
        }
        try {
            logMessage.setSendTime(LocalDateTime.now());
            PropertyFilter filter = (object, name, value) -> !name.equals("api") && !name.equals("secret");
            kafkaMQService.sendMessage(LogTopicConstants.AGENT_LOG_MESSAGE_TOPIC, JSON.toJSONString(logMessage, filter));
        } catch (Exception e) {
            log.error("【日志埋点】发送日志埋点发生异常！", e);
        }
    }

    /**
     * 异步发送日志消息
     *
     * @param logMessage
     */
    public void asyncSendLogMessage(LogMessage logMessage) {
        commonExecutorService.execute(() -> {
            sendLogMessage(logMessage);
        });
    }

    /**
     * 发送对话埋点
     *
     * @param dialogResultMessage
     */
    public void sendDialogResultMessage(DialogResultMessage dialogResultMessage) {
        try {
            if (Boolean.FALSE.equals(sendDialogResultMsg)) {
                return;
            }
            String dialogResultMessageEntry = CharSequenceUtil.isBlank(dialogResultMessage.getEntry()) ? EntryEnum.OFFLINE.getCode() : dialogResultMessage.getEntry();
            String entry = CharSequenceUtil.isBlank(LocalContext.getEntry()) ? dialogResultMessageEntry : LocalContext.getEntry();
            dialogResultMessage.setEntry(entry);
            dialogResultMessage.setLogId(CharSequenceUtil.isBlank(dialogResultMessage.getLogId()) ? UUID.randomUUID().toString() : dialogResultMessage.getLogId());
            kafkaMQService.sendMessage(LogTopicConstants.DIALOG_RESULT_TOPIC, JsonUtils.toJsonString(dialogResultMessage));
        } catch (Exception e) {
            log.error("【对话埋点】发送对话埋点发生异常！", e);
        }
    }

    /**
     * 异步发送对话埋点
     *
     * @param dialogResultMessage
     */
    public void asyncSendDialogResultMessage(DialogResultMessage dialogResultMessage) {
        if (Boolean.FALSE.equals(sendDialogResultMsg)) {
            return;
        }
        commonExecutorService.execute(() -> sendDialogResultMessage(dialogResultMessage));
    }

    /**
     * 发送过程埋点
     *
     * @param invokeProcessMessage
     */
    public void sendInvokeProcessMessage(InvokeProcessMessage invokeProcessMessage) {
        try {
            if (Boolean.FALSE.equals(sendInvokeProcessMsg)) {
                return;
            }
            if (CharSequenceUtil.isNotBlank(invokeProcessMessage.getAgentCode()) && CharSequenceUtil.isBlank(invokeProcessMessage.getAgentName())) {
                LocalInfo localInfo = LocalContext.get();
                Result<AgentInfoResponse> agentInfoResponseResult = agentInfoRpcApi.queryAgentInfo(invokeProcessMessage.getAgentCode(), EnvTypeEnum.TEST.getCode().equalsIgnoreCase(localInfo.getEnv()));
                if (Boolean.TRUE.equals(agentInfoResponseResult.isSuccess()) && Objects.nonNull(agentInfoResponseResult.getData())) {
                    invokeProcessMessage.setAgentName(agentInfoResponseResult.getData().getAgentName());
                }
            }
            String entry = CharSequenceUtil.isBlank(LocalContext.getEntry()) ? EntryEnum.OFFLINE.getCode() : LocalContext.getEntry();
            invokeProcessMessage.setEntry(entry);
            invokeProcessMessage.setLogId(CharSequenceUtil.isBlank(invokeProcessMessage.getLogId()) ? UUID.randomUUID().toString() : invokeProcessMessage.getLogId());
            kafkaMQService.sendMessage(LogTopicConstants.INVOKE_PROCESS_TOPIC, JsonUtils.toJsonString(invokeProcessMessage));
        } catch (Exception e) {
            log.error("【过程埋点】发送过程埋点发生异常！", e);
        }
    }

    /**
     * 异步发送过程埋点
     *
     * @param invokeProcessMessage
     */
    public void asyncSendInvokeProcessMessage(InvokeProcessMessage invokeProcessMessage) {
        if (Boolean.FALSE.equals(sendInvokeProcessMsg)) {
            return;
        }
        commonExecutorService.execute(() -> {
            sendInvokeProcessMessage(invokeProcessMessage);
        });
    }
}
