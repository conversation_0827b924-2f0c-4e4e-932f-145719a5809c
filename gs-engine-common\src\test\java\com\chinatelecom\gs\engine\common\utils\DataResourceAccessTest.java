package com.chinatelecom.gs.engine.common.utils;

import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.platform.client.config.PlatformSsoProperties;
import com.chinatelecom.cloud.platform.client.enums.ResourceLevelEnum;
import com.chinatelecom.cloud.platform.client.model.req.DataResourceBaseRequest;
import com.chinatelecom.cloud.platform.client.model.req.DataResourceBatchCreateRequest;
import com.chinatelecom.cloud.platform.client.model.res.DataResourceDetailResponse;
import com.chinatelecom.cloud.platform.client.util.DataResourceAuthUtils;
import com.chinatelecom.cloud.platform.client.util.DataResourceUtil;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class DataResourceAccessTest {

    private static final String TEST_APP_CODE = "testAppCode";
    private static final String TEST_APP_SECRET = "testAppSecret";
    private static final String TEST_TENANT_ID = "tenant456";
    private static final String TEST_USER_ID = "user123";
    private static final String TEST_RESOURCE_TYPE = "业务类型";

    @InjectMocks
    private DataResourceAccess dataResourceAccess;

    @Mock
    private PlatformSsoProperties platformSsoProps;

    @Mock
    private RequestContext requestContext;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(platformSsoProps.getAppCode()).thenReturn(TEST_APP_CODE);
        when(platformSsoProps.getAppSecret()).thenReturn(TEST_APP_SECRET);
        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setUserId(TEST_USER_ID);
        requestInfo.setTenantId(TEST_TENANT_ID);
        requestInfo.setAppCode(TEST_APP_CODE);
        RequestContext.set(requestInfo);
    }

    @AfterEach
    public void tearDown() {
        RequestContext.remove();
    }

    // ------------------ buildBatchDataResourceRequest ------------------

    @Test
    public void testBuildBatchDataResourceRequest() {
        String resourceCode = "RC001";
        String resourceName = "测试资源";

//        when(requestContext.getUserId()).thenReturn(TEST_USER_ID);
//        when(requestContext.getTenantId()).thenReturn(TEST_TENANT_ID);

        DataResourceBatchCreateRequest request = dataResourceAccess.buildBatchDataResourceRequest(resourceCode, resourceName, TEST_RESOURCE_TYPE);

        assertNotNull(request);
        assertEquals(TEST_APP_CODE, request.getAppCode());
        assertEquals(TEST_APP_SECRET, request.getAppSecret());
        assertEquals(TEST_TENANT_ID, request.getCorpCode());
        assertEquals(TEST_USER_ID, request.getUserId());
        assertEquals(1, request.getResources().size());

        DataResourceBaseRequest base = request.getResources().get(0);
        assertEquals(resourceCode, base.getResourceCode());
        assertEquals(resourceName, base.getResourceName());
        assertEquals(ResourceLevelEnum.CONFIDENTIAL.getValue(), base.getResourceLevel());
        assertEquals(TEST_USER_ID, base.getResourceOwner());
        assertEquals(TEST_RESOURCE_TYPE, base.getResourceType());
    }

    // ------------------ createOrUpdate ------------------

    @SuppressWarnings("unchecked")
    @Test
    public void testCreateOrUpdate_Success() {
        DataResourceBatchCreateRequest request = new DataResourceBatchCreateRequest();
        List<DataResourceDetailResponse> dataList = Collections.singletonList(new DataResourceDetailResponse());

        BaseResult<List<DataResourceDetailResponse>> result = mock(BaseResult.class);
        when(result.ifSuccess()).thenReturn(true);
        when(result.getData()).thenReturn(dataList);

        try (MockedStatic<DataResourceUtil> mockedStatic = Mockito.mockStatic(DataResourceUtil.class)) {
            mockedStatic.when(() -> DataResourceUtil.createOrUpdate(request)).thenReturn(result);

            List<DataResourceDetailResponse> response = dataResourceAccess.createOrUpdate(request);
            assertThat(response).isNotNull().isEqualTo(dataList);
        }
    }

    @Test
    public void testCreateOrUpdate_Failure() {
        DataResourceBatchCreateRequest request = new DataResourceBatchCreateRequest();

        BaseResult<List<DataResourceDetailResponse>> result = mock(BaseResult.class);
        when(result.ifSuccess()).thenReturn(false);
        when(result.getCode()).thenReturn(500);
        when(result.getMsg()).thenReturn("Internal Error");

        try (MockedStatic<DataResourceUtil> mockedStatic = Mockito.mockStatic(DataResourceUtil.class)) {
            mockedStatic.when(() -> DataResourceUtil.createOrUpdate(request)).thenReturn(result);

            BizException exception = assertThrows(BizException.class, () -> dataResourceAccess.createOrUpdate(request));
            assertThat(exception.getCode()).isEqualTo("CA003");
        }
    }

    @Test
    public void testCreateOrUpdate_NullResult() {
        DataResourceBatchCreateRequest request = new DataResourceBatchCreateRequest();

        try (MockedStatic<DataResourceUtil> mockedStatic = Mockito.mockStatic(DataResourceUtil.class)) {
            mockedStatic.when(() -> DataResourceUtil.createOrUpdate(request)).thenReturn(null);

            assertTrue(dataResourceAccess.createOrUpdate(request).size() == 0);
        }
    }

    // ------------------ getAuthedResourceCodesByType ------------------

    private BaseResult<Set<String>> mockSuccessAuthResult(Set<String> data) {
        BaseResult<Set<String>> result = mock(BaseResult.class);
        when(result.ifSuccess()).thenReturn(true);
        when(result.getData()).thenReturn(data);
        return result;
    }

    private BaseResult<Set<String>> mockFailureAuthResult(String msg) {
        BaseResult<Set<String>> result = mock(BaseResult.class);
        when(result.ifSuccess()).thenReturn(false);
        when(result.getCode()).thenReturn(500);
        when(result.getMsg()).thenReturn(msg);
        return result;
    }

    @Test
    public void testGetAuthedResourceCodesByType_Success() throws Exception {
        Set<String> expectedSet = new HashSet<>(Arrays.asList("R1", "R2"));
        BaseResult<Set<String>> result = mockSuccessAuthResult(expectedSet);

        try (MockedStatic<DataResourceAuthUtils> mockedStatic = Mockito.mockStatic(DataResourceAuthUtils.class)) {
            mockedStatic.when(() -> DataResourceAuthUtils.getAuthedResourceCodes(any()))
                    .thenReturn(result);

            Set<String> actual = dataResourceAccess.getAuthedResourceCodesByType(TEST_TENANT_ID, TEST_USER_ID, TEST_RESOURCE_TYPE);
            assertThat(actual).isNotNull().containsExactlyInAnyOrderElementsOf(expectedSet);
        }
    }

    @Test
    public void testGetAuthedResourceCodesByType_EmptyData() throws Exception {
        BaseResult<Set<String>> result = mockSuccessAuthResult(null);

        try (MockedStatic<DataResourceAuthUtils> mockedStatic = Mockito.mockStatic(DataResourceAuthUtils.class)) {
            mockedStatic.when(() -> DataResourceAuthUtils.getAuthedResourceCodes(any()))
                    .thenReturn(result);

            Set<String> actual = dataResourceAccess.getAuthedResourceCodesByType(TEST_TENANT_ID, TEST_USER_ID, TEST_RESOURCE_TYPE);
            assertThat(actual).isEmpty();
        }
    }

    /**
     * 测试当getAuthedResourceCodes返回null时的情况
     * Test when getAuthedResourceCodes returns null
     */
    /**
     * 测试当getAuthedResourceCodes方法返回null时的情况
     * Test when getAuthedResourceCodes method returns null
     */
    @Test
    void testGetAuthedResourceCodesByType_MethodReturnsNull() {
        try (MockedStatic<DataResourceAuthUtils> mockedStatic = Mockito.mockStatic(DataResourceAuthUtils.class)) {
            // 修复：模拟DataResourceAuthUtils.getAuthedResourceCodes方法本身返回null
            BaseResult<Set<String>> setBaseResult = new BaseResult<>();
            mockedStatic.when(() -> DataResourceAuthUtils.getAuthedResourceCodes(any()))
                    .thenReturn(setBaseResult);

            // 根据方法实现
            Set<String> resourceCodesByType = dataResourceAccess.getAuthedResourceCodesByType(TEST_TENANT_ID, TEST_USER_ID, TEST_RESOURCE_TYPE);
            assertThat(resourceCodesByType).isEmpty();

        }
    }

    @Test
    public void testGetAuthedResourceCodesByType_Exception() throws Exception {
        try (MockedStatic<DataResourceAuthUtils> mockedStatic = Mockito.mockStatic(DataResourceAuthUtils.class)) {
            mockedStatic.when(() -> DataResourceAuthUtils.getAuthedResourceCodes(Mockito.any()))
                    .thenThrow(new RuntimeException("Network error"));

            BizException exception = assertThrows(BizException.class, () ->
                    dataResourceAccess.getAuthedResourceCodesByType(TEST_TENANT_ID, TEST_USER_ID, TEST_RESOURCE_TYPE));
            assertThat(exception.getCode()).isEqualTo("CA005");
        }
    }

    @Test
    public void testGetAuthedResourceCodesByType_NullInput() throws Exception {
        try (MockedStatic<DataResourceAuthUtils> mockedStatic = Mockito.mockStatic(DataResourceAuthUtils.class)) {

            BaseResult<Set<String>> mockResult = mock(BaseResult.class);
            when(mockResult.ifSuccess()).thenReturn(true);
            when(mockResult.getData()).thenReturn(Collections.emptySet());

            mockedStatic.when(() -> DataResourceAuthUtils.getAuthedResourceCodes(Mockito.any()))
                    .thenReturn(mockResult);

            Set<String> result = dataResourceAccess.getAuthedResourceCodesByType(null, null, null);
            assertThat(result).isNotNull().isEmpty();
        }
    }


}
