package com.chinatelecom.gs.engine.core.manager.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.common.platform.PlatformAuthService;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.platform.StatOpenApi;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.UserInfoUtils;
import com.chinatelecom.gs.engine.core.manager.convert.BaseModelConvert;
import com.chinatelecom.gs.engine.core.manager.convert.ConfigConvert;
import com.chinatelecom.gs.engine.core.manager.param.CodeParam;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.manager.vo.config.InternalFunctionConfig;
import com.chinatelecom.gs.engine.core.manager.vo.config.base.*;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月28日
 */

@RestController
@Slf4j
@Tag(name = "配置管理接口")
@RefreshScope
@RequestMapping({Apis.BASE_PREFIX + Apis.WEB_API + Apis.CONFIG,
        Apis.BASE_PREFIX + Apis.OPENAPI + Apis.CONFIG,
        Apis.BASE_PREFIX + Apis.RPC_PREFIX + Apis.CONFIG})
public class BaseConfigAppController {

    /**
     * 是否禁止关联工具, 默认值为false,不禁止关联工具
     */
    @Value("${app.default.forbidRelatedTools:false}")
    private Boolean forbidRelatedTools;

    @Resource
    private BaseConfigAppService baseConfigAppService;

    @Resource
    private PlatformAuthService platformAuthService;

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "配置详情分页列表")
    @PlatformRestApi(name = "配置详情分页列表", groupName = "配置管理")
    @StatOpenApi(name = "配置详情分页列表", groupName = "配置管理")
    @PostMapping(KmsApis.PAGE_API)
    @AuditLog(businessType = "配置管理接口", operType = "配置详情分页列表", operDesc = "配置详情分页列表", objId = "#param.businessNo")
    public Result<Page<BaseConfigVO>> page(@Validated @RequestBody BaseConfigQueryParam param) {
        return Result.success(baseConfigAppService.pageQuery(param));
    }

    @Operation(summary = "配置详情")
    @StatOpenApi(name = "配置详情", groupName = "配置管理")
    @PlatformRestApi(name = "配置详情", groupName = "配置管理")
    @PostMapping(KmsApis.GET_API)
    @AuditLog(businessType = "配置管理接口", operType = "配置详情", operDesc = "配置详情", objId = "#param.businessNo")
    public Result<Object> query(@Validated @RequestBody BaseConfigQueryParam param) {
        return Result.success(baseConfigAppService.getConfigOrDefault(param.getConfigType(), param.getBusinessNo()));
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "指定维度维度自动设值配置详情查询")
    @StatOpenApi(name = "指定维度维度自动设值配置详情查询", groupName = "配置管理")
    @PlatformRestApi(name = "指定维度维度自动设值配置详情查询", groupName = "配置管理")
    @PostMapping(KmsApis.AUTO + KmsApis.GET_API)
    @AuditLog(businessType = "配置管理接口", operType = "指定维度维度自动设值配置详情查询", operDesc = "指定维度维度自动设值配置详情查询", objId = "#param.businessNo")
    public Result<Object> queryAuto(@Validated @RequestBody BaseConfigDetailQueryParam param) {
        DimensionEnum dimension = param.getDimension();
        if (dimension != null) {
            switch (dimension) {
                case SYSTEM:
                    param.setBusinessNo(DimensionEnum.SYSTEM.name());
                    break;
                case TENANT:
                    param.setBusinessNo(RequestContext.getTenantId());
                    break;
                default:
                    throw new IllegalArgumentException("不支持的维度类型");
            }
        }
        BizAssert.notEmpty(param.getBusinessNo(), "AA055", "业务标识不能为空");
        Object result = baseConfigAppService.getConfigOrDefault(param.getConfigType(), param.getBusinessNo());
        result = processInternalConfig(param, result);
        return Result.success(result);
    }

    /**
     * 文件类型根据是否多模态进行一次过滤
     *
     * @param param
     * @param result
     * @return
     */
    private Object processInternalConfig(BaseConfigDetailQueryParam param, Object result) {
        if (StringUtils.equals(param.getConfigType(), ConfigConvert.INTERNAL_CONFIG) && result instanceof InternalFunctionConfig config) {
            boolean isMultiModal = UserInfoUtils.checkResource(Constants.MULTIMODAL_UPLOAD);
            config.filterMultimodal(isMultiModal);
            config.setForbidRelatedTools(forbidRelatedTools);
            result = config;
        }
        return result;
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "指定维度维度配置详情查询")
    @StatOpenApi(name = "指定维度维度配置详情查询", groupName = "配置管理")
    @PlatformRestApi(name = "指定维度维度配置详情查询", groupName = "配置管理")
    @PostMapping(KmsApis.AUTO + KmsApis.GETS_API)
    @AuditLog(businessType = "配置管理接口", operType = "指定维度维度配置详情查询", operDesc = "指定维度维度配置详情查询", objId = "null")
    public Result<Object> queryConfigs(@Validated @RequestBody BaseConfigDetailsQueryParam param) {
        return Result.success(baseConfigAppService.getConfigsOrDefault(param.getConfigTypes(), RequestContext.getTenantId()));
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "指定维度维度自动设值配置,新增或更新配置")
    @PlatformRestApi(name = "指定维度维度自动设值配置,新增或更新配置", groupName = "配置管理")
    @StatOpenApi(name = "指定维度维度自动设值配置,新增或更新配置", groupName = "配置管理")
    @PostMapping(KmsApis.AUTO)
    @AuditLog(businessType = "配置管理接口", operType = "指定维度维度自动设值配置,新增或更新配置", operDesc = "指定维度维度自动设值配置,新增或更新配置", objId = "#param.businessNo")
    public Result<Boolean> addAuto(@Validated @RequestBody BaseConfigAutoCreateParam param) {
        DimensionEnum dimension = param.getDimension();
        if (dimension != null) {
            switch (dimension) {
                case SYSTEM:
                    param.setBusinessNo(DimensionEnum.SYSTEM.name());
                    break;
                case TENANT:
                    param.setBusinessNo(RequestContext.getTenantId());
                    break;
                default:
                    throw new IllegalArgumentException("不支持的维度类型");
            }
        }
        BizAssert.notEmpty(param.getBusinessNo(), "AA055", "业务标识不能为空");
        BaseConfigCreateParam createParam = BaseModelConvert.INSTANCE.convert(param);
        return add(createParam);
    }


    @Operation(summary = "新增或更新配置")
    @StatOpenApi(name = "新增或更新配置", groupName = "配置管理")
    @PlatformRestApi(name = "新增或更新配置", groupName = "配置管理")
    @PostMapping
    @AuditLog(businessType = "配置管理接口", operType = "新增或更新配置", operDesc = "新增或更新配置", objId = "#createParam.businessNo")
    public Result<Boolean> add(@Validated @RequestBody BaseConfigCreateParam createParam) {
        baseConfigAppService.createOrUpdate(createParam);
        return Result.success(true);
    }

    @Operation(summary = "更新配置")
    @StatOpenApi(name = "更新配置", groupName = "配置管理")
    @PlatformRestApi(name = "更新配置", groupName = "配置管理")
    @PutMapping(KmsApis.CODE_PATH)
    @AuditLog(businessType = "配置管理接口", operType = "更新配置", operDesc = "更新配置", objId = "#dto.businessNo")
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody BaseConfigUpdateParam dto) {
        return Result.success(baseConfigAppService.update(code, dto));
    }

    @Operation(summary = "移除配置")
    @StatOpenApi(name = "移除配置", groupName = "配置管理")
    @PlatformRestApi(name = "移除配置", groupName = "配置管理")
    @PostMapping(KmsApis.DELETE_API)
    @AuditLog(businessType = "配置管理接口", operType = "移除配置", operDesc = "移除配置", objId = "null")
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
        return Result.success(baseConfigAppService.delete(codes));
    }

}
