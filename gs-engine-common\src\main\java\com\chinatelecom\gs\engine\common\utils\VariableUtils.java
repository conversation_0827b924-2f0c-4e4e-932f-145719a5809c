package com.chinatelecom.gs.engine.common.utils;

import com.chinatelelcom.gs.engine.sdk.common.enums.ParamTypeEnum;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Slf4j
public class VariableUtils {

    /**
     * 获取变量类型
     *
     * @param selector
     * @return
     */
    public static String getVarType(String[] selector) {
        checkSelector(selector);
        return selector[0];
    }

    /**
     * 获取关联Id
     *
     * @param selector
     * @return
     */
    public static String getReferId(String[] selector, Map<String, Object> variableRunContext) {
        checkSelector(selector);
        if (StringUtils.equalsIgnoreCase(selector[1], "workFlowId") || StringUtils.equalsIgnoreCase(selector[1], "agentCode")) {
            return VariableParseUtils.parseJsonPathValue(selector[1], variableRunContext);
        }
        return selector[1];
    }

    /**
     * 获取变量名
     *
     * @param selector
     * @return
     */
    public static String getVarName(String[] selector) {
        checkSelector(selector);
        return selector[2];
    }

    public static String getSelectorAbsolutePath(String[] selector, Map<String, Object> variableRunContext) {
        checkSelector(selector);
        String referId = getReferId(selector, variableRunContext);
        return String.join(".", selector[0], referId, selector[2]);
    }

    public static String[] buildSelector(String varType, String scopeId, String varName) {
        return new String[]{varType, scopeId, varName};
    }

    private static void checkSelector(String[] selector) {
        if (!validateSelector(selector)) {
            throw new BizException("BA000", "变量选择器不可用！");
        }
    }

    /**
     * 校验变量选择器
     * [varType,env,sys/custom/agentCode/workflowid,varName]
     *
     * @param selector
     */
    public static boolean validateSelector(String[] selector) {
        if (selector == null || selector.length != 3) {
            return false;
        }
        for (String element : selector) {
            if (element == null || element.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    public static Object parseValue(String valueStr, int dataType) {
        if (valueStr == null) {
            return null;
        }
        ParamTypeEnum dataTypeEnum = ParamTypeEnum.getByValue(dataType);
        return switch (dataTypeEnum) {
            case STRING -> valueStr;
            case INTEGER -> Integer.parseInt(valueStr);
            case NUMBER -> Double.parseDouble(valueStr);
            case BOOLEAN -> Boolean.parseBoolean(valueStr);
            case OBJECT -> JsonUtils.parseObject(valueStr, Object.class);
            case ARRAY_STRING -> JsonUtils.parseObjectArray(valueStr, String.class);
            case ARRAY_INTEGER -> JsonUtils.parseObjectArray(valueStr, String.class);
            case ARRAY_NUMBER -> JsonUtils.parseObjectArray(valueStr, Number.class);
            case ARRAY_BOOLEAN -> JsonUtils.parseObjectArray(valueStr, Boolean.class);
            case ARRAY_OBJECT -> JsonUtils.parseObjectArray(valueStr, Object.class);
            default -> null;
        };
    }

    public static boolean checkDataType(Object value, int dataType) {
        if (value == null) {
            return true;
        }
        if (value instanceof List<?> list) {
            return checkListDataType(list, dataType);
        }
        return checkBaseDataType(value, dataType);
    }

    private static boolean checkListDataType(List<?> list, int dataType) {
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        return checkBaseDataType(list.get(0), dataType);
    }

    private static boolean checkBaseDataType(Object value, int dataType) {
        ParamTypeEnum dataTypeEnum = ParamTypeEnum.getByValue(dataType);
        return switch (dataTypeEnum) {
            case STRING -> value instanceof String;
            case INTEGER -> value instanceof Integer;
            case NUMBER -> value instanceof Number;
            case BOOLEAN -> value instanceof Boolean;
            case OBJECT -> value instanceof Object;
            default -> false;
        };
    }

    public static String toJsonString(Object value, int dataType) {
        boolean checkFlag = checkDataType(value, dataType);
        if (!checkFlag) {
            log.error("变量ToJsonString失败, 数据类型不匹配。value:{},dataType:{}", value, dataType);
            throw new BizException("AA001", "变量数据类型不匹配");
        }
        try {
            ParamTypeEnum dataTypeEnum = ParamTypeEnum.getByValue(dataType);
            return switch (dataTypeEnum) {
                case STRING -> value.toString();
                case INTEGER -> value.toString();
                case NUMBER -> value.toString();
                case BOOLEAN -> value.toString();
                case OBJECT -> JsonUtils.toJsonString(value);
                case ARRAY_STRING -> JsonUtils.toJsonString(value);
                case ARRAY_INTEGER -> JsonUtils.toJsonString(value);
                case ARRAY_NUMBER -> JsonUtils.toJsonString(value);
                case ARRAY_BOOLEAN -> JsonUtils.toJsonString(value);
                case ARRAY_OBJECT -> JsonUtils.toJsonString(value);
                default -> null;
            };
        } catch (Exception e) {
            log.error("变量ToJsonString异常！", e);
            throw new BizException("AA002", "变量ToJsonString异常", e);
        }
    }
}
