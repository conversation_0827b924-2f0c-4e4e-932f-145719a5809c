package com.chinatelecom.gs.engine.channel.api.param;

import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/25 14:34
 * @description
 */
@Data
@Schema(description = "渠道对象")
public class ChannelInfoParam implements Serializable {
    @Serial
    private static final long serialVersionUID = -559888949117161960L;

    @Schema(description = "渠道名称")
    @NotBlank(message = "渠道名称不能为空")
    private String channelName;

    @Schema(description = "渠道类型")
    @NotNull(message = "渠道类型不能为空")
    private ChannelTypeEnum channelType;

    @Schema(description = "渠道id")
    @NotBlank(message = "渠道id不能为空")
    private String channelId;

    /**
     * 机器人code
     */
    @Schema(description = "机器人code")
    @NotBlank(message = "机器人code不能为空")
    private String robotCode;
}
