package com.chinatelecom.gs.engine.core.entity.ability.recognition.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.chinatelecom.gs.engine.common.enums.LogStatusEnum;
import com.chinatelecom.gs.engine.common.enums.LogTypeEnum;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.entity.ability.recognition.AbstractModelEntityRecognitionAbilityService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
import com.chinatelecom.gs.engine.robot.sdk.dto.EntityContent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 通用小模型实体识别
 * @USER: pengmc1
 * @DATE: 2025/2/6 18:06
 */

@Slf4j
@Service
public class GeneralModelEntityRecognitionAbilityService extends AbstractModelEntityRecognitionAbilityService {

    @Resource
    private ModelServiceClient remoteServiceClient;

    @Resource
    private RestTemplate restNoBalancedTemplate;

    /**
     * 识别能力标识
     *
     * @return
     */
    @Override
    public String abilityCode() {
        return EntityAbilityEnum.MODEL_RECOGNITION.getCode() + ":generalEntityRecognition";
    }

    /**
     * 执行实体识别
     *
     * @param request
     * @param entityDetailList
     * @return
     */
    @Override
    protected List<Entity> doPredict(EntityRecognitionRequest request, List<EntityDetailVO> entityDetailList) {
        String modelCode = getAbilityConfig(entityDetailList.get(0), EntityAbilityEnum.MODEL_RECOGNITION).getModelCode();
        log.info("【实体识别】【{}】模型编码为：{}", abilityCode(), modelCode);
        //获取模型元数据
        ModelPageListParam modelParam = remoteServiceClient.queryByModelCode(modelCode);
        //进行通用小模型实体识别
        Map<String,Entity> entityMap = recognition(modelParam, request);
        //筛选出需要的实体结果
        List<Entity> finalEntities = Lists.newArrayList();
        entityDetailList.forEach( entityDetailVO -> {
            String bindCode = getAbilityConfig(entityDetailVO, EntityAbilityEnum.MODEL_RECOGNITION).getBindCode();
            List<Entity> itemEntities = Lists.newArrayList();
            if(entityMap.containsKey(bindCode)){
                Entity entity = entityMap.get(bindCode);
                entity.setEntityCode(entityDetailVO.getEntityCode());
                entity.setEntityName(entityDetailVO.getEntityName());
                entity.setEntityType(entityDetailVO.getEntityType());
                entity.setTenantId(entityDetailVO.getTenantId());
                itemEntities.add(entity);
            }
            List<Entity> itemFinalEntities = filterConvertEntity(entityDetailVO, itemEntities);
            if(CollectionUtils.isNotEmpty(itemFinalEntities)){
                finalEntities.addAll(itemFinalEntities);
            }
        });
        return finalEntities;
    }
    /**
     * 进行通用小模型实体识别
     * @param modelParam
     * @param request
     * @return
     */
    private Map<String,Entity> recognition(ModelPageListParam modelParam, EntityRecognitionRequest request){
        LocalDateTime startTime = LocalDateTime.now();
        Map<String, Object> paramMap = Maps.newHashMap();
        String rpcResult =null;
        LogStatusEnum logStatusEnum = LogStatusEnum.SUCCESS;
        try{
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Content-Type", "application/json");
            headers.set("App-Key", modelParam.getApiKey());
            headers.set("App-Sign", modelParam.getModelSecret());
            paramMap.put("seqid", request.getMessageId());
            paramMap.put("timestamp", new Date().getTime());
            paramMap.put("input_text", request.getQuery());
            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(paramMap,headers);
            log.info("【实体识别】【{}】通用小模型实体识别请求参数：{}", abilityCode(), JsonUtils.toJsonString(httpEntity));
            ResponseEntity<String> responseEntity = restNoBalancedTemplate.exchange(
                    modelParam.getExternalModelUrl(),
                    HttpMethod.POST,
                    httpEntity,
                    String.class);
            rpcResult = responseEntity.getBody();
            log.info("【实体识别】【{}】通用小模型实体识别返回：{}",abilityCode(), rpcResult);
        }catch (Exception e){
            logStatusEnum = LogStatusEnum.FAILED;
            log.error("通用实体识别异常！", e);
        }finally {
            sendTraceLog("通用实体识别", LogTypeEnum.COMMON_ENTITY_REG,paramMap, rpcResult, logStatusEnum, modelParam, startTime, LocalDateTime.now());
        }
        JSONObject modelResult = JSON.parseObject(rpcResult);
        return convertToEntityMap(modelResult);
    }

    /**
     * 转换成实体map
     * @param modelResult
     * @return
     */
    private Map<String,Entity> convertToEntityMap(JSONObject modelResult){
        Map<String,Entity> entityMap = Maps.newHashMap();
        if(Objects.isNull(modelResult) || !"10000".equals(modelResult.get("code").toString()) || !modelResult.containsKey("data")){
            return entityMap;
        }
        JSONArray dataArr = modelResult.getJSONArray("data");
        for (int i = 0; i < dataArr.size(); i++) {
            JSONObject dataObj = dataArr.getJSONObject(i);
            if(dataObj.containsKey("item") && dataObj.getJSONArray("item").size() > 0){
                Entity entity = new Entity();
                JSONArray itemArr = dataObj.getJSONArray("item");
                List<EntityContent> entityContents = Lists.newArrayList();
                for(int j=0; j< itemArr.size(); j++){
                    JSONObject itemObj = itemArr.getJSONObject(j);
                    EntityContent entityContent = new EntityContent();
                    entityContent.setStart(itemObj.getJSONArray("span").getInteger(0));
                    entityContent.setEnd(itemObj.getJSONArray("span").getInteger(1));
                    entityContent.setValue(itemObj.getString("value"));
                    entityContent.setRawValue(itemObj.getString("value"));
                    entityContents.add(entityContent);
                }
                entity.setEntityContents(entityContents);
                entityMap.put(dataObj.getString("name"), entity);
            }
        }
        return entityMap;
    }
}
