package com.chinatelecom.gs.engine.common.auth;

public interface MenuConfig {

    /**
     * 机器人菜单
     */
    String ROBOT = "/robot";

    /**
     * 文档知识库
     */
    String WORD_KNOWLEDGE = "/word-knowledge";

    /**
     * 问答知识库
     */
    String QA_KNOWLEDGE = "/qa-knowledge";

    /**
     * 知识检索
     */
    String KNOWLEDGE_SEARCH = "/knowledgeSearch";

    /**
     * 知识收藏
     */
    String MY_COLLECTION = "/myCollection";

    /**
     * 智能写作
     */
    String CUSTOM_REPORT = "/customReport";

    /**
     * 标签管理
     */
    String TAGS_MANAGE = "/sysconfig/tagsManage";

    /**
     * 发布管理
     */
    String PUBLISH = "/sysconfig/publish";

    /**
     * 语音配置
     */
    String VOICE = "/createbot/voice";

    /**
     * 对话流
     */
    String DIALOG_FLOW = "/qaflow";

    /**
     * 工作流
     */
    String WORKFLOW = "/workflow";

    /**
     * 插件
     */
    String MY_PLUGIN = "/myPlugin";

    /**
     * 应答策略
     */
    String STRATEGY = "/strategyTemplate";

    /**
     * 插件市场
     */
    String PLUGIN_MARKET = "/pluginMarket";

    /**
     * 外呼
     */
    String OUTBOUND = "/outbound";

    /**
     * telebi
     */
    String TELEBI = "/telebi";

    /**
     * 流程中心
     */
    String PROCESS = "/approval/process";

    /**
     * 流程设计
     */
    String BPMN = "/approval/bpmn";

    /**
     * 监听管理
     */
    String LISTENER = "/approval/listener";

    /**
     * AI报告
     */
    String TELEBI_CUSTOM_REPORT = "/telebi/customReport";

    /**
     * 数据库
     */
    String DATABASE = "/database";

    /**
     * 外呼日志
     */
    String CALL_LOGS = "/outbound/callLogs";

    /**
     * 黑名单
     */
    String BLACK_LIST = "/outbound/blackList";

    /**
     * 知识库
     */
    String KNOWLEDGE = "/knowledge";

    /**
     * 工作台/回访
     */
    String DEMO = "/demo";

    /**
     * 首页
     */
    String HOME = "/home";

    /**
     * 智能质检
     */
    String AIQC = "/aiqc";


    /**
     * 业务流市场
     */
    String FLOW_MARKET = "/flowMarket";

    /**
     * 机器人市场
     */
    String ROBOT_MARKET = "/robotMarket";

    /**
     * 智能IVR
     */
    String IVR = "/ivr";

    /**
     * 变量管理
     */
    String VARIABLE_MANAGE = "/variable";

    /**
     * 敏感词管理
     */
    String SENSITIVE = "/sysconfig/sensitiveManage";

    /**
     * 模型管理
     */
    String MODEL_MANAGE = "/flow-sub/sysconfig/model";

    /**
     * 提示词管理
     */
    String PROMPT = "prompt";
}
