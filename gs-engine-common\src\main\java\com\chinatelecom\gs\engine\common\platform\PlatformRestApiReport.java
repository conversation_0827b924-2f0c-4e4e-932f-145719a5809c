package com.chinatelecom.gs.engine.common.platform;

import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.platform.client.config.PlatformSsoProperties;
import com.chinatelecom.cloud.platform.client.rpc.ApiRegisteryRequest;
import com.chinatelecom.cloud.platform.client.util.PermissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.condition.PatternsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 取消openApi, rpc的自动注册，自定义实现
 * @date 2025年04月10日
 */
@Component
@Slf4j
public class PlatformRestApiReport implements CommandLineRunner {


    @Value("${server.servlet.context-path:}")
    private String contextPath;

    /**
     * 接口自动注册开关
     */
    @Value("${platform.client.apiReportEnable:false}")
    private Boolean apiReportEnable;

    /**
     * 接口鉴权开关
     */
    @Value("${platform.client.apiAuthEnable:false}")
    private Boolean apiAuthEnable;

    @Autowired
    @Qualifier("requestMappingHandlerMapping")
    private RequestMappingHandlerMapping requestMappingHandlerMapping;

    @Resource
    @Qualifier("defaultPoolExecutor")
    private ExecutorService defaultPoolExecutor;

    @Autowired
    private PlatformSsoProperties platformSsoProperties;

    public final static String WEB_REGEX = "/ais/*/web/**";

    private AntPathMatcher ANT_PATH_MATCHER = new AntPathMatcher();

    public void run(String... args) throws Exception {
        if(!apiAuthEnable) {
            return;
        }

        if (!apiReportEnable) {
            log.info("API自动注册云平台已关闭，可能需要手动添加。");
            return;
        }

        // 线程池执行注册，避免长时间占用启动时间
        defaultPoolExecutor.submit(() -> {
            for (Map.Entry<RequestMappingInfo, HandlerMethod> entry :
                    requestMappingHandlerMapping.getHandlerMethods().entrySet()) {
                RequestMappingInfo mappingInfo = entry.getKey();
                HandlerMethod handlerMethod = entry.getValue();
                PlatformRestApi restApi = handlerMethod.getMethodAnnotation(PlatformRestApi.class);
                if (restApi != null) {
                    ApiRegisteryRequest apiRegisteryRequest = new ApiRegisteryRequest();
                    apiRegisteryRequest.setAppCode(platformSsoProperties.getAppCode());
                    apiRegisteryRequest.setPermissions(new ArrayList<>());
                    // 设置角色模板名称
                    apiRegisteryRequest.setMenuName(restApi.menuName());
                    apiRegisteryRequest.setMenuUrl(restApi.menuUrl());
                    apiRegisteryRequest.setParentMenuName(restApi.parentMenuName());
                    Set<RequestMethod> methods = mappingInfo.getMethodsCondition().getMethods();
                    for (RequestMethod method : methods) {
                        PatternsRequestCondition patternsCondition = mappingInfo.getPatternsCondition();
                        if (patternsCondition == null) {
                            continue;
                        }
                        Set<String> patterns = patternsCondition.getPatterns();
                        for (String pattern : patterns) {
                            pattern = StringUtils.isNotEmpty(contextPath) ? contextPath + pattern : pattern;
                            //仅注册web接口
                            if (!ANT_PATH_MATCHER.match(WEB_REGEX, pattern)) {
                                continue;
                            }

                            String pat = pattern.replaceAll("\\{[^}]+\\}", "*");
                            log.info("即将注册API: {}, 分组 {}, {} {} ", restApi.name(), restApi.groupName(), method, pat);
                            ApiRegisteryRequest.PermissionInfo permissionInfo = new ApiRegisteryRequest.PermissionInfo();
                            permissionInfo.setPermissionName(restApi.name());
                            permissionInfo.setPermissionCode(method + " " + pat);
                            permissionInfo.setPermissionType(0);
                            permissionInfo.setGroup(restApi.groupName());
                            permissionInfo.setRoleTemplateName(restApi.roleTemplateName());
                            apiRegisteryRequest.getPermissions().add(permissionInfo);
                        }
                    }
                    // 注册websocket接口
                    ApiRegisteryRequest.PermissionInfo permissionInfo = new ApiRegisteryRequest.PermissionInfo();
                    permissionInfo.setPermissionName("语音识别websocket接口");
                    permissionInfo.setPermissionCode("GET" + " " + "/ais/bot/web/websocket/asr/*/*");
                    permissionInfo.setPermissionType(0);
                    permissionInfo.setGroup("语音识别");
                    permissionInfo.setRoleTemplateName(restApi.roleTemplateName());
                    apiRegisteryRequest.getPermissions().add(permissionInfo);

                    if (!CollectionUtils.isEmpty(apiRegisteryRequest.getPermissions())) {
                        BaseResult<Boolean> register = PermissionUtils.register(apiRegisteryRequest);
                        if (register.ifSuccess()) {
                            log.info("上报API成功");
                        } else {
                            log.error("上报API失败");
                        }
                    }
                }
            }
        });
    }


}
