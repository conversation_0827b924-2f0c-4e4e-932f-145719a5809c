package com.chinatelecom.gs.engine.channel.service.messagehandler;


import com.chinatelecom.gs.engine.channel.dao.repository.ChannelMessageRecordRepository;
import com.chinatelecom.gs.engine.channel.manage.convertor.DTOBeanConvertor;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelMsgRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/26 14:06
 * @description
 */
@Slf4j
@Service
public class MessageRecordService {

    private final LinkedBlockingQueue<ChannelMsgRecordDTO> messageQueue = new LinkedBlockingQueue<>(1000);

    private static final long PUT_INTERVAL = 100;

    private static final int BATCH_SIZE = 10;

    private final ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();


    @Resource
    private ChannelMessageRecordRepository recordRepository;

    @PostConstruct
    public void initTask() {
        executorService.scheduleAtFixedRate(this::writeRecord, 0, 10, TimeUnit.SECONDS);
    }

    /**
     * 应确保在应用关闭时优雅地关闭executorService
     */
    @PreDestroy
    public void shutdown() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5000, TimeUnit.MILLISECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 记录消息
     *
     * @param messageRecord ChannelMessageRecordDTO
     */
    public void recordMessage(ChannelMsgRecordDTO messageRecord) {
        try {
            boolean isOffered = messageQueue.offer(messageRecord, PUT_INTERVAL, TimeUnit.MILLISECONDS);
            if (!isOffered) {
                log.error("消息添加到队列超时或队列已满");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 重新设置中断状态
            log.error("消息记录异常，线程被中断", e);
        }
    }

    private void writeRecord() {
        List<ChannelMsgRecordDTO> batchRecords = new ArrayList<>();
        ChannelMsgRecordDTO channelMessagerecord;
        // 使用while循环直至队列为空，避免固定次数的硬编码
        while ((channelMessagerecord = messageQueue.poll()) != null) {
            batchRecords.add(channelMessagerecord);
            // 达到批次大小时立即保存并清空批次列表，减少内存占用
            if (batchRecords.size() == BATCH_SIZE) {
                saveAndClear(batchRecords);
            }
        }
        // 处理最后一批不足BATCH_SIZE的记录
        if (!batchRecords.isEmpty()) {
            saveAndClear(batchRecords);
        }
    }

    private void saveAndClear(List<ChannelMsgRecordDTO> records) {
        try {
            recordRepository.saveBatch(records.stream()
                    .map(DTOBeanConvertor.INSTANCE::convert)
                    .collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("消息记录写入数据库失败", e);
        } finally {
            // 清空列表，帮助GC
            records.clear();
        }
    }

}
