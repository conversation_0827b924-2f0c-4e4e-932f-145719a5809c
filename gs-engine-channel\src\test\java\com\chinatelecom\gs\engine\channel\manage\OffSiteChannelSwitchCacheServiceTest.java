package com.chinatelecom.gs.engine.channel.manage;

import com.alibaba.fastjson.JSONObject;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.corekit.common.core.RedisKey;
import com.chinatelecom.gs.engine.robot.manage.common.enums.AgentConfigEnum;
import com.chinatelecom.gs.engine.robot.manage.info.dao.service.AgentBasicInfoService;
import com.chinatelecom.gs.engine.robot.manage.info.dao.service.AgentConfigService;
import com.chinatelecom.gs.engine.robot.manage.info.domain.po.AgentBasicInfoPO;
import com.chinatelecom.gs.engine.robot.manage.info.domain.po.AgentConfigPO;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.sdk.config.ChannelOffSiteSwitchParam;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

class OffSiteChannelSwitchCacheServiceTest {

    @InjectMocks
    private OffSiteChannelSwitchCacheService service;

    @Mock
    private RedisKey redisKey;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private AgentBasicConfigService agentBasicConfigService;

    @Mock
    private AgentConfigService agentConfigService;

    @Mock
    private ChannelInfoRepository channelInfoRepository;

    @Mock
    private AgentBasicInfoService agentBasicInfoService;

    @Mock
    private RBucket<Object> rBucket;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // TC01: agentEditVersion == 1
    @Test
    void testChannelOpen_VersionIsOne_ReturnsFalse() {
        String agentCode = "A001";
        String channelId = "C001";

        when(agentBasicConfigService.getAgentEditVersion(agentCode)).thenReturn(1L);

        boolean result = service.channelOpen(agentCode, channelId);
        assertFalse(result);
    }

    // TC03: channel not enabled
    // TC03: channel not enabled
    @Test
    void testChannelOpen_ChannelNotEnabled_ReturnsFalse_withRealLogic() {
        String agentCode = "A001";
        long version = 2L;
        String channelId = "C001";

        // 模拟 agentEditVersion
        when(agentBasicConfigService.getAgentEditVersion(agentCode)).thenReturn(version);

        // 模拟 offsiteOffSwitch 内部逻辑
        String key = "key";
        when(redisKey.getOffSiteSwitchKey(agentCode, version)).thenReturn(key);

        @SuppressWarnings("unchecked")
        RBucket<Object> bucket = mock(RBucket.class);
        when(bucket.get()).thenReturn(null); // Redis 中无缓存
        when(redissonClient.getBucket(key)).thenReturn(bucket);

        // 添加这行来模拟 agentBasicInfoService.queryByAgentCodeAndVersion 方法
        AgentBasicInfoPO agentBasicInfoPO = new AgentBasicInfoPO();
        agentBasicInfoPO.setTemplateCategoryId("some-category");
        when(agentBasicInfoService.queryByAgentCodeAndVersion(agentCode, version)).thenReturn(agentBasicInfoPO);

        // 模拟 DB 查询结果为空
        when(agentConfigService.queryConfigByKey(
                eq(agentCode),
                eq(AgentConfigEnum.BOT_CONFIG_OFF_SITE_DEPLOYMENT_SWITCH.getCode()),
                eq(version), eq(false)
        )).thenReturn(null);

        // offsiteOffSwitch 返回 false
        boolean switchResult = service.offsiteOffSwitch(agentCode, version);
        assertFalse(switchResult);

        // channelOpen 应该返回 false
        ChannelInfoDTO dto = new ChannelInfoDTO();
        dto.setEnable(false);
        when(channelInfoRepository.getChannelInfo(channelId, agentCode)).thenReturn(dto);

        boolean result = service.channelOpen(agentCode, channelId);
        assertFalse(result);
    }



    // TC04: all conditions met
    @Test
    void testChannelOpen_AllConditionsMet_ReturnsTrue() {
        String agentCode = "A001";
        String channelId = "C001";
        long version = 2L;

        String key = "key";
        when(redisKey.getOffSiteSwitchKey(agentCode, version)).thenReturn(key);

        // 直接使用 RBucket<Boolean>
        @SuppressWarnings("unchecked")
        RBucket<Object> booleanRBucket = mock(RBucket.class);
        when(redissonClient.getBucket(key)).thenReturn(booleanRBucket);

        when(booleanRBucket.get()).thenReturn(true);
        when(agentBasicConfigService.getAgentEditVersion(agentCode)).thenReturn(2L);
        when(service.offsiteOffSwitch(agentCode, 2L)).thenReturn(true);

        ChannelInfoDTO dto = new ChannelInfoDTO();
        dto.setEnable(true);
        when(channelInfoRepository.getChannelInfo(channelId, agentCode)).thenReturn(dto);

        boolean result = service.channelOpen(agentCode, channelId);
        assertTrue(result);
    }

    // TC05: Redis has value true
    @Test
    void testOffsiteOffSwitch_RedisHasValue_ReturnsTrue() {
        String agentCode = "A001";
        long version = 2L;

        String key = "key";
        when(redisKey.getOffSiteSwitchKey(agentCode, version)).thenReturn(key);

        // 直接使用 RBucket<Boolean>
        @SuppressWarnings("unchecked")
        RBucket<Object> booleanRBucket = mock(RBucket.class);
        when(redissonClient.getBucket(key)).thenReturn(booleanRBucket);

        when(booleanRBucket.get()).thenReturn(true);

        boolean result = service.offsiteOffSwitch(agentCode, version);
        assertTrue(result);
        verify(booleanRBucket, never()).set(anyBoolean(), anyLong(), any(TimeUnit.class));
    }


    // TC06: Redis no value, DB no config
    @Test
    void testOffsiteOffSwitch_NoRedisNoDBConfig_ReturnsFalse() {
        String agentCode = "A001";
        long version = 2L;

        String key = "key";
        when(redisKey.getOffSiteSwitchKey(agentCode, version)).thenReturn(key);
        when(redissonClient.getBucket(key)).thenReturn(rBucket);
        when(rBucket.get()).thenReturn(null);

        // 添加这行来模拟 agentBasicInfoService.queryByAgentCodeAndVersion 方法
        AgentBasicInfoPO agentBasicInfoPO = new AgentBasicInfoPO();
        agentBasicInfoPO.setTemplateCategoryId("some-category"); // 设置一个非SYSTEM的category ID
        when(agentBasicInfoService.queryByAgentCodeAndVersion(agentCode, version)).thenReturn(agentBasicInfoPO);

        when(agentConfigService.queryConfigByKey(agentCode, AgentConfigEnum.BOT_CONFIG_OFF_SITE_DEPLOYMENT_SWITCH.getCode(), version, false))
                .thenReturn(null);

        boolean result = service.offsiteOffSwitch(agentCode, version);
        assertFalse(result);
        verify(rBucket).set(false, 1, TimeUnit.MINUTES);
    }

    // TC07: Redis no value, DB has config but parse failed
    @Test
    void testOffsiteOffSwitch_ParseFailed_ReturnsFalse() {
        String agentCode = "A001";
        long version = 2L;

        String key = "key";
        when(redisKey.getOffSiteSwitchKey(agentCode, version)).thenReturn(key);
        when(redissonClient.getBucket(key)).thenReturn(rBucket);
        when(rBucket.get()).thenReturn(null);

        // 添加这行来模拟 agentBasicInfoService.queryByAgentCodeAndVersion 方法
        AgentBasicInfoPO agentBasicInfoPO = new AgentBasicInfoPO();
        agentBasicInfoPO.setTemplateCategoryId("some-category");
        when(agentBasicInfoService.queryByAgentCodeAndVersion(agentCode, version)).thenReturn(agentBasicInfoPO);

        AgentConfigPO po = new AgentConfigPO();
        po.setConfigValue("invalid-json");
        when(agentConfigService.queryConfigByKey(anyString(), anyString(), anyLong(), eq(false))).thenReturn(po);

        boolean result = service.offsiteOffSwitch(agentCode, version);
        assertFalse(result);
    }


    // TC08: Redis no value, DB has config and open is true
    @Test
    void testOffsiteOffSwitch_ConfigOpenTrue_ReturnsTrue() {
        String agentCode = "A001";
        long version = 2L;

        String key = "key";
        when(redisKey.getOffSiteSwitchKey(agentCode, version)).thenReturn(key);
        when(redissonClient.getBucket(key)).thenReturn(rBucket);
        when(rBucket.get()).thenReturn(null);

        // 添加这行来模拟 agentBasicInfoService.queryByAgentCodeAndVersion 方法
        AgentBasicInfoPO agentBasicInfoPO = new AgentBasicInfoPO();
        agentBasicInfoPO.setTemplateCategoryId("some-category");
        when(agentBasicInfoService.queryByAgentCodeAndVersion(agentCode, version)).thenReturn(agentBasicInfoPO);

        ChannelOffSiteSwitchParam param = new ChannelOffSiteSwitchParam(true);
        String json = JSONObject.toJSONString(param);

        AgentConfigPO po = new AgentConfigPO();
        po.setConfigValue(json);
        when(agentConfigService.queryConfigByKey(anyString(), anyString(), anyLong(), eq(false))).thenReturn(po);

        boolean result = service.offsiteOffSwitch(agentCode, version);
        assertTrue(result);
        verify(rBucket).set(true, 1, TimeUnit.MINUTES);
    }

    // TC11: DB no config, init success
    @Test
    void testUpdateOffSiteSwitch_NoConfig_InitSuccess_ReturnsTrue() {
        String agentCode = "A001";
        long version = 2L;

        String key = "key";
        when(redisKey.getOffSiteSwitchKey(agentCode, version)).thenReturn(key);

        @SuppressWarnings("unchecked")
        RBucket<Object> bucket = mock(RBucket.class);
        when(redissonClient.getBucket(key)).thenReturn(bucket);

        // 添加这行来模拟 agentBasicInfoService.queryByAgentCodeAndVersion 方法
        AgentBasicInfoPO agentBasicInfoPO = new AgentBasicInfoPO();
        agentBasicInfoPO.setTemplateCategoryId("some-category");
        when(agentBasicInfoService.queryByAgentCodeAndVersion(agentCode, version)).thenReturn(agentBasicInfoPO);

        // 修复：对所有参数使用匹配器，包括第四个参数
        when(agentConfigService.queryConfigByKey(anyString(), anyString(), anyLong(), eq(false)))
                .thenReturn(null)
                .thenAnswer(invocation -> {
                    AgentConfigPO po = new AgentConfigPO();
                    po.setConfigValue(JsonUtils.toJsonString(new ChannelOffSiteSwitchParam(false)));
                    return po;
                });

        // 添加这一行来 mock updateById
        when(agentConfigService.updateById(any(AgentConfigPO.class))).thenReturn(true);

        boolean result = service.updateOffSiteSwitch(agentCode, true, version);
        assertTrue(result); // 现在应该成功

        verify(bucket).delete(); // 验证 Redis 缓存被删除
    }


}
