package com.chinatelecom.gs.engine.common.utils;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.kms.sdk.enums.RecallType;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * @USER: pengmc1
 * @DATE: 2025/4/15 17:21
 */

@Component
public class RankExpressionHolder {

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    private final Map<RecallType, String> rankExpressionMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        gsGlobalConfig.getSearch().getRankExpression().forEach((recallType, expression) -> {
            rankExpressionMap.put(recallType, expression);
        });
    }

    /**
     * 根据recallType获取rankExpression
     *
     * @param recallType
     * @return
     */
    public String getRankExpression(RecallType recallType) {
        if(Objects.isNull(recallType)){
            return null;
        }
        return rankExpressionMap.get(recallType);
    }
}
