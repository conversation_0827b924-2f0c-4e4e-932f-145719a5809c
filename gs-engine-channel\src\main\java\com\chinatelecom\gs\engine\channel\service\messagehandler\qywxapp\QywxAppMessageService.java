package com.chinatelecom.gs.engine.channel.service.messagehandler.qywxapp;

import com.chinatelecom.gs.engine.channel.common.SessionIdHolder;
import com.chinatelecom.gs.engine.channel.common.UidUtils;
import com.chinatelecom.gs.engine.channel.common.cache.localcache.ChannelCaches;
import com.chinatelecom.gs.engine.channel.common.enums.MessageDirectionEnum;
import com.chinatelecom.gs.engine.channel.common.enums.QywxMessageTypeEnum;
import com.chinatelecom.gs.engine.channel.common.utils.XmlUtil;
import com.chinatelecom.gs.engine.channel.service.dto.*;
import com.chinatelecom.gs.engine.channel.service.messagehandler.MessageRecordService;
import com.chinatelecom.gs.engine.channel.service.messagehandler.qywxapp.encrypt.WXBizMsgCrypt;
import com.chinatelecom.gs.engine.common.cache.RedisService;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.chinatelecom.gs.engine.channel.common.constants.ChatConstants.DEFAULT_ANSWER_CONTENT;
import static com.chinatelecom.gs.engine.channel.common.constants.ChatConstants.QYWX_RESPONSE_FAILED;

/**
 * <AUTHOR>
 * @date 2023/12/22 15:29
 * @description 企业微信应用消息处理
 */
@Slf4j
@Service
public class QywxAppMessageService {

    @Resource
    private ChannelCaches caches;

    @Resource
    private SessionIdHolder sessionIdHolder;

    @Resource
    private MessageRecordService messageRecordService;

    @Autowired(required = false)
    private RedisService redisService;

    @Resource
    private QywxMessageSendService qywxMessageSendService;


    private static final String MESSAGE_CACHE_KEY = "csrobot_channel_message_%s";

    private static final String USER_CACHE_KEY = "csrobot_channel_user_%s";

    private static final String EXCLUDE_NODE = "<Event>";


    /**
     * 企业微信应用消息通道握手
     *
     * @param channelId
     * @param msgSignature
     * @param timestamp
     * @param nonce
     * @param echoStr
     * @return
     */
    public String handshake(String channelId, String msgSignature, String timestamp, String nonce, String echoStr) {
        try {
            WXBizMsgCrypt msgCrypt = caches.getWxBizMsgCrypt(channelId);
            return msgCrypt.verifyURL(msgSignature, timestamp, nonce, echoStr);
        } catch (Exception e) {
            log.error("解密企业微信应用握手消息异常 {}", e.getMessage());
            return QYWX_RESPONSE_FAILED;
        }
    }

    /**
     * 异步推送消息
     */
    public String asyncChat(QywxRequestMessage message) {
        log.info("收到企业微信消息:{}", JsonUtils.toJsonString(message));
        String channelId = message.getChannelId();
        String msgSignature = message.getMsgSignature();
        String timestamp = message.getTimestamp();
        String nonce = message.getNonce();
        String qwMessage = message.getQwMessage();
        QywxConfigDTO config = caches.getWxCryptConfig(channelId);
        String userId = "";
        try {
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(config.getToken(), config.getEncodingAESKey(),
                    config.getCorpId());
            String msg = wxcpt.decryptMsg(msgSignature, timestamp, nonce, qwMessage);
            if (msg.contains(EXCLUDE_NODE)) {
                log.warn("不支持的节点类型");
                return "";
            }
            QywxAppMessage msgDTO = XmlUtil.readObj(msg, QywxAppMessage.class);
            if (!QywxMessageTypeEnum.TEXT.getCode().equals(msgDTO.getMsgType())) {
                log.warn("不支持的消息类型：{}", msgDTO.getMsgType());
                return "";
            }
            if (StringUtils.isNotBlank(redisService.get(MESSAGE_CACHE_KEY.formatted(msgDTO.getMsgId())))) {
                log.warn("消息id重复：{}", msgDTO.getMsgId());
                return "";
            }
            redisService.setEx(MESSAGE_CACHE_KEY.formatted(msgDTO.getMsgId()), msgDTO.getMsgId(), 1, TimeUnit.MINUTES);

            if (StringUtils.isNotBlank(redisService.get(USER_CACHE_KEY.formatted(msgDTO.getFromUserName())))) {
                log.warn("用户消息超频：{}", msgDTO.getFromUserName());
                QywxAppTextResponseMessage responseMessage = new QywxAppTextResponseMessage();
                String responseTime = String.valueOf(System.currentTimeMillis() / 1000);
                responseMessage.setCreateTime(responseTime);
                responseMessage.setMsgType(QywxMessageTypeEnum.TEXT.getCode());
                responseMessage.setFromUserName(config.getCorpId());
                responseMessage.setContent("您消息发送太频繁了，喝口茶休息一下再来");
                responseMessage.setToUserName(msgDTO.getFromUserName());
                WXBizMsgCrypt msgCrypt = caches.getWxBizMsgCrypt(channelId);
                return msgCrypt.encryptMsg(XmlUtil.xmlToString(responseMessage), responseTime, nonce);
            }
            redisService.setEx(USER_CACHE_KEY.formatted(msgDTO.getFromUserName()), msgDTO.getFromUserName(), 5, TimeUnit.SECONDS);

            String content = msgDTO.getContent();
            userId = msgDTO.getFromUserName();
            //异步回复消息
            qywxMessageSendService.asyncReply(channelId, userId, content, config.getAgentId(), config.getCorpId());
        } catch (Exception e) {
            log.error("解析企业微信消息错误", e);
        }
        return "";
    }


    /**
     * 同步回复消息
     *
     * @param channelId    渠道ID
     * @param qwMessage    消息体
     * @param msgSignature 消息签名
     * @param timestamp    时间戳
     * @param nonce        随机数
     * @return 返回加密消息
     */
    public String chat(String channelId, String qwMessage, String msgSignature, String timestamp, String nonce) {
        long start = System.currentTimeMillis();
        QywxConfigDTO config = caches.getWxCryptConfig(channelId);
        WXBizMsgCrypt msgCrypt = caches.getWxBizMsgCrypt(channelId);
        RobotConfigDTO robotConfig = caches.getRobotConfig(channelId);

        QywxAppTextResponseMessage responseMessage = new QywxAppTextResponseMessage();
        String responseTime = String.valueOf(System.currentTimeMillis() / 1000);
        responseMessage.setCreateTime(responseTime);
        responseMessage.setMsgType(QywxMessageTypeEnum.TEXT.getCode());
        responseMessage.setFromUserName(config.getCorpId());
        try {

            String message = msgCrypt.decryptMsg(msgSignature, timestamp, nonce, qwMessage);
            QywxAppMessageDetail messageDetail = XmlUtil.readObj(message, QywxAppMessageDetail.class);
            log.info("【企业微信应用消息】{}", messageDetail);
            String sessionId = sessionIdHolder.getSessionId(channelId, messageDetail.getFromUserName());
            String messageId = UidUtils.randomString();
            String userId = messageDetail.getFromUserName();
            ChannelMsgRecordDTO userRecordDTO = ChannelMsgRecordDTO.builder()
                    .channelId(channelId)
                    .robotCode(robotConfig.getBotCode())
                    .sessionId(sessionId)
                    .userId(userId)
                    .messageId(messageId)
                    .message(message)
                    .messageType(QywxMessageTypeEnum.TEXT.getCode())
                    .msgDirection(MessageDirectionEnum.USER)
                    .build();
            userRecordDTO.setTenantId(robotConfig.getTenantId());
            messageRecordService.recordMessage(userRecordDTO);
            String answerText = "同步接口相应结果";
            log.info("机器人平台响应：{}", answerText);
            ChannelMsgRecordDTO botRecordDTO = ChannelMsgRecordDTO.builder()
                    .channelId(channelId)
                    .robotCode(robotConfig.getBotCode())
                    .sessionId(sessionId)
                    .userId(userId)
                    .messageId(messageId)
                    .message(answerText)
                    .messageType(QywxMessageTypeEnum.TEXT.getCode())
                    .msgDirection(MessageDirectionEnum.BOT)
                    .build();
            botRecordDTO.setTenantId(robotConfig.getTenantId());
            messageRecordService.recordMessage(botRecordDTO);

            responseMessage.setContent(answerText);
            responseMessage.setToUserName(messageDetail.getFromUserName());
        } catch (Exception e) {
            log.error("企业微信应用消息聊天失败 {}", e.getMessage());
            responseMessage.setContent(DEFAULT_ANSWER_CONTENT);
        }

        try {
            return msgCrypt.encryptMsg(XmlUtil.xmlToString(responseMessage), responseTime, nonce);
        } catch (Exception e) {
            log.error("反序列化企业微信应用消息xml对象异常 {}", e.getMessage());
            return QYWX_RESPONSE_FAILED;
        } finally {
            log.info("【企业微信调用机器人时间开销】{} ms", System.currentTimeMillis() - start);
        }
    }

}
