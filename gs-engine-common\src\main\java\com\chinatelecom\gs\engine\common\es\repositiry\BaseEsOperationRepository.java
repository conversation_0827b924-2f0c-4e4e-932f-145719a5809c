package com.chinatelecom.gs.engine.common.es.repositiry;

import com.chinatelecom.gs.engine.common.es.repositiry.BaseEsRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.ByQueryResponse;
import org.springframework.data.elasticsearch.core.query.Query;

import java.util.List;
import java.util.Map;

public interface BaseEsOperationRepository<DTO, PO, ID> extends BaseEsRepository<DTO, PO, ID> {

    /**
     * 创建索引
     *
     * @param index
     *         索引名称
     */
    boolean createIndex(String index);

    /**
     * 删除索引
     *
     * @param index
     *         索引名称
     */
    boolean deleteIndex(String index);

    boolean indexExist(String index);

    /**
     * 单个新增或更新
     *
     * @param index
     *         索引名称
     * @param dtoClass
     *         更新数据
     */
    void saveOrUpdate(String index, DTO dtoClass);

    /**
     * 批量保存或更新，根据ID判断如果文档不存在就创建，存在就覆盖
     *
     * @param index
     *         索引名称
     * @param dataList
     *         批量数据
     */
    void bulkSaveOrUpdate(String index, List<DTO> dataList);

    /**
     * 批量保存或更新，根据ID判断如果文档不存在就创建，存在就覆盖
     *
     * @param index
     *         索引名称
     * @param dataList
     *         批量数据
     * @param idKey
     *         Map中数据ID的key
     */
    void bulkSaveOrUpdate(String index, List<Map<String, Object>> dataList, String idKey);

    /**
     * 批量更新数据，如果ID不存在会报错，存在就部分更新（只覆盖有数据的字段）
     *
     * @param index
     *         索引名称
     * @param dataList
     *         批量数据
     */
    void bulkUpdate(String index, List<DTO> dataList);

    /**
     * 批量更新数据，如果ID不存在会报错，存在就部分更新（只覆盖有数据的字段）
     *
     * @param index
     *         索引名称
     * @param dataList
     *         批量数据
     * @param idKey
     *         Map中数据ID的key
     */
    void bulkUpdate(String index, List<Map<String, Object>> dataList, String idKey);

//    /**
//     * 搜索
//     *
//     * @param index
//     *         索引名称
//     * @param queryBuilder
//     *         匹配query
//     */
//    SearchHits<PO> search(String index, QueryBuilder queryBuilder);

//    /**
//     * 搜索
//     *
//     * @param index
//     *         索引名称
//     * @param queryBuilder
//     *         匹配QueryBuilder
//     * @param pageable
//     *         分页
//     */
//    SearchHits<PO> search(String index, QueryBuilder queryBuilder, Pageable pageable);

    /**
     * 搜索
     *
     * @param index
     *         索引名称
     * @param query
     *         搜索query
     */
    SearchHits<PO> search(String index, Query query);

    SearchHit<PO> searchOne(String index, Query query);

    /**
     * 删除 删除索引数据
     *
     * @param index
     *         索引名称
     * @param query
     *         查询条件
     *
     * @return 操作结果
     */
    ByQueryResponse delete(String index, Query query);
}
