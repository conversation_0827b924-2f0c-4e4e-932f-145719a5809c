package com.chinatelecom.gs.engine.channel.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/25 15:30
 * @description
 */
@Data
@Schema(description = "企业微信应用消息渠道配置类")
public class QywxConfigVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5570679560120805490L;

    @Schema(description = "应用ID")
    @NotNull(message = "应用ID不能为空")
    private Integer agentId;

    @Schema(description = "应用secret")
    @NotBlank(message = "应用secret不能为空")
    private String secret;

    @Schema(description = "企业ID")
    @NotBlank(message = "企业ID不能为空")
    private String corpId;

    @Schema(description = "企业微信token")
    private String token;

    @Schema(description = "aes key")
    private String encodingAESKey;

    @Schema(description = "应用名称")
    private String appName;

    @Schema(description = "用户名称")
    private String updateName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
