package com.chinatelecom.gs.engine.channel.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/24 11:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageThumbReq implements Serializable {
    /**
     * 机器人编码
     */
    @Schema(description = "机器人编码")
    @NotBlank(message = "机器人编码不能为空！")
    private String agentCode;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    //@NotBlank(message = "用户唯一标识不能为空！")
    private String userId;


    /**
     * 请求时间
     */
    @Schema(description = "请求时间")
    //@NotNull(message = "请求时间不能为空！")
    private Long requestTime;

    /**
     * 消息id
     */
    @Schema(description = "消息id")
    @NotEmpty(message = "消息id不能为空")
    private String messageId;

    /**
     * 点赞：up
     * 点踩：down
     */
    @Schema(description = "点赞:up/点踩:down")
    @NotEmpty(message = "点赞点踩类型不能为空")
    private String thumbType;

    /**
     * 回复准确性描述
     */
    @Schema(description = "详情")
    @Size(max = 2000,message = "详情长度必须在[0,2000]范围内")
    private List<String> detail;

    /**
     * 其他意见
     */
    @Schema(description = "其他意见")
    @Size(max = 5000,message = "其他意见长度必须在[0,5000]范围内")
    private String remark;

    @Schema(description = "请求内容")
    private String content;
}
