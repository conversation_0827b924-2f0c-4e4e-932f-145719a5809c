package com.chinatelecom.gs.engine.core.entity.ability.validator.impl;

import com.chinatelecom.gs.engine.core.entity.domain.request.EntityValidatorRequest;
import com.chinatelecom.gs.engine.core.sdk.enums.EntityValidatorEnum;
import com.chinatelecom.gs.engine.core.entity.ability.validator.AbstractEntityValidatorAbilityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * 基于规则的实体校验
 * @USER: pengmc1
 * @DATE: 2025/1/21 8:46
 */

@Slf4j
@Service
public class RegexEntityValidatorAbilityService extends AbstractEntityValidatorAbilityService {
    /**
     * 校验编码
     *
     * @return
     */
    @Override
    public String validatorCode() {
        return EntityValidatorEnum.REGEX_VALIDATOR.getCode();
    }

    /**
     * 执行实体校验
     * @param request
     * @return
     */
    @Override
    protected Boolean doValidator(EntityValidatorRequest request) {
        String validatorRegexp = request.getEntityVO().getValidatorRegexp();
        Pattern pattern = Pattern.compile(validatorRegexp);
        String entityValue = request.getEntity().getEntityContents().get(0).getValue();
        log.info("【实体校验】【{}】实体校验表达式：{},校验值：{}", validatorCode(), validatorRegexp, entityValue);
        return pattern.matcher(entityValue).matches();
    }
}
