package com.chinatelecom.gs.engine.robot.dialog.execute.service.action.engine.inner.tool;

import com.chinatelecom.gs.engine.robot.manage.info.domain.dto.StrategyNodeSetting;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntentAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.Action;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.ActionMessage;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.ActionStateEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.ActionTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Slf4j
public abstract class AbstractToolSourceEngineService implements ToolSourceEngine {

   public List<ToolIntentAnswer> getSummaryContents(String query, DagContext context, StrategyNodeSetting strategyNodeSetting){
       this.preRecall(context);
       LocalDateTime startTime = LocalDateTime.now();
       List<ToolIntentAnswer> toolIntentAnswers = doGetSummaryContents(query, context, strategyNodeSetting);
       LocalDateTime endTime = LocalDateTime.now();
       this.postRecall(context, startTime, endTime);
       return toolIntentAnswers;
   }

   protected List<ToolIntentAnswer> doGetSummaryContents(String query, DagContext context, StrategyNodeSetting strategyNodeSetting){
       return Collections.emptyList();
   }

    public List<ToolIntentAnswer> getRecallContents(String query, DagContext context, StrategyNodeSetting strategyNodeSetting) {
        this.preRecall(context);
        LocalDateTime startTime = LocalDateTime.now();
        List<ToolIntentAnswer> toolIntentAnswers = doGetRecallContents(query, context, strategyNodeSetting);
        LocalDateTime endTime = LocalDateTime.now();
        this.postRecall(context, startTime, endTime);
        return toolIntentAnswers;
    }

    protected List<ToolIntentAnswer> doGetRecallContents(String query, DagContext context, StrategyNodeSetting strategyNodeSetting) {
        return Collections.emptyList();
    }

    private void preRecall(DagContext context) {
        if (getActionType() == null) {
            return;
        }
        if (context.getActionCallback() == null) {
            return;
        }
        context.getActionCallback().onStart(buildStartActions(context));
    }

    private void postRecall(DagContext context, LocalDateTime startTime, LocalDateTime endTime) {
        if (getActionType() == null) {
            return;
        }
        if (context.getActionCallback() == null) {
            return;
        }
        context.getActionCallback().onComplete(buildCompleteActions(context, startTime, endTime));
    }

    private ActionMessage buildStartActions(DagContext context) {
        ActionTypeEnum actionType = getActionType();
        if (actionType == null) {
            return null;
        }
        Action action = Action.builder()
                .type(actionType.getCode())
                .code(context.getDownMessageId() + "_" + actionType.getCode())
                .name(actionType.getDesc())
                .state(ActionStateEnum.START.getCode())
                .build();
        return ActionMessage.builder().actions(Lists.newArrayList(action)).messageId(context.getDownMessageId()).build();
    }

    private ActionMessage buildCompleteActions(DagContext context, LocalDateTime startTime, LocalDateTime endTime) {
        ActionTypeEnum actionType = getActionType();
        if (actionType == null) {
            return null;
        }
        Action action = Action.builder()
                .type(actionType.getCode())
                .code(context.getDownMessageId() + "_" + actionType.getCode())
                .name(actionType.getDesc())
                .state(ActionStateEnum.FINISH.getCode())
                .cost(getCost(startTime, endTime))
                .build();
        return ActionMessage.builder().actions(Lists.newArrayList(action)).messageId(context.getDownMessageId()).build();
    }

    private String getCost(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return StringUtils.EMPTY;
        }
        return "%.3f".formatted(Duration.between(startTime, endTime).toNanos() / 1e9);
    }

    private ActionTypeEnum getActionType() {
        if (DialogEngineType.KMS.getCode().equals(getName())) {
            return ActionTypeEnum.KNOWLEDGE_RETRIEVAL;
        }
        return null;
    }
}
