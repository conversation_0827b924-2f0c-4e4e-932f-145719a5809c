INSERT INTO `agent_default_config` (`id`, `config_key`, `config_value`, `type`, `category`, `yn`, `create_id`, `create_name`, `update_id`, `update_name`, `create_time`, `update_time`, `version`, `tenant_id`) VALUES (NULL, 'agent_kms_auth_config', '{"kmsPublicChatSwitch":true, "kmsPublicViewSwitch":true}', 1, 1, 0, '1', '', '1', '', NOW(), NOW(), 1, '');

alter table agent_knowledge_base add column auth_config varchar(255) default '{"kmsPublicChatSwitch":true, "kmsPublicViewSwitch":true}';
