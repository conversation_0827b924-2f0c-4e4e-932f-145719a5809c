package com.chinatelecom.gs.engine.config.filter.interceptor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelecom.gs.engine.config.filter.BaseHandlerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.net.URL;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 防止跨站攻击 csrf
 * @date 2024年06月12日
 */
@Slf4j
@RefreshScope
@Component
public class CsrfInterceptor implements BaseHandlerInterceptor {

    /**
     * 开启防止跨站攻击
     */
    @Value("${platform.enableCsrfCheck:false}")
    private Boolean enableCsrfCheck;

    @Value("#{'${platform.trustedDomain}'.split(';')}")
    private List<String> trustedDomains;

    @Override
    public boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest req = (HttpServletRequest) request;
        String referer = req.getHeader("Referer");
        if (CharSequenceUtil.isBlank(referer)) {
            referer = req.getParameter("Referer");
        }
        String host = CharSequenceUtil.EMPTY;
        log.debug("CsrfInterceptor preHandle referer [{}] request url [{}]", referer, req.getRequestURL());
        if (CharSequenceUtil.isNotBlank(referer)) {
            URL refererUrl = new URL(referer);
            host = refererUrl.getHost();
        }
        boolean checkHost = Boolean.FALSE.equals(enableCsrfCheck)
                || (CharSequenceUtil.isNotBlank(host) && CollUtil.isNotEmpty(trustedDomains) && trustedDomains.contains(host));
        if (checkHost) {
            return true;
        }
        return InterceptorUtils.writeError((HttpServletResponse) response, "A0078", "csrf 异常");
    }
}
