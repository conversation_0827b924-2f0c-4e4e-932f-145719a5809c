package com.chinatelecom.gs.engine.channel.service.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 图片消息
 *
 * <AUTHOR>
 * @date 2024/1/16 14:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImageMessageDTO extends BaseSendMessageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8007765935603215785L;

    private JSONObject image;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
