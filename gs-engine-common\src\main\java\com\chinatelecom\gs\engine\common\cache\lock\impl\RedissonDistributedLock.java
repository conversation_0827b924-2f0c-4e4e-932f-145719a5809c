package com.chinatelecom.gs.engine.common.cache.lock.impl;


import com.chinatelecom.gs.engine.common.cache.lock.DistributedLock;
import com.chinatelecom.gs.engine.common.cache.lock.ZLock;
import com.chinatelecom.gs.engine.common.cache.lock.exception.LockException;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.utils.RedisPrefixUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * redisson分布式锁实现，基本锁功能的抽象实现
 * 本接口能满足绝大部分的需求，高级的锁功能，请自行扩展或直接使用原生api
 */
@SuppressWarnings("all")
public class RedissonDistributedLock implements DistributedLock {
    @Autowired
    private RedissonClient redisson;


    @Value("${spring.redis.lock.prefix:gs:lock}")
    private String lockKeyPrefix;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    private ZLock getLock(String key, boolean isFair) {
        RLock lock;
        String rKey = RedisPrefixUtils.buildByPrefix(lockKeyPrefix, ":", "key");
        if (isFair) {
            lock = redisson.getFairLock(rKey);
        } else {
            lock = redisson.getLock(rKey);
        }
        return new ZLock(key, lock, this);
    }

    @Override
    public ZLock lock(String key, long leaseTime, TimeUnit unit, boolean isFair) {
        ZLock zLock = getLock(key, isFair);
        RLock lock = (RLock)zLock.getLock();
        lock.lock(leaseTime, unit);
        return zLock;
    }

    @Override
    public ZLock tryLock(String key, long waitTime, long leaseTime, TimeUnit unit, boolean isFair) throws InterruptedException {
        ZLock zLock = getLock(key, isFair);
        RLock lock = (RLock)zLock.getLock();
        if (lock.tryLock(waitTime, leaseTime, unit)) {
            return zLock;
        }
        return null;
    }

    @Override
    public void unlock(ZLock zlock) {
        if (zlock != null) {
            Object lock = zlock.getLock();
            if (lock instanceof RLock rLock) {
                if (rLock.isLocked()) {
                    rLock.unlock();
                }
            } else {
                throw new LockException("requires RLock type");
            }
        }
    }
}
