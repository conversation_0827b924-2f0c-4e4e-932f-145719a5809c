package com.chinatelecom.gs.engine.kms.util;

import com.chinatelecom.gs.engine.kms.sdk.enums.PermissionEnum;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年06月18日
 */
public class PrivilegeEnumUtils {

    private PrivilegeEnumUtils() {
    }

    /**
     * 转换权限类型
     * @param privilegeEnum
     * @return
     */
    public static PermissionEnum convertPermission(PrivilegeEnum privilegeEnum) {
        if (privilegeEnum != null) {
            switch (privilegeEnum) {
                case no_permission:
                    return PermissionEnum.no_permission;
                case view:
                    return PermissionEnum.view;
                case edit:
                    return PermissionEnum.edit;
                case manage:
                    return PermissionEnum.manage;
            }
        }
        return PermissionEnum.no_permission;
    }

    /**
     * 转换权限类型
     * @param privilegeEnum
     * @return
     */
    public static PrivilegeEnum convertPrivilege(PermissionEnum privilegeEnum) {
        if (privilegeEnum == null) {
            return null;
        }

        switch (privilegeEnum) {
            case no_permission:
                return PrivilegeEnum.no_permission;
            case view:
                return PrivilegeEnum.view;
            case edit:
                return PrivilegeEnum.edit;
            case manage:
                return PrivilegeEnum.manage;
        }
        return PrivilegeEnum.no_permission;
    }

}
