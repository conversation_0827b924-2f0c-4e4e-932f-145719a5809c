package com.chinatelecom.gs.engine.common.s3;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月30日
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileMetadataRes {

    /**
     * 文件大小
     * 单位:bytes
     */
    private long fileLength;


    public double fileLength2KB() {
        return fileLength / 1024;
    }

    public double fileLength2MB() {
        return fileLength / (1024 * 1024);
    }

}
