package com.chinatelecom.gs.engine.core.corekit.common.core;

import com.chinatelecom.gs.engine.task.sdk.config.WorkflowExecConfig;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service
public class RequestNodeCache {

    private final RedisKey redisKey;

    private final RedissonClient redissonClient;

    @Value("${spring.cloud.client.ip-address:default}")
    private String host;

    @Resource
    private WorkflowExecConfig workflowExecConfig;

    public RequestNodeCache(RedisKey redisKey, RedissonClient redissonClient){
        this.redisKey = redisKey;
        this.redissonClient = redissonClient;
    }

    public void cache(String upId){
        log.debug("cache:{}", upId);
        RBucket<String> hostBucket = getHostBucket(upId);
        hostBucket.set(host, workflowExecConfig.getWorkflowExecTimeout(), TimeUnit.MILLISECONDS);
    }

    private RBucket<String> getHostBucket(String upId){
        String telechatMessageKey = redisKey.getChatMessageKey(upId);
        return redissonClient.getBucket(telechatMessageKey, StringCodec.INSTANCE);
    }

    public String host(String upId){
        log.debug("host:{}", upId);
        RBucket<String> hostBucket = getHostBucket(upId);
        return hostBucket.get();
    }

    public void invalidate(String upId){
        log.info("del:{}", upId);
        RBucket<String> hostBucket = getHostBucket(upId);
        hostBucket.delete();
    }

}
