package com.chinatelecom.gs.engine.core.manager.search.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.platform.StatOpenApi;
import com.chinatelecom.gs.engine.core.manager.param.CodeParam;
import com.chinatelecom.gs.engine.core.manager.search.SearchAnalyzerConfigService;
import com.chinatelecom.gs.engine.core.manager.search.dto.SearchAnalyzerConfigCreateParam;
import com.chinatelecom.gs.engine.core.manager.search.dto.SearchAnalyzerConfigDTO;
import com.chinatelecom.gs.engine.core.manager.search.dto.SearchAnalyzerConfigQueryParam;
import com.chinatelecom.gs.engine.core.manager.search.vo.SearchAnalyzerConfigVO;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;


@RestController
@Slf4j
@Tag(name = "搜索分析器配置管理")
@HideFromApiTypes(ApiType.OPENAPI)
@RequestMapping({Apis.BASE_PREFIX + Apis.WEB_API + Apis.SEARCH_ANALYZER_CONFIG,
        Apis.BASE_PREFIX + Apis.OPENAPI + Apis.SEARCH_ANALYZER_CONFIG,
        Apis.BASE_PREFIX + Apis.RPC_PREFIX + Apis.SEARCH_ANALYZER_CONFIG})
public class SearchAnalyzerConfigController {

    @Resource
    private SearchAnalyzerConfigService searchAnalyzerConfigService;

    @Operation(summary = "获取所有搜索分析器配置")
    @GetMapping(Apis.ALL)
    @AuditLog(businessType = "搜索分析器配置管理", operType = "获取所有搜索分析器配置", operDesc = "获取所有搜索分析器配置", objId="null")
    @StatOpenApi(name = "获取所有搜索分析器配置", groupName = "配置搜索分析器配置")
    public Result<List<SearchAnalyzerConfigDTO>> getAllConfigs() {
        return Result.success(searchAnalyzerConfigService.getAllConfigs());
    }

    @Operation(summary = "分页查询搜索分析器配置")
    @PostMapping(KmsApis.PAGE_API)
    @AuditLog(businessType = "搜索分析器配置管理", operType = "分页查询搜索分析器配置", operDesc = "分页查询搜索分析器配置", objId="null")
    @StatOpenApi(name = "分页查询搜索分析器配置", groupName = "配置搜索分析器配置")
    public Result<Page<SearchAnalyzerConfigVO>> pageQuery(@Validated @RequestBody SearchAnalyzerConfigQueryParam param) {
        return Result.success(searchAnalyzerConfigService.pageQuery(param));
    }

    @Operation(summary = "创建搜索分析器配置")
    @PostMapping
    @AuditLog(businessType = "搜索分析器配置管理", operType = "创建搜索分析器配置", operDesc = "创建搜索分析器配置", objId="#param.businessNo")
    @StatOpenApi(name = "创建搜索分析器配置", groupName = "配置搜索分析器配置")
    public Result<Boolean> createConfig(@Validated @RequestBody SearchAnalyzerConfigCreateParam param) {
        searchAnalyzerConfigService.saveConfig(param);
        return Result.success(true);
    }

    @Operation(summary = "更新搜索分析器配置")
    @PutMapping
    @AuditLog(businessType = "搜索分析器配置管理", operType = "更新搜索分析器配置", operDesc = "更新搜索分析器配置", objId="#param.businessNo")
    @StatOpenApi(name = "更新搜索分析器配置", groupName = "配置搜索分析器配置")
    public Result<Boolean> updateConfig(@Validated @RequestBody SearchAnalyzerConfigCreateParam config) {

        return Result.success(searchAnalyzerConfigService.updateConfig(config));
    }

    @Operation(summary = "移除搜索分析器配置")
    @PlatformRestApi(name = "移除搜索分析器配置", groupName = "配置搜索分析器配置")
    @StatOpenApi(name = "移除搜索分析器配置", groupName = "配置搜索分析器配置")
    @PostMapping(KmsApis.DELETE_API)
    @AuditLog(businessType = "搜索分析器配置管理", operType = "移除搜索分析器配置", operDesc = "移除搜索分析器配置", objId="null")
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
        return Result.success(searchAnalyzerConfigService.delete(codes));
    }
}
