package com.chinatelecom.gs.engine.common.s3;

import lombok.Data;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月30日
 */
@Data
public class ObjectMetadataRes {

//    String CONTENT_LENGTH = "Content-Length";
//    String CONTENT_RANGE = "Content-Range";
//
//    private String contentRange;
//
//    private long contentLength;


    private InputStream inputStream;

    private long firstBytePos;

    private long lastBytePos;

    private long entityLength;

    public String buildContentRange() {
        return "bytes %d-%d/%d".formatted(firstBytePos, lastBytePos, entityLength);
    }

    public long buildCurrentLength() {
        return lastBytePos - firstBytePos + 1;
    }

}
