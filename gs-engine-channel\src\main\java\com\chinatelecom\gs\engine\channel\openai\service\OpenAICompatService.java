package com.chinatelecom.gs.engine.channel.openai.service;

import com.chinatelecom.gs.engine.channel.openai.dto.ChatCompletionRequest;
import com.chinatelecom.gs.engine.channel.openai.dto.ChatCompletionResponse;
import com.chinatelecom.gs.engine.channel.openai.dto.ChatCompletionStreamResponse;
import com.chinatelecom.gs.engine.channel.openai.dto.ChatMessage;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.FinalMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.MessageRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.SseMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.BotAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * OpenAI API 兼容服务
 * 负责OpenAI API请求和响应的转换
 */
@Slf4j
@Service
public class OpenAICompatService {

    /**
     * 将OpenAI请求转换为内部MessageRequest
     *
     * @param request OpenAI格式的请求
     * @return 内部MessageRequest
     */
    public MessageRequest convertToMessageRequest(ChatCompletionRequest request) {
        MessageRequest messageRequest = new MessageRequest();

        // 设置agentCode，从model字段映射
        messageRequest.setAgentCode(request.getModel());

        // 设置会话ID，如果没有则生成一个
        messageRequest.setSessionId(request.getSessionId() == null ? UUID.randomUUID().toString() : request.getSessionId());

        // 设置消息ID
        messageRequest.setMessageId(IdGenerator.getMessageId());

        // 设置用户ID
        messageRequest.setUserId(request.getUser());

        // 设置请求时间
        messageRequest.setRequestTime(System.currentTimeMillis());


        messageRequest.setTest(request.getTest());

        // 从messages中提取最后一条用户消息作为query
        if (request.getMessages() != null && !request.getMessages().isEmpty()) {
            // 查找最后一条用户消息
            for (int i = request.getMessages().size() - 1; i >= 0; i--) {
                ChatMessage message = request.getMessages().get(i);
                if ("user".equals(message.getRole())) {
                    messageRequest.setContent(message.getContent());
//                    messageRequest.setQuery(message.getContent());
                    break;
                }
            }
        }

        // 设置额外数据，包括完整的消息历史
        if (request.getExtraData() != null) {
            messageRequest.setExtraData(request.getExtraData());
        }

        // 设置温度参数
        if (request.getTemperature() != null) {
            if (messageRequest.getExtraData() == null) {
                messageRequest.setExtraData(Collections.singletonMap("temperature", request.getTemperature()));
            } else {
                messageRequest.getExtraData().put("temperature", request.getTemperature());
            }
        }

        // 处理reasoning_content字段
        if (request.getMessages() != null && !request.getMessages().isEmpty()) {
            for (ChatMessage message : request.getMessages()) {
                if (message.getReasoning_content() != null && !message.getReasoning_content().isEmpty()) {
                    if (messageRequest.getExtraData() == null) {
                        messageRequest.setExtraData(Collections.singletonMap("reasoningContent", message.getReasoning_content()));
                    } else {
                        messageRequest.getExtraData().put("reasoningContent", message.getReasoning_content());
                    }
                    break;
                }
            }
        }

        return messageRequest;
    }

    /**
     * 将内部响应转换为OpenAI响应格式
     *
     * @param response 内部FinalMessageResponse
     * @param request 原始OpenAI请求
     * @return OpenAI格式的响应
     */
    public ChatCompletionResponse convertToOpenAIResponse(FinalMessageResponse response, ChatCompletionRequest request) {
        ChatCompletionResponse openAIResponse = new ChatCompletionResponse();

        // 设置基本信息
        openAIResponse.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", ""));
        openAIResponse.setCreated(System.currentTimeMillis() / 1000);
        openAIResponse.setModel(request.getModel());

        // 创建选项列表
        List<ChatCompletionResponse.ChatCompletionChoice> choices = new ArrayList<>();

        // 处理答案
        if (response.getAnswers() != null && !response.getAnswers().isEmpty()) {
            BotAnswer botAnswer = response.getAnswers().get(0);

            ChatCompletionResponse.ChatCompletionChoice choice = new ChatCompletionResponse.ChatCompletionChoice();
            choice.setIndex(0);
            choice.setFinish_reason("stop");

            // 创建消息
            ChatMessage message = new ChatMessage();
            message.setRole("assistant");

            // 设置内容
            if (botAnswer.getContent() != null) {
                if (botAnswer.getContent() instanceof String) {
                    message.setContent((String) botAnswer.getContent());
                } else {
                    message.setContent(botAnswer.getContent().toString());
                }
            }

            // 设置推理内容
            if (botAnswer.getReasoning() != null && botAnswer.getReasoning().getReasoningContent() != null) {
                message.setReasoning_content(botAnswer.getReasoning().getReasoningContent());
            }

            choice.setMessage(message);
            choices.add(choice);
        }

        openAIResponse.setChoices(choices);

        // 设置使用统计
        ChatCompletionResponse.Usage usage = new ChatCompletionResponse.Usage();
        // 这里需要根据实际情况计算token数量，暂时使用估算值
        int promptTokens = estimateTokens(request);
        int completionTokens = 0;

        if (!choices.isEmpty() && choices.get(0).getMessage() != null &&
            !ObjectUtils.isEmpty(choices.get(0).getMessage().getContent())) {
            completionTokens = estimateTokens(choices.get(0).getMessage().getContent());
        }

        usage.setPrompt_tokens(promptTokens);
        usage.setCompletion_tokens(completionTokens);
        usage.setTotal_tokens(promptTokens + completionTokens);

        openAIResponse.setUsage(usage);

        return openAIResponse;
    }

    /**
     * 将内部流式响应转换为OpenAI流式响应格式
     *
     * @param response 内部SseMessageResponse
     * @param request 原始OpenAI请求
     * @return OpenAI格式的流式响应
     */
    public ChatCompletionStreamResponse convertToOpenAIStreamResponse(SseMessageResponse response, ChatCompletionRequest request) {
        ChatCompletionStreamResponse streamResponse = new ChatCompletionStreamResponse();

        // 设置基本信息
        streamResponse.setId("chatcmpl-" + UUID.randomUUID().toString().replace("-", ""));
        streamResponse.setCreated(System.currentTimeMillis() / 1000);
        streamResponse.setModel(request.getModel());

        // 创建选项列表
        List<ChatCompletionStreamResponse.ChatCompletionStreamChoice> choices = new ArrayList<>();

        // 处理答案
        ChatCompletionStreamResponse.ChatCompletionStreamChoice choice = new ChatCompletionStreamResponse.ChatCompletionStreamChoice();
        choice.setIndex(0);

        // 创建增量消息
        ChatCompletionStreamResponse.ChatCompletionStreamDelta delta = new ChatCompletionStreamResponse.ChatCompletionStreamDelta();

        // 第一条消息设置角色
        if (response.getAnswer() != null) {
            if (response.getEventType() != null && response.getEventType().equals(SendMessageTypeEnum.ADD)) {
                delta.setRole("assistant");
            }

            // 设置内容
            if (response.getAnswer().getContent() != null) {
                if (response.getAnswer().getContent() instanceof String) {
                    delta.setContent((String) response.getAnswer().getContent());
                } else {
                    delta.setContent(response.getAnswer().getContent().toString());
                }
            }

            // 设置推理内容
            if (response.getAnswer().getReasoning() != null && response.getAnswer().getReasoning().getReasoningContent() != null) {
                delta.setReasoning_content(response.getAnswer().getReasoning().getReasoningContent());
            }
        }

        choice.setDelta(delta);

        // 设置结束原因
        if (response.getEventType() != null && response.getEventType().name().equals("FINISH")) {
            choice.setFinishReason("stop");
        }

        choices.add(choice);
        streamResponse.setChoices(choices);

        return streamResponse;
    }

    /**
     * 估算文本的token数量
     * 这是一个简单的估算方法，实际应用中可能需要更准确的计算
     *
     * @param text 文本
     * @return 估算的token数量
     */
    private int estimateTokens(String text) {
        if (ObjectUtils.isEmpty(text)) {
            return 0;
        }
        // 简单估算：平均每个单词1.3个token
        return (int) (text.split("\\s+").length * 1.3);
    }

    /**
     * 估算请求中的token数量
     *
     * @param request OpenAI请求
     * @return 估算的token数量
     */
    private int estimateTokens(ChatCompletionRequest request) {
        int total = 0;
        if (request.getMessages() != null) {
            for (ChatMessage message : request.getMessages()) {
                if (!ObjectUtils.isEmpty(message.getContent())) {
                    total += estimateTokens(message.getContent());
                }
            }
        }
        return total;
    }
}
