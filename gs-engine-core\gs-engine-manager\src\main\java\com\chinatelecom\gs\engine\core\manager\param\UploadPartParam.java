package com.chinatelecom.gs.engine.core.manager.param;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * @author: Wei
 * @date: 2025-04-22 10:27
 */
@Data
public class UploadPartParam {

    @NotEmpty(message = "上传ID不能为空")
    private String uploadId;

    @NotNull(message = "第几个分片不能为空")
    private Integer partNumber;

    @NotNull(message = "分片文件不能为空")
    private MultipartFile part;

}
