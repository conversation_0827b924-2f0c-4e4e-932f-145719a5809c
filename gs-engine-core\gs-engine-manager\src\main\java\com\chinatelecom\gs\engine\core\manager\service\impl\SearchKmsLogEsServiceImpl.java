package com.chinatelecom.gs.engine.core.manager.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.mq.KmsSearchMessage;
import com.chinatelecom.gs.engine.common.mq.TermQueryCount;
import com.chinatelecom.gs.engine.common.utils.EsPrefixUtils;
import com.chinatelecom.gs.engine.core.manager.repository.impl.KmsSearchMessageRepository;
import com.chinatelecom.gs.engine.core.manager.service.SearchKmsLogEsService;
import com.chinatelecom.gs.engine.core.manager.vo.config.base.QueryKmsSearchMessageVO;
import com.chinatelecom.gs.engine.robot.sdk.constant.CommonConstant;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.*;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @USER: pengmc1
 * @DATE: 2025/5/6 19:38
 */

@Service
@Slf4j
public class SearchKmsLogEsServiceImpl implements SearchKmsLogEsService {

    @Resource
    private ElasticsearchOperations elasticsearchOperations;

    @Autowired
    private KmsSearchMessageRepository repository;

    @Override
    public void saveLog(KmsSearchMessage logMessage) {
        try {
            repository.save(logMessage);
        } catch (RuntimeException e) {
            if (!e.getMessage().contains("Unable to parse response body")) {
                throw e;
            }
        }
    }

    @Override
    public Boolean deleteLogsBySearchTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            throw new BizException("AA089", "开始时间和结束时间必须提供");
        }
        PlatformUser user = SsoUtil.get();
        if (Boolean.TRUE.equals(!user.getIsSuperAdmin() && Objects.nonNull(user.getIsManager())) && user.getIsManager().equals(0)) {
            throw new BizException("AA090", "非管理员用户不能删除日志");
        }

        // 构建 range 查询：sendTime 在 [startTime, endTime] 之间
        String startTimeStr = startTime.format(DateTimeFormatter.ofPattern(CommonConstant.MONITOR_DATE_PATTERN));
        String endTimeStr = endTime.format(DateTimeFormatter.ofPattern(CommonConstant.MONITOR_DATE_PATTERN));

        Criteria criteria = new Criteria("searchTime.keyword")
                .greaterThanEqual(startTimeStr)
                .lessThanEqual(endTimeStr);
        Query query = new CriteriaQuery(criteria);

        long count = elasticsearchOperations.count(query, KmsSearchMessage.class,
                IndexCoordinates.of(EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME)));
        log.debug("匹配到{}条待删除文档", count);

        // 执行 Delete By Query
        DeleteQuery deleteQuery = DeleteQuery.builder(query).build();
        ByQueryResponse response = elasticsearchOperations.delete(deleteQuery, KmsSearchMessage.class, IndexCoordinates.of(EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME)));
        return response.getDeleted() > 0;
    }

    /**
     * 根据 logId 查询日志
     *
     * @param logId 唯一标识请求链路的日志 ID
     * @return 匹配的日志列表
     */
    @Override
    public List<QueryKmsSearchMessageVO> searchLogsByLogId(String logId) {
        if (logId == null || logId.isEmpty()) {
            throw new BizException("AA089", "logId 必须提供");
        }

        Criteria criteria = new Criteria("logId").is(logId);
        CriteriaQuery query = new CriteriaQuery(criteria);

        return elasticsearchOperations.search(query, KmsSearchMessage.class, IndexCoordinates.of(EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME)))
                .getSearchHits()
                .stream()
                .map(o -> {
                    KmsSearchMessage message = o.getContent();
                    QueryKmsSearchMessageVO logMessageVO = new QueryKmsSearchMessageVO();
                    BeanUtil.copyProperties(message, logMessageVO);
                    return logMessageVO;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<TermQueryCount> getTopQueries(String dimension, LocalDateTime startTime, LocalDateTime endTime) {
        return getTopQueriesByTenantAndTimeRange(RequestContext.getTenantId(), startTime, endTime, 50);
    }


    public List<TermQueryCount> getTopQueriesByTenantAndTimeRange(String tenantId, LocalDateTime startTime, LocalDateTime endTime, int limit) {
        return repository.findTopQueriesByTenantAndTimeRange(tenantId, startTime, endTime, limit);
    }

}
