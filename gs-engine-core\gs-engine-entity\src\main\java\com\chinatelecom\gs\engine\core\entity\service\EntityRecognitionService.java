package com.chinatelecom.gs.engine.core.entity.service;

import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionResponse;

/**
 * 实体识别接口
 *
 * @USER: pengmc1
 * @DATE: 2025/1/16 18:12
 */
public interface EntityRecognitionService {
    /**
     * 实体识别
     * @param request
     * @return
     */
    EntityRecognitionResponse predict(EntityRecognitionRequest request);
}
