package com.chinatelecom.gs.engine.core.entity.domain.request;

import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import lombok.Data;

import java.util.List;

/**
 * @USER: pengmc1
 * @DATE: 2025/2/13 15:43
 */

@Data
public class PluginRecognitionRequest {
    /**
     * 跟踪ID
     */
    private String sessionId;
    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 当前用户query
     */
    private String query;
    /**
     * 实体元数据
     */
    private List<EntityDetailVO> entityDetailVOs;
}
