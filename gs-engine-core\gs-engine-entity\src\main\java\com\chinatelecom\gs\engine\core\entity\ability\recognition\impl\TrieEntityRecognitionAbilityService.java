package com.chinatelecom.gs.engine.core.entity.ability.recognition.impl;

import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.core.entity.ability.recognition.AbstractEntityRecognitionAbilityService;
import com.chinatelecom.gs.engine.core.entity.common.trie.Trie;
import com.chinatelecom.gs.engine.core.entity.common.trie.WordPosition;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
import com.chinatelecom.gs.engine.core.entity.service.EntityService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
import com.chinatelecom.gs.engine.robot.sdk.dto.EntityContent;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.*;

/**
 * 基于前缀树的实体识别能力
 *
 * @USER: pengmc1
 * @DATE: 2025/1/16 19:32
 */

@Slf4j
@Service
public class TrieEntityRecognitionAbilityService extends AbstractEntityRecognitionAbilityService {

    /**
     * 租户隔离的实体前缀树
     */
    private final Map<String, Trie<Entity>> entityPreTreeMap = Maps.newHashMap();

    @Resource
    private EntityService entityService;

    /**
     * 实体前缀树初始化
     */
    @PostConstruct
    void init(){
        List<String> tenantIds = entityService.queryAllTenant();
        log.info("构造前缀数，租户列表：{}", JSON.toJSONString(tenantIds));
        for(String tenantId : tenantIds){
            doBuild(tenantId);
        }
    }

    /**
     * 识别能力标识
     *
     * @return
     */
    @Override
    public String abilityCode() {
        return EntityAbilityEnum.TRIE_MATCH.getCode();
    }

    /**
     * 执行实体识别
     *
     * @param request
     * @param entityDetailList
     * @return
     */
    @Override
    protected List<Entity> doPredict(EntityRecognitionRequest request, List<EntityDetailVO> entityDetailList) {
        List<Entity> entities = Lists.newArrayList();
        //获取前缀树
        Trie<Entity> entityTrie = entityPreTreeMap.get(request.getTenantId());
        if(null == entityTrie){
            log.info("【实体识别】【{}】获取前缀树为空", abilityCode());
            return entities;
        }
        //匹配实体值（全部匹配）
        List<WordPosition<Entity>> entityPositions = entityTrie.matchWordPositions(request.getQuery());
        log.info("【实体识别】【{}】前缀树匹配结果为：{}", abilityCode(), JSON.toJSONString(entityPositions));
        if(CollectionUtils.isEmpty(entityPositions) && entityPositions.size() < 1){
            return entities;
        }
        Map<String,List<WordPosition<Entity>>> entityPositionMap = Maps.newHashMap();
        entityPositions.forEach( wordPosition -> {
            if(!entityPositionMap.containsKey(wordPosition.getData().getEntityCode())){
                entityPositionMap.put(wordPosition.getData().getEntityCode(), new ArrayList<>());
            }
            entityPositionMap.get(wordPosition.getData().getEntityCode()).add(wordPosition);
        });

        //构造实体值列表
        entityDetailList.forEach( entityDetailVO -> {
            Entity entity = new Entity();
            entity.setTenantId(entityDetailVO.getTenantId());
            entity.setEntityCode(entityDetailVO.getEntityCode());
            entity.setEntityType(EntityTypeEnum.ENUM_ENTITY);
            entity.setEntityContents(new ArrayList<>());
            List<WordPosition<Entity>> wordPositions = entityPositionMap.get(entityDetailVO.getEntityCode());
            if(CollectionUtils.isNotEmpty(wordPositions)){
                wordPositions.forEach( wordPosition -> {
                    Entity entityData = wordPosition.getData();
                    entity.setKmsCode(entityData.getKmsCode());
                    entity.setEntityName(entityData.getEntityName());
                    if(CollectionUtils.isNotEmpty(entityData.getEntityContents())){
                        EntityContent entityContent = new EntityContent();
                        EntityContent entityContentData = entityData.getEntityContents().get(0);
                        entityContent.setStart(wordPosition.getStart());
                        entityContent.setEnd(wordPosition.getEnd());
                        entityContent.setValue(entityContentData.getValue());
                        entityContent.setRawValue(entityContentData.getRawValue());
                        entity.addEntityContent(entityContent);
                    }else{
                        EntityContent entityContent = new EntityContent();
                        entityContent.setStart(wordPosition.getStart());
                        entityContent.setEnd(wordPosition.getEnd());
                        entityContent.setValue(wordPosition.getWord());
                        entityContent.setRawValue(wordPosition.getWord());
                        entity.addEntityContent(entityContent);
                    }
                });
                entities.add(entity);
            }
        });
        return entities;
    }

    /**
     * 构造前缀树
     * @param tenantId
     */
    public void doBuild(String tenantId) {
        Trie<Entity> entityTrie = new Trie<>();
        int curPage = 1;
        Integer pageSize = 100;
        while (true){
            //查询实体枚举值
            Page<EntityDetailVO> pageResult = entityService.queryEntityDetailList(tenantId, EntityTypeEnum.ENUM_ENTITY.getCode(), curPage, pageSize);
            if(Objects.isNull(pageResult)){
                break;
            }
            List<EntityDetailVO> dataList = pageResult.getRecords();
            if(CollectionUtils.isEmpty(dataList)){
                break;
            }
            dataList.forEach( item -> {
                if(Objects.isNull(item) || CollectionUtils.isEmpty(item.getEntityDataList())){
                    return;
                }
                item.getEntityDataList().forEach(detail -> {
                    Entity entity = new Entity();
                    entity.setEntityCode(item.getEntityCode());
                    entity.setEntityName(item.getEntityName());
                    entity.setEntityType(EntityTypeEnum.ENUM_ENTITY);
                    EntityContent entityContent = new EntityContent();
                    entityContent.setValue(detail.getEntityValue());
                    entityContent.setRawValue(detail.getEntityValue());
                    entity.setEntityContents(Lists.newArrayList(entityContent));
                    entityTrie.addNodeData(detail.getEntityValue(), entity.getEntityCode(), entity);
                    //近义词
                    String entitySynonym = detail.getEntitySynonym();
                    if(StringUtils.isNotBlank(entitySynonym)){
                        List<String> synonymList =  Arrays.asList(entitySynonym.split(","));
                        synonymList.forEach( synonym -> {
                            Entity synonymEntity = new Entity();
                            synonymEntity.setEntityCode(item.getEntityCode());
                            synonymEntity.setEntityName(item.getEntityName());
                            synonymEntity.setEntityType(EntityTypeEnum.ENUM_ENTITY);
                            EntityContent synonymEntityContent = new EntityContent();
                            synonymEntityContent.setValue(detail.getEntityValue());
                            synonymEntityContent.setRawValue(synonym);
                            synonymEntity.setEntityContents(Lists.newArrayList(entityContent));
                            entityTrie.addNodeData(synonym, entity.getEntityCode(), synonymEntity);
                        });
                    }
                });
            });
            if(pageResult.getPages() <= curPage){
                break;
            }
            curPage++;
        }
        entityPreTreeMap.put(tenantId, entityTrie);
    }
}
