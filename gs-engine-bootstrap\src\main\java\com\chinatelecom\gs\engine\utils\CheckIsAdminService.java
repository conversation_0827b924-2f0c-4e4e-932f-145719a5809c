package com.chinatelecom.gs.engine.utils;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.platform.AppOwnerRequest;
import com.chinatelecom.gs.engine.common.platform.GovernmentAuthClient;
import com.chinatelecom.gs.engine.common.platform.KsAuthClient;
import com.chinatelecom.gs.engine.common.utils.UserInfoUtils;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
@Slf4j
public class CheckIsAdminService {

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Resource
    private KsAuthClient ksAuthClient;

    @Resource
    private GovernmentAuthClient governmentAuthClient;

    public boolean checkIsAdmin(AppSourceType appSourceType, String userId, String corpCode) {
        log.info("查询账号是否为管理员 source:{},userId:{},corpCode:{}", appSourceType, userId, corpCode);
        try {
            if (AppSourceType.KS.equals(appSourceType)) {
                Result<Boolean> appOwner = ksAuthClient.isAppOwner();
                return appOwner.isSuccess() && appOwner.getData();
            } else if (AppSourceType.GOVERNMENT.equals(appSourceType)) {
                AppOwnerRequest appOwnerRequest = new AppOwnerRequest();
                appOwnerRequest.setCorpCode(corpCode);
                appOwnerRequest.setUserId(userId);
                Result<Boolean> appOwner = governmentAuthClient.isAppOwner(appOwnerRequest);
                return appOwner.isSuccess() && appOwner.getData();
            } else {
                return UserInfoUtils.isAdmin(corpCode, userId);
            }
        } catch (Exception e) {
            log.error("向子账号查询账号信息失败", e);
            if (gsGlobalConfig.getSystem().isUseEngineAppInfo()) {
                return UserInfoUtils.isAdmin(corpCode, userId);
            } else {
                throw e;
            }
        }
    }
}
