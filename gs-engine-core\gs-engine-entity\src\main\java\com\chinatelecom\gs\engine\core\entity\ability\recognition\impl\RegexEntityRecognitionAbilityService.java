package com.chinatelecom.gs.engine.core.entity.ability.recognition.impl;

import com.chinatelecom.gs.engine.core.entity.ability.recognition.AbstractEntityRecognitionAbilityService;
import com.chinatelecom.gs.engine.core.entity.common.utils.ParallelUtils;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
import com.chinatelecom.gs.engine.core.entity.utils.RegexUtils;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
import com.chinatelecom.gs.engine.robot.sdk.dto.EntityContent;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 基于正则表达式的实体识别
 *
 * @USER: pengmc1
 * @DATE: 2025/1/16 19:32
 */

@Slf4j
@Service
public class RegexEntityRecognitionAbilityService extends AbstractEntityRecognitionAbilityService {

    @Resource
    private ExecutorService commonExecutorService;

    /**
     * 识别能力标识
     *
     * @return
     */
    @Override
    public String abilityCode() {
        return EntityAbilityEnum.REGEX_MATCH.getCode();
    }

    /**
     * 执行实体识别
     *
     * @param request
     * @param entityDetailList
     * @return
     */
    @Override
    protected List<Entity> doPredict(EntityRecognitionRequest request, List<EntityDetailVO> entityDetailList) {
        List<Entity> entities = ParallelUtils.parallelExecute(commonExecutorService, entityDetailList, 60000L,
                entityDetailVO -> regexMatch(request.getQuery(),entityDetailVO)
        );
        return entities;
    }

    /**
     * 正则匹配识别
     * @param content   识别内容
     * @param entityDetail  实体详情
     * @return
     */
    private List<Entity> regexMatch(String content, EntityDetailVO entityDetail) {
        List<Entity> entities = Lists.newArrayList();
        if (StringUtils.isBlank(content) || Objects.isNull(entityDetail)) {
            return entities;
        }
        String regex = getAbilityConfig(entityDetail, EntityAbilityEnum.REGEX_MATCH).getRegExp();
        log.info("【实体识别】【{}】正则表达式为：{}", abilityCode(), regex);
        try {
            Pattern pattern = RegexUtils.getPattern(regex);
            Matcher matcher = pattern.matcher(content);
            Entity entity = new Entity();
            entity.setEntityCode(entityDetail.getEntityCode());
            entity.setEntityName(entityDetail.getEntityName());
            entity.setTenantId(entityDetail.getTenantId());
            entity.setEntityType(EntityTypeEnum.NORMAL_ENTITY);
            while (matcher.find()) {
                EntityContent entityContent = new EntityContent(matcher.start(),matcher.end(),matcher.group());
                entity.addEntityContent(entityContent);
            }
            if (CollectionUtils.isNotEmpty(entity.getEntityContents())) {
                entities.add(entity);
            }
        } catch (Exception e) {
            log.error("【实体识别】【{}】正则匹配失败！", abilityCode(), e);
        }
        return entities;
    }
}
