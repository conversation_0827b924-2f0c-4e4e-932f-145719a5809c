package com.chinatelecom.gs.engine.channel.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/25 15:53
 * @description
 */
@Schema(description = "渠道下的机器人接入信息配置")
@Data
public class ChannelRobotConfigVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -4964932631197547158L;

    @Schema(description = "机器人编码")
    private String botCode;

    @Schema(description = "appid，机器人平台生成")
    private String appId;

    @Schema(description = "secretId，机器人平台生成")
    private String secretId;

    @Schema(description = "机器人访问地址")
    private String url;

    @Schema(description = "机器人提供方")
    private String provider;
}
