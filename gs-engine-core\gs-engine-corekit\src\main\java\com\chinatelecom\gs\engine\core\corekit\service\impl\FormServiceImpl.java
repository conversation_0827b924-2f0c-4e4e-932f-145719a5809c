package com.chinatelecom.gs.engine.core.corekit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinatelecom.gs.engine.common.infra.base.impl.BaseExtendServiceImpl;
import com.chinatelecom.gs.engine.core.corekit.convert.FormConvert;
import com.chinatelecom.gs.engine.core.corekit.domain.po.FormPO;
import com.chinatelecom.gs.engine.core.sdk.enums.FormStatusEnum;
import com.chinatelecom.gs.engine.core.sdk.request.FormDelRequest;
import com.chinatelecom.gs.engine.core.sdk.request.FormQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.request.FormSaveRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.FormVO;
import com.chinatelecom.gs.engine.core.corekit.mapper.FormMapper;
import com.chinatelecom.gs.engine.core.corekit.service.FormService;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @USER: pengmc1
 * @DATE: 2025/7/21 11:12
 */

@Service
public class FormServiceImpl extends BaseExtendServiceImpl<FormMapper, FormPO> implements FormService {

    /**
     * 保存表单
     *
     * @param request
     * @return
     */
    @Override
    public Boolean saveForm(FormSaveRequest request) {
        if(Objects.isNull(request)){
            return false;
        }
        FormPO formPO = FormConvert.INSTANCE.convert(request);
        //查询数据库中的数据
        FormVO preFormVO = getDetail(request.getFormCode(), request.getSource());
        if(Objects.nonNull(preFormVO)){
            if(FormStatusEnum.PUBLISHED.getCode().equalsIgnoreCase(preFormVO.getStatus())){
                //数据库中是发布状态，需要新增一行数据，并新增版本号
                formPO.setVersion(preFormVO.getVersion() + 1L);
                formPO.setSource(preFormVO.getSource());
                formPO.setStatus(FormStatusEnum.UNPUBLISHED.getCode());
                return save(formPO);
            }else{
                //数据库中为未发布状态，在原来数据上进行修改
                LambdaUpdateWrapper<FormPO> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(FormPO::getId, preFormVO.getId());
                updateWrapper.set(FormPO::getFormName, request.getFormName());
                updateWrapper.set(FormPO::getFormData, request.getFormData());
                return update(updateWrapper);
            }
        }else{
            //新增数据
            formPO.setVersion(1L);
            formPO.setStatus(FormStatusEnum.UNPUBLISHED.getCode());
            return save(formPO);
        }
    }

    /**
     * 发布表单
     *
     * @param request
     * @return
     */
    @Override
    public Boolean publishForm(FormSaveRequest request) {
        //先获取最新版本号的表单数据
        FormVO formVO = getDetail(request.getFormCode(), request.getSource());
        if(Objects.nonNull(formVO) && FormStatusEnum.PUBLISHED.getCode().equalsIgnoreCase(formVO.getStatus())){
            //已发布，不能重复发布
            throw new BizException("表单已发布，不能重复发布");
        }
        if(Objects.nonNull(formVO)){
            //更新数据，并修改为发布状态
            LambdaUpdateWrapper<FormPO> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(FormPO::getId, formVO.getId());
            updateWrapper.set(FormPO::getFormData, request.getFormData());
            updateWrapper.set(FormPO::getFormName, request.getFormName());
            updateWrapper.set(FormPO::getStatus, FormStatusEnum.PUBLISHED.getCode());
            return update(updateWrapper);
        }{
            //新增数据，并修改为发布状态
            FormPO newFormPO = FormConvert.INSTANCE.convert(request);
            newFormPO.setVersion(1L);
            newFormPO.setStatus(FormStatusEnum.PUBLISHED.getCode());
            return save(newFormPO);
        }
    }

    /**
     * 删除表单
     *
     * @param request
     * @return
     */
    @Override
    public Boolean deleteForm(FormDelRequest request) {
        LambdaQueryWrapper<FormPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(FormPO::getFormCode,request.getFormCodes());
        return remove(queryWrapper);
    }

    /**
     * 获取表单详情
     *
     * @param formCode
     * @param source
     * @return
     */
    @Override
    public FormVO getDetail(String formCode, String source) {
        if(StringUtils.isBlank(formCode)){
            return null;
        }
        LambdaQueryWrapper<FormPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(FormPO::getFormCode,formCode);
        queryWrapper.eq(StringUtils.isNotBlank(source), FormPO::getSource,source);
        queryWrapper.orderByDesc(FormPO::getVersion);
        queryWrapper.last("limit 1");
        FormPO formPO = this.getOne(queryWrapper);
        return FormConvert.INSTANCE.convert(formPO);
    }

    /**
     * 获取最新发布表单详情
     *
     * @param formCode
     * @param source
     * @return
     */
    @Override
    public FormVO getLastPublishDetail(String formCode, String source) {
        if(StringUtils.isBlank(formCode)){
            return null;
        }
        LambdaQueryWrapper<FormPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(FormPO::getFormCode,formCode);
        queryWrapper.eq(StringUtils.isNotBlank(source), FormPO::getSource,source);
        queryWrapper.eq(FormPO::getStatus, FormStatusEnum.PUBLISHED.getCode());
        queryWrapper.orderByDesc(FormPO::getVersion);
        queryWrapper.last("limit 1");
        FormPO formPO = this.getOne(queryWrapper);
        return FormConvert.INSTANCE.convert(formPO);
    }

    /**
     * 根据版本获取表单详情
     *
     * @param formCode
     * @param source
     * @param version
     * @return
     */
    @Override
    public FormVO getDetailByVersion(String formCode, String source, Long version) {
        if(StringUtils.isBlank(formCode)){
            return null;
        }
        LambdaQueryWrapper<FormPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(FormPO::getFormCode,formCode);
        queryWrapper.eq(StringUtils.isNotBlank(source), FormPO::getSource,source);
        queryWrapper.eq(FormPO::getVersion,version);
        FormPO formPO = this.getOne(queryWrapper);
        return FormConvert.INSTANCE.convert(formPO);
    }

    /**
     * 获取表单列表
     *
     * @param request
     * @return
     */
    @Override
    public List<FormVO> queryFormList(FormQueryRequest request) {
        List<FormPO> formPOs = this.getBaseMapper().queryFormList(request.getFormCodes(),request.getStatus(), request.getSource());
        if(CollectionUtils.isNotEmpty(formPOs)){
            return formPOs.stream().map(FormConvert.INSTANCE::convert).collect(Collectors.toList());
        }
        return null;
    }
}
