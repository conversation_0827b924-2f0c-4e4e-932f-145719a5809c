package com.chinatelecom.gs.engine.common.es.repositiry.impl;

import com.chinatelecom.gs.engine.common.es.repositiry.AgentEsOperationRepository;

//import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.convert.ElasticsearchConverter;
import org.springframework.data.elasticsearch.core.index.Settings;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.ByQueryResponse;
import org.springframework.data.elasticsearch.core.query.DeleteQuery;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
//import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
//import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/16 15:09
 */
@Service
public class AgentEsOperationRepositoryImpl implements AgentEsOperationRepository {

    private static final Function<?, String> NULL_FUNC = x->null;

    @Resource
    private ElasticsearchOperations operations;

    @Resource
    private ElasticsearchConverter elasticsearchConverter;

    @Override
    public boolean createIndex(String index, Class<?> entityClass){
        // 构建查询条件
        try{
            IndexOperations indexOperations = operations.indexOps(entityClass);
            Settings settings = indexOperations.createSettings();
            // 设置分片数
            settings.put("number_of_shards", 1);
            settings.put("number_of_replicas", 0);
            return operations.indexOps(IndexCoordinates.of(index))
                    .create(settings, indexOperations.createMapping());
        }catch(Exception e){
            if(e.getMessage().contains("resource_already_exists_exception")){
                return operations.indexOps(IndexCoordinates.of(index)).putMapping(entityClass);
            }
            throw e;
        }
    }

    @Override
    public boolean deleteIndex(String index){
        try{
            return operations.indexOps(IndexCoordinates.of(index)).delete();
        }catch(Exception e){
            if(e.getMessage().contains("index_not_found_exception")){
                return true;
            }
            throw e;
        }
    }

    @Override
    public <T> void save(String index, T poClass){
        if(poClass == null){
            return;
        }
        operations.save(poClass, IndexCoordinates.of(index));
    }

    @Override
    public <T> void bulkSaveOrUpdate(String index, List<T> dataList){
        bulkSaveOrUpdate(index, dataList, (Function<T, String>) NULL_FUNC);
    }

    @Override
    public <T> void bulkSaveOrUpdate(String index, List<T> dataList, Function<T, String> idExtractor){
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        List<IndexQuery> queries = dataList.stream().map(data->{
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setId(idExtractor.apply(data));
            indexQuery.setObject(data);
            return indexQuery;
        }).collect(Collectors.toList());
        operations.bulkIndex(queries, IndexCoordinates.of(index));
    }

    @Override
    public <T> void bulkUpdate(String index, List<T> dataList){
        bulkUpdate(index, dataList, (Function<T, String>) NULL_FUNC);
    }

    @Override
    public <T> void bulkUpdate(String index, List<T> dataList, Function<T, String> idExtractor){
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        List<UpdateQuery> queries = dataList.stream().map(data->UpdateQuery.builder(idExtractor.apply(data))
                .withDocument(elasticsearchConverter.mapObject(data))
                .build()).collect(Collectors.toList());
        operations.bulkUpdate(queries, IndexCoordinates.of(index));
    }

//    @Override
//    public <T> SearchHits<T> search(String index, Class<T> poClass, QueryBuilder queryBuilder){
//        return search(index, poClass, queryBuilder, null);
//    }
//
//    @Override
//    public <T> SearchHits<T> search(String index, Class<T> poClass, QueryBuilder queryBuilder, Pageable pageable){
//        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
//                .withQuery(queryBuilder)
//                .withPageable(pageable)
//                .build();
//        return search(index, poClass, nativeSearchQuery);
//    }

    @Override
    public <T> SearchHits<T> search(String index, Class<T> poClass, Query query){
        return operations.search(query, poClass, IndexCoordinates.of(index));
    }

    @Override
    public <T> SearchHit<T> searchOne(String index, Class<T> poClass, Query query){
        return operations.searchOne(query, poClass, IndexCoordinates.of(index));
    }

    @Override
    public boolean exist(String index){
        return operations.indexOps(IndexCoordinates.of(index)).exists();
    }

    public <T> ByQueryResponse delete(String index, Class<T> poClass, Query query){
        // 在新版本中，需要创建 DeleteQuery
        DeleteQuery deleteQuery = DeleteQuery.builder(query).build();
        return operations.delete(deleteQuery, poClass, IndexCoordinates.of(index));
    }
}
