package com.chinatelecom.gs.engine.channel.dao.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinatelecom.gs.engine.channel.common.enums.MessageDirectionEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/22 10:53
 * @description 会话记录表
 */
@Setter
@Getter
@TableName("channel_message_record")
public class ChannelMessageRecordPO extends BasePO implements Serializable {
    @Serial
    private static final long serialVersionUID = -2782260050753522718L;

    @TableField("channel_id")
    private String channelId;

    @TableField("robot_code")
    private String robotCode;

    @TableField("session_id")
    private String sessionId;

    @TableField("user_id")
    private String userId;

    @TableField("message_id")
    private String messageId;

    @TableField("message")
    private String message;

    @TableField("message_type")
    private String messageType;

    @TableField("msg_direction")
    private MessageDirectionEnum msgDirection;

}
