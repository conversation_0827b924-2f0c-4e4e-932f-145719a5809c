package com.chinatelecom.gs.engine.channel.common.cache.localcache;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.channel.common.enums.ConfigKeyEnums;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelConfigPO;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelConfigRepository;
import com.chinatelecom.gs.engine.channel.service.dto.QywxConfigDTO;
import com.chinatelecom.gs.engine.channel.service.dto.RobotConfigDTO;
import com.chinatelecom.gs.engine.channel.service.messagehandler.qywxapp.encrypt.WXBizMsgCrypt;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/12/22 17:43
 * @description
 */
@Slf4j
@Service
public class ChannelCaches {

    @Resource
    private ChannelConfigRepository configRepository;

//    LoadingCache<String, ImmutablePair<String, WXBizMsgCrypt>> cryptCache = Caffeine.newBuilder()
//            .expireAfterWrite(10, TimeUnit.MINUTES)
//            .maximumSize(10000)
//            .refreshAfterWrite(9, TimeUnit.MINUTES)
//            .build(key -> {
//                if (StringUtils.isBlank(key)) {
//                    return null;
//                }
//                LambdaQueryWrapper<ChannelConfigPO> queryWrapper = new LambdaQueryWrapper<>();
//                queryWrapper.eq(ChannelConfigPO::getChannelId, key)
//                        .eq(ChannelConfigPO::getConfigKey, ConfigKeyEnums.QYWX_APP_CONFIG.getCode());
//                ChannelConfigPO configPO = configRepository.getOne(queryWrapper);
//                if (Objects.isNull(configPO)) {
//                    return null;
//                }
//                try {
//                    QywxConfigDTO qywxConfigDTO = JsonUtils.parseObject(configPO.getConfigValue(), QywxConfigDTO.class);
//                    return ImmutablePair.of(qywxConfigDTO.getCorpId(), new WXBizMsgCrypt(qywxConfigDTO.getToken(), qywxConfigDTO.getEncodingAESKey(), qywxConfigDTO.getCorpId()));
//                } catch (Exception e) {
//                    log.error("加载企微应用消息渠道配置缓存失败", e);
//                    return null;
//                }
//            });

    LoadingCache<String, QywxConfigDTO> cryptConfigCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(10000)
            .refreshAfterWrite(9, TimeUnit.MINUTES)
            .build(key -> {
                if (StringUtils.isBlank(key)) {
                    return null;
                }
                LambdaQueryWrapper<ChannelConfigPO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ChannelConfigPO::getChannelId, key)
                        .eq(ChannelConfigPO::getConfigKey, ConfigKeyEnums.QYWX_APP_CONFIG.getCode());
                ChannelConfigPO config = configRepository.getOne(queryWrapper);
                if (Objects.isNull(config)) {
                    return null;
                }
                try {
                    return JsonUtils.parseObject(config.getConfigValue(), QywxConfigDTO.class);
                } catch (Exception e) {
                    log.error("加载企微应用配置缓存失败", e);
                    return null;
                }
            });

    private LoadingCache<String, RobotConfigDTO> robotConfigDTOCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(10000)
            .refreshAfterWrite(9, TimeUnit.MINUTES)
            .build(key -> {
                if (StringUtils.isBlank(key)) {
                    return null;
                }
                LambdaQueryWrapper<ChannelConfigPO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ChannelConfigPO::getChannelId, key)
                        .eq(ChannelConfigPO::getConfigKey, ConfigKeyEnums.QYWX_APP_CONFIG.getCode());
                ChannelConfigPO configPO = configRepository.getOne(queryWrapper);
                if (Objects.isNull(configPO)) {
                    return null;
                }

                try {
                    RobotConfigDTO robotConfigDTO = JsonUtils.parseObject(configPO.getConfigValue(), RobotConfigDTO.class);
                    robotConfigDTO.setTenantId(configPO.getTenantId());
                    return robotConfigDTO;
                } catch (Exception e) {
                    log.error("机器人配置为空channelId:{}", key, e);
                    return null;
                }
            });

    public WXBizMsgCrypt getWxBizMsgCrypt(String channelId){
        try {
            QywxConfigDTO config = cryptConfigCache.get(channelId);
            if (Objects.isNull(config)) {
                log.error("企业微信未正确配置，配置数据未空, channelId:{}", channelId);
                throw new BizException("AD040", "企业微信渠道未正确配置，配置数据为空");
            }
            return new WXBizMsgCrypt(config.getToken(), config.getEncodingAESKey(),
                    config.getCorpId());
        } catch (Exception e) {
            log.error("获取企业微信配置报错", e);
        }
        return null;
    }

    public QywxConfigDTO getWxCryptConfig(String channelId) {
        QywxConfigDTO config = cryptConfigCache.get(channelId);
        if (Objects.isNull(config)) {
            log.error("企业微信未正确配置，配置数据未空, channelId:{}", channelId);
            throw new BizException("AD040", "企业微信渠道未正确配置，配置数据为空");
        }
        return config;
    }

    public RobotConfigDTO getRobotConfig(String channelId) {
        RobotConfigDTO config = robotConfigDTOCache.get(channelId);
        if (Objects.isNull(config)) {
            log.error("机器人配置为空， channelId：{}", channelId);
            throw new BizException("AD041", "机器人配置信息为空");
        }

        return config;
    }
}
