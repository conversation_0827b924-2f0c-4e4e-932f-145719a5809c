package com.chinatelecom.gs.engine.core.manager.convert;

import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.manager.dto.BaseConfigDTO;
import com.chinatelecom.gs.engine.core.manager.vo.config.*;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
public class ConfigConvert {
    private static final Map<String, Class<? extends ConfigValidator>> configTypeMap = new HashMap<>();

    /**
     * 前端使用的默认全局配置
     */
    public static final String GLOBAL_CONFIG = "globalConfig";
    /**
     * 发布相关业务配置
     */
    public static final String PUBLISH_CONFIG = "publishConfig";
    /**
     * 支持的文件类型的配置
     */
    public static final String INTERNAL_CONFIG = "internalConfig";

    /**
     * 语音配置
     */
    public static final String SPEECH_CONFIG = "speechConfig";

    /**
     * 敏感词配置
     */
    public static final String SENSITIVE_CONFIG = "sensitiveConfig";
    /**
     * 聊天窗文案提示配置
     */
    public static final String LLM_CHAT_HINT_CONFIG = "chatHintConfig";
    /**
     * 聊天窗文案提示配置
     */
    public static final String SAFE_FENCE_CONFIG = "safeFenceConfig";
    /**
     * 知识文件扫描任务配置
     */
    public static final String KNWL_FILE_LENGTH_SCAN_TASK_CONFIG = "KnowledgeFileLengthScanTask";


    static {
        configTypeMap.put(GLOBAL_CONFIG, GlobalConfig.class);
        configTypeMap.put(PUBLISH_CONFIG, PublishConfig.class);
        configTypeMap.put(SPEECH_CONFIG, SpeechConfig.class);
        configTypeMap.put(SENSITIVE_CONFIG, SensitiveConfig.class);
        configTypeMap.put(INTERNAL_CONFIG, InternalFunctionConfig.class);
        configTypeMap.put(LLM_CHAT_HINT_CONFIG, LLMChatHint.class);
        configTypeMap.put(SAFE_FENCE_CONFIG, SafeFenceMenuConfig.class);
        configTypeMap.put(KNWL_FILE_LENGTH_SCAN_TASK_CONFIG, KmsFileLengthTaskConfig.class);
    }


    /**
     * 根据传入的 BaseConfigDTO 对象，动态反序列化 configData 为指定的泛型类实例并进行校验。
     * 如果 configType 不存在于 configTypeMap 中，则将 configData 反序列化为 JsonNode 返回。
     *
     * @param config 包含 configType 和 configData 的对象
     * @param <T>    泛型类型，必须实现 ConfigValidator
     * @return 返回具体的配置类实例（已校验），或者 JsonNode
     * @throws BizException 如果校验失败或反序列化失败
     */
    public <T extends ConfigValidator> Object getConfigObjectAndValidate(BaseConfigDTO config) throws BizException {
        // 获取配置类型
        String configType = config.getConfigType();
        // 获取配置数据
        String configData = config.getConfigData();
        if (configTypeMap.containsKey(configType)) {
            // 反序列化为指定的泛型类型
            T configObject = (T) JsonUtils.parseObject(configData, configTypeMap.get(configType));
            // 进行参数校验
            configObject.validate(new HashMap<>());
            // 返回具体的类型
            return configObject;
        } else {
            // 将 configData 反序列化为 JsonNode
            return JsonUtils.parseObject(configData, JsonNode.class);
        }

    }

}

