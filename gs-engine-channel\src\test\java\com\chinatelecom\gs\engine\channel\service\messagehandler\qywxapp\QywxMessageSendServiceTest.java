package com.chinatelecom.gs.engine.channel.service.messagehandler.qywxapp;

import com.chinatelecom.gs.engine.channel.common.AccessTokenUtil;
import com.chinatelecom.gs.engine.channel.common.SessionIdHolder;
import com.chinatelecom.gs.engine.channel.common.cache.localcache.ChannelCaches;
import com.chinatelecom.gs.engine.channel.common.utils.HttpUtils;
import com.chinatelecom.gs.engine.channel.foundation.BotPlatformDialog;
import com.chinatelecom.gs.engine.channel.foundation.platformanswer.PlatformAnswerTransformer;
import com.chinatelecom.gs.engine.channel.service.dto.BaseSendMessageDTO;
import com.chinatelecom.gs.engine.channel.service.dto.RobotConfigDTO;
import com.chinatelecom.gs.engine.channel.service.messagehandler.MessageRecordService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.BotAnswer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class QywxMessageSendServiceTest {

    @Mock
    private ChannelCaches caches;

    @Mock
    private BotPlatformDialog botPlatformDialog;

    @Mock
    private SessionIdHolder sessionIdHolder;

    @Mock
    private MessageRecordService messageRecordService;

    @Mock
    private AccessTokenUtil accessTokenUtil;

    @Mock
    private PlatformAnswerTransformer platformAnswerTransformer;

    @Mock
    private HttpUtils httpUtils;

    @InjectMocks
    private QywxMessageSendService qywxMessageSendService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
//        qywxMessageSendService.setWeixinSendMessageUrl("http://test.url");
    }

    @Test
    void testAsyncReply_Success() throws Exception {
        // 准备测试数据
        String channelId = "testChannel";
        String userId = "testUser";
        String content = "testContent";
        Integer agentId = 123;
        String corpId = "testCorp";

        RobotConfigDTO robotConfig = new RobotConfigDTO();
        robotConfig.setBotCode("testBot");
        robotConfig.setTenantId("testTenant");

        when(caches.getRobotConfig(anyString())).thenReturn(robotConfig);
        when(sessionIdHolder.getSessionId(anyString(), anyString())).thenReturn("testSession");
        when(botPlatformDialog.chat(anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(Collections.singletonList(new BotAnswer()));
        when(platformAnswerTransformer.convertCsrobotAnswer(any(), anyString(), any(), anyString(), anyString()))
                .thenReturn(Collections.singletonList(new BaseSendMessageDTO()));

        // 执行测试
        qywxMessageSendService.asyncReply(channelId, userId, content, agentId, corpId);

        // 验证结果
        verify(messageRecordService, times(2)).recordMessage(any());
        verify(botPlatformDialog).chat(anyString(), anyString(), anyString(), anyString(), any());
    }

}