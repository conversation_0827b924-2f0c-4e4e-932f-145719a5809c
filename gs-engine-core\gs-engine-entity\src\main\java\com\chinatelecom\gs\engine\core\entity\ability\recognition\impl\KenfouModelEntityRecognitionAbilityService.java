package com.chinatelecom.gs.engine.core.entity.ability.recognition.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinatelecom.gs.engine.common.cache.memory.SessionHistoryCacheService;
import com.chinatelecom.gs.engine.common.enums.LogStatusEnum;
import com.chinatelecom.gs.engine.common.enums.LogTypeEnum;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.entity.ability.recognition.AbstractModelEntityRecognitionAbilityService;
import com.chinatelecom.gs.engine.robot.sdk.dto.SimpleChatLog;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.MessageRoleEnum;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
import com.chinatelecom.gs.engine.robot.sdk.dto.EntityContent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 肯否模型实体识别
 * @USER: pengmc1
 * @DATE: 2025/6/5 10:43
 */

@Slf4j
@Service
public class KenfouModelEntityRecognitionAbilityService extends AbstractModelEntityRecognitionAbilityService {

    @Resource
    private ModelServiceClient remoteServiceClient;

    @Resource
    private RestTemplate restNoBalancedTemplate;

    @Resource
    private SessionHistoryCacheService sessionHistoryCacheService;

    /**
     * 识别能力标识
     *
     * @return
     */
    @Override
    public String abilityCode() {
        return EntityAbilityEnum.MODEL_RECOGNITION.getCode() + ":kenfouEntityRecognition";
    }

    /**
     * 执行实体识别
     *
     * @param request
     * @param entityDetailList
     * @return
     */
    @Override
    protected List<Entity> doPredict(EntityRecognitionRequest request, List<EntityDetailVO> entityDetailList) {
        String modelCode = getAbilityConfig(entityDetailList.get(0), EntityAbilityEnum.MODEL_RECOGNITION).getModelCode();
        log.info("【实体识别】【{}】模型编码为：{}", abilityCode(), modelCode);
        //获取模型元数据
        ModelPageListParam modelParam = remoteServiceClient.queryByModelCode(modelCode);
        //进行通用小模型实体识别
        Entity orgEntity = recognition(modelParam, request);
        if(Objects.isNull(orgEntity)){
            return null;
        }
        //筛选出需要的实体结果
        List<Entity> entityList = Lists.newArrayList();
        entityDetailList.forEach( entityDetailVO -> {
            Entity entity = new Entity();
            entity.setEntityContents(orgEntity.getEntityContents());
            entity.setEntityCode(entityDetailVO.getEntityCode());
            entity.setEntityName(entityDetailVO.getEntityName());
            entity.setEntityType(entityDetailVO.getEntityType());
            entity.setTenantId(entityDetailVO.getTenantId());
            List<Entity> itemFinalEntities = filterConvertEntity(entityDetailVO, Arrays.asList(entity));
            if(CollectionUtils.isNotEmpty(itemFinalEntities)){
                entityList.addAll(itemFinalEntities);
            }
        });
        return entityList;
    }

    /**
     * 进行通用小模型实体识别
     * @param modelParam
     * @param request
     * @return
     */
    private Entity recognition(ModelPageListParam modelParam, EntityRecognitionRequest request){
        LocalDateTime startTime = LocalDateTime.now();
        Map<String, Object> paramMap = Maps.newHashMap();
        String rpcResult =null;
        LogStatusEnum logStatusEnum = LogStatusEnum.SUCCESS;
        try{
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Content-Type", "application/json");
            headers.set("App-Key", modelParam.getApiKey());
            headers.set("App-Sign", modelParam.getModelSecret());
            paramMap.put("seqid", request.getMessageId());
            paramMap.put("timestamp", new Date().getTime());
            paramMap.put("system_ask_text", getLastAssistantContent(request));
            paramMap.put("input_text", request.getQuery());
            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(paramMap,headers);
            log.info("【实体识别】【{}】肯否小模型实体识别请求参数：{}", abilityCode(), JsonUtils.toJsonString(httpEntity));
            ResponseEntity<String> responseEntity = restNoBalancedTemplate.exchange(
                    modelParam.getExternalModelUrl(),
                    HttpMethod.POST,
                    httpEntity,
                    String.class);
            rpcResult = responseEntity.getBody();
            log.info("【实体识别】【{}】肯否小模型实体识别返回：{}",abilityCode(), rpcResult);
        }catch (Exception e){
            logStatusEnum = LogStatusEnum.FAILED;
            log.error("肯否识别异常！", e);
        }finally {
            sendTraceLog("肯否识别", LogTypeEnum.KENFOU_REG,paramMap, rpcResult, logStatusEnum, modelParam, startTime, LocalDateTime.now());
        }
        JSONObject modelResult = JSON.parseObject(rpcResult);
        return convertToEntity(modelParam, modelResult);
    }

    /**
     * 获取最近一次机器人内容
     * @param request
     * @return
     */
    private String getLastAssistantContent(EntityRecognitionRequest request){
        if(StringUtils.isNotBlank(request.getLastSystemAsk())){
            return request.getLastSystemAsk();
        }
        //如果传了历史消息，取非坐席或机器人的最后一条消息
        if(CollectionUtils.isNotEmpty(request.getHistoryMessages())){
            for(int i= request.getHistoryMessages().size()-1;i>=0;i--){
                SimpleChatLog simpleChatLog = request.getHistoryMessages().get(i);
                if(MessageRoleEnum.assistant.getCode().equalsIgnoreCase(simpleChatLog.getRole()) || MessageRoleEnum.seat.getCode().equalsIgnoreCase(simpleChatLog.getRole())){
                    return simpleChatLog.getContent();
                }
            }
        }
        log.info("采用上文SystemAsk进行识别");
        String assistantContent = null;
        List<WrapLLMMessage> llmMessages = sessionHistoryCacheService.getChatHistoryFromCache(request.getSessionId(), request.getMessageId(), 5,true,false);
        if(CollectionUtils.isNotEmpty(llmMessages)){
            for(WrapLLMMessage llmMessage : llmMessages){
                if(MessageRoleEnum.assistant.getCode().equalsIgnoreCase(llmMessage.getRole()) && StringUtils.isNotBlank(llmMessage.getContent())){
                    assistantContent = llmMessage.getContent();
                }
            }
        }
        return assistantContent;
    }

    /**
     * 转换成实体map
     * @param modelResult
     * @return
     */
    private Entity convertToEntity(ModelPageListParam modelParam,JSONObject modelResult){
        if(Objects.isNull(modelResult) || !modelResult.containsKey("status")
                || modelResult.getInteger("status") != 0 || !modelResult.containsKey("result")){
            return null;
        }
        JSONObject entityResult = modelResult.getJSONObject("result");
        if(Objects.isNull(entityResult) || !entityResult.containsKey("label")){
            return null;
        }
        String value = entityResult.getString("label");
        if("无关".equalsIgnoreCase(value)){
            return null;
        }
        Double score = entityResult.getDouble("score");
        if(Objects.nonNull(modelParam.getThreshold()) && score < modelParam.getThreshold()){
            //不满足阈值，直接返回
            return null;
        }
        Entity entity = new Entity();
        EntityContent entityContent = new EntityContent();
        entityContent.setValue("肯定".equals(value) ? "是" : "否");
        entityContent.setRawValue(value);
        entity.setEntityContents(Arrays.asList(entityContent));
        return entity;
    }
}
