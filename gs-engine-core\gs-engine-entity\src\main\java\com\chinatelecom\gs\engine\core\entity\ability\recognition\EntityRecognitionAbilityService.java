package com.chinatelecom.gs.engine.core.entity.ability.recognition;

import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;

import java.util.List;

/**
 * @USER: pengmc1
 * @DATE: 2025/1/16 19:27
 */
public interface EntityRecognitionAbilityService {
    /**
     * 识别能力标识
     * @return
     */
    String abilityCode();

    /**
     * 实体识别
     * @param request
     * @return
     */
    List<Entity> predict(EntityRecognitionRequest request);
}
