package com.chinatelecom.gs.engine.core.entity.common.trie;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @USER: pengmc1
 * @DATE: 2023/5/9 18:12
 */

@Data
public class TrieNode<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 7120615241517967514L;

    /** 字 */
    private Character character;

    /** 词 */
    private String word;

    /** 深度 */
    private Integer level;

    /**下一节点信息*/
    private Map<Character, TrieNode<T>> next = new HashMap<>();

    /**
     * 关联数据Map,可能是实体，可能是知识点
     */
    private Map<String,T> dataMap;

    TrieNode(){
    }

    TrieNode(Character character){
        this.character = character;
    }
}
