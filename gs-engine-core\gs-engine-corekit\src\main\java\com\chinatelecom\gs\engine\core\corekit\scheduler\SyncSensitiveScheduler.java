package com.chinatelecom.gs.engine.core.corekit.scheduler;

import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.core.corekit.service.SensitiveService;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;


/**
 * 敏感词同步器
 */
@Component
@EnableScheduling
public class SyncSensitiveScheduler {

    @Resource
    private SensitiveService sensitiveService;

    @Scheduled(cron="0 0/1 * * * ?") // 1分钟执行一次
    public void syncSensitiveData() {
        //设置请求上下文，绕过租户检查
        try{
            RequestInfo requestInfo = new RequestInfo();
            requestInfo.setTenant(false);
            RequestContext.set(requestInfo);
            sensitiveService.syncSensitiveData();
        }finally {
            RequestContext.remove();
        }
    }
}
