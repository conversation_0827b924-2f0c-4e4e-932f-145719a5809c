package com.chinatelecom.gs.engine.core.manager.vo.config.base;


import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BaseConfigDetailQueryParam {

    /**
     * 配置维度
     */
    private DimensionEnum dimension;

    /**
     * 业务配置场景
     */
    @NotBlank(message = "配置类型不能为空")
    private String configType;


    /**
     * 业务场景配置唯一标识
     */
    private String businessNo;
}
