package com.chinatelecom.gs.engine.core.corekit.domain.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinatelecom.gs.engine.common.infra.base.BaseEntity;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @USER: pengmc1
 * @DATE: 2025/7/21 10:58
 */

@Data
@TableName("gs_form")
public class FormPO extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 7949298227332443791L;
    /**
     * 表单编码
     */
    @TableField("form_code")
    private String formCode;
    /**
     * 表单名称
     */
    @TableField("form_name")
    private String formName;
    /**
     * 表单描述
     */
    @TableField("form_desc")
    private String formDesc;
    /**
     * 表单内容
     */
    @TableField("form_data")
    private String formData;

    /**
     * 表单状态
     */
    @TableField("status")
    private String status;
    /**
     * 版本号
     */
    @TableField("version")
    private Long version;

    /**
     * 创建来源
     */
    @TableField("source")
    private String source;
}
