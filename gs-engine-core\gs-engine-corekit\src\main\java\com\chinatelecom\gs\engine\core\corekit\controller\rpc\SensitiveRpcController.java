package com.chinatelecom.gs.engine.core.corekit.controller.rpc;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.core.corekit.service.SensitiveService;
import com.chinatelecom.gs.engine.core.sdk.request.MatchSensitiveRequest;
import com.chinatelecom.gs.engine.core.sdk.rpc.SensitiveRpcApi;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import java.util.List;

/**
 * @author: Wei
 * @date: 2025-02-05 09:24
 */
@Slf4j
@RestController
@RequestMapping(Apis.BASE_PREFIX + Apis.RPC_PREFIX + "/sensitive")
public class SensitiveRpcController implements SensitiveRpcApi {

    @Resource
    private SensitiveService sensitiveService;

    /**
     * 敏感词识别
     * @param request
     * @return
     */
    @Override
    @PostMapping("/matchSensitiveValue")
    @AuditLog(businessType = "敏感词识别", operType = "敏感词识别", operDesc = "敏感词识别", objId="#request.content")
    public Result<List<String>> matchSensitiveValue(@RequestBody @Valid MatchSensitiveRequest request){
        return Result.success(sensitiveService.matchSensitiveValue(request.getContent()));
    }

}
