package com.chinatelecom.gs.engine.core.manager.search.dto;

import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.core.manager.search.AnalyzerTypeEnum;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 搜索分析器配置创建参数
 */
@Data
public class SearchAnalyzerConfigCreateParam {
    private String code;
    @NotNull(message = "分析器类型不能为空")
    private AnalyzerTypeEnum analyzerType;
    @NotNull(message = "维度不能为空")
    private DimensionEnum dimension;
    @NotBlank(message = "配置值不能为空")
    private String configValue;
    private String description;
    private String businessNo;
}
