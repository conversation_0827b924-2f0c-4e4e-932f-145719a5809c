package com.chinatelecom.gs.engine.core.manager.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.common.utils.IPageUtils;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.core.manager.convert.BaseDtoAndPoConvert;
import com.chinatelecom.gs.engine.core.manager.convert.ConfigConvert;
import com.chinatelecom.gs.engine.core.manager.dto.BaseConfigDTO;
import com.chinatelecom.gs.engine.core.manager.infra.po.BaseConfigPO;
import com.chinatelecom.gs.engine.core.manager.param.CodeParam;
import com.chinatelecom.gs.engine.core.manager.repository.BaseConfigRepository;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.manager.vo.config.*;
import com.chinatelecom.gs.engine.core.manager.vo.config.base.BaseConfigCreateParam;
import com.chinatelecom.gs.engine.core.manager.vo.config.base.BaseConfigQueryParam;
import com.chinatelecom.gs.engine.core.manager.vo.config.base.BaseConfigUpdateParam;
import com.chinatelecom.gs.engine.core.manager.vo.config.base.BaseConfigVO;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月28日
 */
@Service
@Validated
@Slf4j
public class BaseConfigAppServiceImpl implements BaseConfigAppService {

    @Resource
    private BaseConfigRepository repository;

    @Resource
    private ConfigConvert configConvert;

    private static final Map<String, DimensionEnum> configDefaultDimension = new HashMap<>();
    private static final Map<String, Object> configDefault = new HashMap<>();


    static {
        configDefaultDimension.put(ConfigConvert.GLOBAL_CONFIG, DimensionEnum.TENANT);
        configDefaultDimension.put(ConfigConvert.PUBLISH_CONFIG, DimensionEnum.TENANT);
        configDefaultDimension.put(ConfigConvert.SPEECH_CONFIG, DimensionEnum.TENANT);
        configDefaultDimension.put(ConfigConvert.SENSITIVE_CONFIG, DimensionEnum.TENANT);
        configDefaultDimension.put(ConfigConvert.SAFE_FENCE_CONFIG, DimensionEnum.TENANT);
        configDefaultDimension.put(ConfigConvert.INTERNAL_CONFIG, DimensionEnum.SYSTEM);
        configDefaultDimension.put(ConfigConvert.LLM_CHAT_HINT_CONFIG, DimensionEnum.SYSTEM);
        configDefaultDimension.put(ConfigConvert.KNWL_FILE_LENGTH_SCAN_TASK_CONFIG, DimensionEnum.SYSTEM);

        configDefault.put(ConfigConvert.GLOBAL_CONFIG, null);
        configDefault.put(ConfigConvert.PUBLISH_CONFIG, new PublishConfig(false, false,true, null, false));
        configDefault.put(ConfigConvert.SPEECH_CONFIG, new SpeechConfig(true, true));
        configDefault.put(ConfigConvert.SENSITIVE_CONFIG, new SensitiveConfig(true));
        configDefault.put(ConfigConvert.SAFE_FENCE_CONFIG, new SafeFenceMenuConfig(false));
        configDefault.put(ConfigConvert.INTERNAL_CONFIG, InternalFunctionConfig.defaultConfig());
        configDefault.put(ConfigConvert.LLM_CHAT_HINT_CONFIG, new LLMChatHint());
        configDefault.put(ConfigConvert.KNWL_FILE_LENGTH_SCAN_TASK_CONFIG, new KmsFileLengthTaskConfig());
    }

    protected BaseDtoAndPoConvert converter() {
        return BaseDtoAndPoConvert.INSTANCE;
    }

    protected Wrapper<BaseConfigPO> pageQueryWrapper(BaseConfigQueryParam query) {
        LambdaQueryWrapper<BaseConfigPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(StringUtils.isNotBlank(query.getName()), BaseConfigPO::getName, query.getName());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(query.getConfigType()), BaseConfigPO::getConfigType, query.getConfigType());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(query.getBusinessNo()), BaseConfigPO::getBusinessNo, query.getBusinessNo());
        return lambdaQueryWrapper;
    }


    @Override
    public  <T> T getConfigOrDefault(String configType, String businessNo) {
        // 配置优先级
        DimensionEnum[] dimensions = {DimensionEnum.BUSINESS, DimensionEnum.TENANT, DimensionEnum.SYSTEM};

//        String businessNo = query.getBusinessNo();

        List<BaseConfigDTO> configList = repository.selectByType(configType);

        if (configList == null || configList.isEmpty()) {
            // 未配置 默认兜底
            log.debug("未找到配置，使用兜底配置，配置类型: {}, businessNo: {}", configType, businessNo);
            return (T) configDefault.getOrDefault(configType, null);
        }

        // 1. 优先匹配 businessNo 和维度
        Optional<BaseConfigDTO> selectedConfigDto = findConfigByBusinessNoAndDimension(configList, businessNo, dimensions);

        if (selectedConfigDto.isPresent()) {
            return (T) getConfigObject(selectedConfigDto.get(), configType);
        }

        // 2. 匹配 SYSTEM 维度 (不限制 BusinessNo)
        Optional<BaseConfigDTO> systemConfigDto = configList.stream().filter(config -> DimensionEnum.SYSTEM.equals(config.getDimension())).findFirst();

        if (systemConfigDto.isPresent()) {
            log.debug("找到SYSTEM维度配置，配置类型: {}, businessNo: {}", configType, businessNo);
            return (T) getConfigObject(systemConfigDto.get(), configType);
        }


        // 3. 都没找到，使用兜底配置
        log.debug("未找到匹配配置，使用兜底配置，配置类型: {}, businessNo: {}", configType, businessNo);
        return (T) configDefault.getOrDefault(configType, null);
    }

    @Override
    public Map<String, Object> getConfigsOrDefault(List<String> configTypes, String businessNo) {
        Map<String, Object> result = new HashMap<>(10);
        // 配置优先级
        DimensionEnum[] dimensions = {DimensionEnum.BUSINESS, DimensionEnum.TENANT, DimensionEnum.SYSTEM};

        //查询指定租户和系统级别配置
        ArrayList<String> businessNos = Lists.newArrayList(businessNo, "SYSTEM");
        List<BaseConfigDTO> configList = repository.selectByTypesAndNos(configTypes, businessNos);

        //配置为空 使用默认配置
        if(CollectionUtils.isEmpty(configList)){
            configTypes.forEach(configType ->{
                result.put(configType, configDefault.getOrDefault(configType, null));
            });
            return result;
        }

        // 根据configType分组
        Map<String, List<BaseConfigDTO>> configMap = configList.stream().collect(Collectors.groupingBy(BaseConfigDTO::getConfigType));
        configTypes.forEach(configType ->{
            //配置为空 使用默认配置
            result.put(configType, getConfig(configMap.get(configType), configType, businessNo));
        });

        return result;
    }

    private Object getConfig(List<BaseConfigDTO> configList, String configType, String businessNo){
        // 配置优先级
        DimensionEnum[] dimensions = {DimensionEnum.BUSINESS, DimensionEnum.TENANT, DimensionEnum.SYSTEM};
        if (configList == null || configList.isEmpty()) {
            // 未配置 默认兜底
            log.debug("未找到配置，使用兜底配置，配置类型: {}, businessNo: {}", configType, businessNo);
            return configDefault.getOrDefault(configType, null);
        }

        // 1. 优先匹配 businessNo 和维度
        Optional<BaseConfigDTO> selectedConfigDto = findConfigByBusinessNoAndDimension(configList, businessNo, dimensions);

        if (selectedConfigDto.isPresent()) {
            return getConfigObject(selectedConfigDto.get(), configType);
        }

        // 2. 匹配 SYSTEM 维度 (不限制 BusinessNo)
        Optional<BaseConfigDTO> systemConfigDto = configList.stream().filter(config -> DimensionEnum.SYSTEM.equals(config.getDimension())).findFirst();

        if (systemConfigDto.isPresent()) {
            log.debug("找到SYSTEM维度配置，配置类型: {}, businessNo: {}", configType, businessNo);
            return getConfigObject(systemConfigDto.get(), configType);
        }


        // 3. 都没找到，使用兜底配置
        log.debug("未找到匹配配置，使用兜底配置，配置类型: {}, businessNo: {}", configType, businessNo);
        return configDefault.getOrDefault(configType, null);
    }

    private Optional<BaseConfigDTO> findConfigByBusinessNoAndDimension(List<BaseConfigDTO> configList, String businessNo, DimensionEnum[] dimensions) {

        return Arrays.stream(dimensions)
                .flatMap(dimension -> configList.stream()
                        .filter(config -> dimension.equals(config.getDimension()) &&
                                (businessNo == null || businessNo.isEmpty() || Objects.equals(businessNo, config.getBusinessNo())))
                        .findFirst()
                        .map(Stream::of).orElseGet(Stream::empty))
                .findFirst();
    }


    private Object getConfigObject(BaseConfigDTO configDto, String configType) {
        if (configConvert == null) {
            log.error("configConvert is null,配置类型：{}, configDto :{}", configType, configDto);
            return null;
        }
        try {
            Object configObject = configConvert.getConfigObjectAndValidate(configDto);
            log.debug("成功获取配置，配置类型: {}, 配置内容：{}", configType, configObject);
            return configObject;
        } catch (Exception e) {
            log.error("配置转换失败,配置类型：{}, configDto :{}, 错误信息:{}", configType, configDto, e.getMessage(), e);
            return null; // 转换失败，返回null或抛出异常
        }
    }

    @Override
    public Page<BaseConfigVO> pageQuery(BaseConfigQueryParam query) {
        IPage<BaseConfigDTO> page = new PageDTO<>(query.getPageNum(), query.getPageSize());
        Wrapper<BaseConfigPO> queryWrapper = pageQueryWrapper(query);
        page = repository.page(page, queryWrapper);
        return IPageUtils.convert(page, dto -> converter().convertVO(dto));
    }

    @Override
    public BaseConfigVO createOrUpdate(BaseConfigCreateParam createParam) {
        BaseConfigDTO dto = converter().convertCreate(createParam);
        BaseConfigQueryParam queryParam = BaseConfigQueryParam.builder()
                .businessNo(dto.getBusinessNo())
                .configType(dto.getConfigType())
                .dimension(dto.getDimension())
                .build();
        configConvert.getConfigObjectAndValidate(dto);
        BaseConfigDTO record = repository.selectByTypeAndNoAndDimension(queryParam);
        if (record == null) {
            dto.setCode(IdGenerator.getId(StringUtils.EMPTY));
            repository.save(dto);
            return converter().convertVO(dto);
        }
        record.setConfigData(dto.getConfigData());
        record.setDescription(dto.getDescription());
        if (StringUtils.isNotBlank(dto.getName())) {
            record.setName(dto.getName());
        }
        repository.saveOrUpdate(record);
        return converter().convertVO(dto);
    }

    @Override
    public boolean update(String code, BaseConfigUpdateParam updateParam) {
        BaseConfigDTO dto = converter().convertUpdate(updateParam);
        dto.setId(null);
        return repository.updateOne(dto, Wrappers.<BaseConfigPO>lambdaUpdate().eq(BaseConfigPO::getCode, code));
    }

    @Override
    public boolean delete(CodeParam codes) {
        return repository.remove(Wrappers.<BaseConfigPO>lambdaUpdate().in(BaseConfigPO::getCode, codes.getCodes()));
    }

}
