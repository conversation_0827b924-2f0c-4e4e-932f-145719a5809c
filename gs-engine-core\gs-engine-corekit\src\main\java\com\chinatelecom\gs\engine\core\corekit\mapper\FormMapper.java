package com.chinatelecom.gs.engine.core.corekit.mapper;

import com.chinatelecom.gs.engine.common.infra.base.BaseExtendMapper;
import com.chinatelecom.gs.engine.core.corekit.domain.po.FormPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @USER: pengmc1
 * @DATE: 2025/7/21 11:13
 */

@Mapper
public interface FormMapper extends BaseExtendMapper<FormPO> {
    /**
     * 查询表单列表
     * @param formCodes 表单编码列表
     * @param status  表单状态
     * @param source 来源
     * @return
     */
    List<FormPO> queryFormList(@Param("formCodes") List<String> formCodes, @Param("status") String status, @Param("source") String source);
}
