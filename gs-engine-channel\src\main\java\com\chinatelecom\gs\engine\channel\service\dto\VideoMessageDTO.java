package com.chinatelecom.gs.engine.channel.service.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/19 15:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VideoMessageDTO extends BaseSendMessageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -9121710566131617475L;

    private JSONObject video;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
