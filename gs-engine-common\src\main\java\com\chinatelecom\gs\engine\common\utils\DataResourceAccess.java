package com.chinatelecom.gs.engine.common.utils;

import com.alibaba.fastjson2.JSONObject;
import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.platform.client.config.PlatformSsoProperties;
import com.chinatelecom.cloud.platform.client.enums.ResourceLevelEnum;
import com.chinatelecom.cloud.platform.client.model.req.*;
import com.chinatelecom.cloud.platform.client.model.res.DataResourceAuthResponse;
import com.chinatelecom.cloud.platform.client.model.res.DataResourceDetailResponse;
import com.chinatelecom.cloud.platform.client.util.DataResourceAuthUtils;
import com.chinatelecom.cloud.platform.client.util.DataResourceUtil;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class DataResourceAccess {

    @Resource
    private PlatformSsoProperties platformSsoProps;

    /**
     * 构建批量创建数据资源请求
     *
     * @param resourceCode String
     * @param resourceName String
     * @param resourceType String
     * @return DataResourceBatchCreateRequest
     */
    public DataResourceBatchCreateRequest buildBatchDataResourceRequest(String resourceCode,
                                                                        String resourceName, String resourceType) {
        List<DataResourceBaseRequest> resources = new ArrayList<>();
        resources.add(DataResourceBaseRequest.builder()
                .resourceCode(resourceCode).resourceName(resourceName)
                .resourceLevel(ResourceLevelEnum.CONFIDENTIAL.getValue()).resourceOwner(RequestContext.getUserId())
                .resourceType(resourceType).resourceTypeName("业务类型").build());
        return DataResourceBatchCreateRequest.builder()
                .appCode(platformSsoProps.getAppCode()).appSecret(platformSsoProps.getAppSecret())
                .corpCode(RequestContext.getTenantId()).userId(RequestContext.getUserId())
                .resources(resources).build();
    }


    /**
     * 创建或更新资源信息
     *
     * @param request DataResourceBatchCreateRequest
     * @return List<DataResourceDetailResponse>
     */
    public List<DataResourceDetailResponse> createOrUpdate(DataResourceBatchCreateRequest request) {
        request.setAppCode(platformSsoProps.getAppCode());
        request.setAppSecret(platformSsoProps.getAppSecret());
        request.setCorpCode(RequestContext.getTenantId());
        request.setUserId(RequestContext.getUserId());

        BaseResult<List<DataResourceDetailResponse>> result = DataResourceUtil
                .createOrUpdate(request);
        if (Objects.isNull(result)) {
            return Collections.emptyList();
        }
        if (!result.ifSuccess()) {
            log.error("try to create or update resource failed, code:{}, msg:{}, request:{}",
                    result.getCode(),
                    result.getMsg(),
                    JSONObject.toJSONString(request)
            );
            throw new BizException("CA003", "创建或修改资源失败：" + result.getMsg());
        }
        return result.getData();
    }

    /**
     * 授予资源权限
     *
     * @param request ResourceAuthCreateReq
     */
    public void grantResourceAuth(ResourceAuthCreateReq request) {
        request.setAppCode(platformSsoProps.getAppCode());
        request.setAppSecret(platformSsoProps.getAppSecret());
        request.setCorpCode(RequestContext.getTenantId());
        request.setUserId(RequestContext.getUserId());
        BaseResult<Boolean> result = DataResourceAuthUtils.batchCreate(request);
        if (Objects.isNull(result)) {
            throw new BizException("CA004", "资源授权失败");
        }
        if (!result.ifSuccess()) {
            log.error("try to get grant resource auth failed, code:{}, msg:{}",
                    result.getCode(),
                    result.getMsg()
            );
            throw new BizException("CA004", "资源授权失败：" + result.getMsg());
        }
    }

    public Set<String> getAuthedResourceCodesByType(String type) {
        return getAuthedResourceCodesByType(RequestContext.getTenantId(), RequestContext.getUserId(), type);
    }

    public Set<String> getAuthedResourceCodesByType(List<String> typeList) {
        Set<String> result = new HashSet<>();
        if (CollectionUtils.isNotEmpty(typeList)) {
            for (String type : typeList) {
                Set<String> resourceCodes = getAuthedResourceCodesByType(RequestContext.getTenantId(), RequestContext.getUserId(), type);
                if (CollectionUtils.isNotEmpty(resourceCodes)) {
                    result.addAll(resourceCodes);
                }
            }
        }
        return result;
    }

    /**
     * 获取授权资源列表
     *
     * @param corpCode String
     * @param userId   String
     * @param type     String
     * @return Set<String>
     */
    public Set<String> getAuthedResourceCodesByType(String corpCode, String userId, String type) {
        ResourceAuthComplexQueryRequest request = new ResourceAuthComplexQueryRequest();
        request.setAppCode(platformSsoProps.getAppCode());
        request.setAppSecret(platformSsoProps.getAppSecret());
        request.setCorpCode(corpCode);
        request.setUserId(userId);
        request.setResourceType(type);
        BaseResult<Set<String>> result;
        try {
            result = DataResourceAuthUtils.getAuthedResourceCodes(request);
        } catch (Exception e) {
            log.error("failed to get auth resource info from cloud platform.", e);
            throw new BizException("CA005", "查询授权资源列表失败");
        }
        if (!result.ifSuccess()) {
            log.error("try to get checked resource failed, result:{}, type:{}", result, type);
            throw new BizException("CA005", "查询授权资源列表失败：" + result.getMsg());
        }

        if (CollectionUtils.isEmpty(result.getData())) {
            return new HashSet<>();
        }
        return Objects.isNull(result.getData()) ? new HashSet<>() : result.getData();
    }

    /**
     * 获取数据资源
     *
     * @param resourceCode String
     * @param corpCode     String
     * @param resourceType String
     * @param userId       String
     * @return DataResourceAuthResponse
     */
    public DataResourceAuthResponse getResourceData(String resourceCode, String corpCode,
                                                    String resourceType, String userId) {
        ResourceAuthComplexQueryRequest request = new ResourceAuthComplexQueryRequest();
        request.setAppCode(platformSsoProps.getAppCode());
        request.setAppSecret(platformSsoProps.getAppSecret());
        request.setResourceCodes(Collections.singleton(resourceCode));
        request.setCorpCode(corpCode);
        request.setUserId(userId);
        request.setResourceType(resourceType);
        BaseResult<List<DataResourceAuthResponse>> result;
        try {
            result = DataResourceAuthUtils.complexQuery(request);
        } catch (Exception e) {
            log.error("failed to get auth resource info from cloud platform.", e);
            throw new BizException("CA005", "查询授权资源列表失败");
        }

        if (Objects.isNull(result)) {
            throw new BizException("CA006", "查询资源详情失败");
        }

        if (!result.ifSuccess()) {
            log.error("try to get checked resource failed, code:{}, msg:{}, type:{}",
                    result.getCode(),
                    result.getMsg(),
                    resourceType
            );
            throw new BizException("CA005", "查询授权资源列表失败：" + result.getMsg());
        }

        if (result.getCode() != 1) {
            throw new BizException("CA006", "查询资源详情失败" + result.getMsg());
        }

        if (CollectionUtils.isEmpty(result.getData())) {
            throw new BizException("CA007", "数据资源不存在: " + resourceCode);
        }

        return result.getData().get(0);
    }

    /**
     * 删除数据资源
     *
     * @param resourceId Long
     * @param corpCode   String
     * @param userId     String
     * @return Boolean
     */
    public Boolean deleteResource(Long resourceId, String corpCode, String userId) {
        DataResourceDeleteRequest request = new DataResourceDeleteRequest();
        request.setAppCode(platformSsoProps.getAppCode());
        request.setAppSecret(platformSsoProps.getAppSecret());
        request.setId(resourceId);
        request.setUserId(userId);
        request.setCorpCode(corpCode);
        BaseResult<Boolean> resp = DataResourceUtil.delete(request);

        if (resp == null) {
            throw new BizException("CA008", "删除数据资源失败");
        }

        if (resp.getCode() != 1) {
            throw new BizException("CA008", "删除数据资源失败" + resp.getMsg());
        }

        return resp.getData();
    }


}
