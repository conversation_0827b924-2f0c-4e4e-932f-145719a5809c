package com.chinatelecom.gs.engine.common.platform;

import lombok.Getter;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 使用 ApplicationContext 获取所有 RequestMappingInfo 和 HandlerMethod 的映射关系。
 * 遍历这些映射，找到标记了 @StatOpenApi 的方法。
 * 检查对应 URL 是否包含 "openapi"。
 * 如果满足条件，则将接口名（格式为：分组名称-接口名称）保存到列表中。
 * @date 2025年08月15日
 */
@Component
public class StatOpenApiCollector implements ApplicationRunner {

    @Resource
    private RequestMappingHandlerMapping requestMappingHandlerMapping;

    @Getter
    private final List<String> openApiList = new ArrayList<>();


    @Override
    public void run(ApplicationArguments args) throws Exception {
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet()) {
            HandlerMethod handlerMethod = entry.getValue();
            StatOpenApi statOpenApi = handlerMethod.getMethodAnnotation(StatOpenApi.class);
            if (statOpenApi != null) {
                RequestMappingInfo requestMappingInfo = entry.getKey();
                // 检查URL是否包含"openapi"
                Set<String> patterns = requestMappingInfo.getPatternsCondition().getPatterns();
                if (patterns.stream().anyMatch(pattern -> pattern.contains("openapi"))) {
                    String interfaceName = statOpenApi.groupName() + "-" + statOpenApi.name();
                    openApiList.add(interfaceName);
                }
            }
        }
    }

}