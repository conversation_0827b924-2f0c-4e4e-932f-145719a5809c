package com.chinatelecom.gs.engine.common.platform;

import com.chinatelelcom.gs.engine.sdk.common.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 政务壳子查询权限client
 * @date 2025年07月29日
 */

@FeignClient(name = "governmentAuthClient", url = "${platform.client.governmentHost:}")
public interface GovernmentAuthClient {
    /**
     * 查询是否管理员
     *
     * @param request AppOwnerRequest
     * @return Boolean
     */
    @PostMapping("/ais/government/rpc/isAppOwner")
    Result<Boolean> isAppOwner(@Valid @RequestBody AppOwnerRequest request);

    /**
     * 查询用户授权菜单
     *
     * @param request AppOwnerRequest
     * @return List<String>
     */
    @PostMapping("/ais/government/rpc/getMenuList")
    Result<List<String>> getMenuList(@Valid @RequestBody AppOwnerRequest request);

    /**
     * 查询用户授权资源
     *
     * @param request AppOwnerRequest
     * @return List<String>
     */
    @PostMapping("/ais/government/rpc/getResourceList")
    Result<List<String>> getResourceList(@Valid @RequestBody AppOwnerRequest request);
}
