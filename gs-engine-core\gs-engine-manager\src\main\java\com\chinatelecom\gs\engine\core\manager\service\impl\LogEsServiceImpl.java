package com.chinatelecom.gs.engine.core.manager.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.chinatelecom.gs.engine.common.log.track.LogEsPO;
import com.chinatelecom.gs.engine.common.log.track.LogMessage;
import com.chinatelecom.gs.engine.common.utils.EsPrefixUtils;
import com.chinatelecom.gs.engine.core.manager.service.LogEsService;
import com.chinatelecom.gs.engine.core.manager.vo.LogVO;
import com.chinatelecom.gs.engine.robot.sdk.constant.CommonConstant;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.convert.ElasticsearchConverter;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.ByQueryResponse;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
import org.springframework.data.elasticsearch.core.query.DeleteQuery;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @USER: pengmc1
 * @DATE: 2025/5/6 19:38
 */

@Service
@Slf4j
public class LogEsServiceImpl implements LogEsService {

    @Resource
    private ElasticsearchOperations elasticsearchOperations;

    @Resource
    private ElasticsearchConverter elasticsearchConverter;

    @Override
    public void saveLog(LogMessage logMessage) {
        try {
            LogEsPO logEsPO = new LogEsPO();
            BeanUtil.copyProperties(logMessage, logEsPO);
            elasticsearchOperations.save(logEsPO, IndexCoordinates.of(EsPrefixUtils.buildByPrefix(CommonConstant.ES_LOG_MESSAGE_INDEX_NAME)));
        } catch (RuntimeException e) {
            if (!e.getMessage().contains("Unable to parse response body")) {
                throw e;
            }
        }
    }

    /**
     * 根据 sendTime 范围删除日志
     *
     * @param startTime 开始时间（包含）
     * @param endTime   结束时间（包含）
     */
    public Boolean deleteLogsBySendTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            throw new BizException("AA089", "开始时间和结束时间必须提供");
        }

        // 构建 range 查询：sendTime 在 [startTime, endTime] 之间
        String startTimeStr = startTime.format(DateTimeFormatter.ofPattern(CommonConstant.MONITOR_DATE_PATTERN));
        String endTimeStr = endTime.format(DateTimeFormatter.ofPattern(CommonConstant.MONITOR_DATE_PATTERN));

        Criteria criteria = new Criteria("sendTime.keyword")
                .greaterThanEqual(startTimeStr)
                .lessThanEqual(endTimeStr);
        Query query = new CriteriaQuery(criteria);

        long count = elasticsearchOperations.count(query, LogEsPO.class,
                IndexCoordinates.of(EsPrefixUtils.buildByPrefix(CommonConstant.ES_LOG_MESSAGE_INDEX_NAME)));
        log.debug("匹配到{}条待删除文档", count);

        // 执行 Delete By Query
        DeleteQuery deleteQuery = DeleteQuery.builder(query).build();
        ByQueryResponse response = elasticsearchOperations.delete(deleteQuery, LogEsPO.class, IndexCoordinates.of(EsPrefixUtils.buildByPrefix(CommonConstant.ES_LOG_MESSAGE_INDEX_NAME)));
        return response.getDeleted() > 0;
    }

    /**
     * 根据 traceId 查询日志
     *
     * @param traceId 唯一标识请求链路的日志 ID
     * @return 匹配的日志列表
     */
    public List<LogVO> searchLogsByTraceId(String traceId) {
        if (traceId == null || traceId.isEmpty()) {
            throw new BizException("AA089", "traceId 必须提供");
        }

        Criteria criteria = new Criteria("traceId").is(traceId);
        CriteriaQuery query = new CriteriaQuery(criteria);

        return elasticsearchOperations.search(query, LogMessage.class, IndexCoordinates.of(EsPrefixUtils.buildByPrefix(CommonConstant.ES_LOG_MESSAGE_INDEX_NAME)))
                .getSearchHits()
                .stream()
                .map(o -> {
                    LogMessage message = o.getContent();
                    LogVO logMessageVO = new LogVO();
                    BeanUtil.copyProperties(message, logMessageVO);
                    return logMessageVO;
                })
                .collect(Collectors.toList());
    }

    public List<LogVO> searchLogsByMessageId(String messageId) {
        if (messageId == null || messageId.isEmpty()) {
            throw new BizException("AA089", "messageId 必须提供");
        }

        Criteria criteria = new Criteria("messageId").is(messageId);
        CriteriaQuery query = new CriteriaQuery(criteria);

        return elasticsearchOperations.search(query, LogMessage.class, IndexCoordinates.of(EsPrefixUtils.buildByPrefix(CommonConstant.ES_LOG_MESSAGE_INDEX_NAME)))
                .getSearchHits()
                .stream()
                .map(o -> {
                    LogMessage message = o.getContent();
                    LogVO logMessageVO = new LogVO();
                    BeanUtil.copyProperties(message, logMessageVO);
                    return logMessageVO;
                })
                .collect(Collectors.toList());
    }
}
