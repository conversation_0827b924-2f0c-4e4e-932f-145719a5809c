package com.chinatelecom.gs.engine.core.manager.param;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * 开始分片上传参数
 *
 * @author: Wei
 * @date: 2025-04-22 10:25
 */
@Data
public class UploadStartParam {

    @NotEmpty(message = "文件名称不能为空")
    private String name;

    @NotNull(message = "文件总分片数不能为空")
    private Integer totalPartNum;

    @NotNull(message = "分片大小不能为空")
    private Integer partSize;

    @NotNull(message = "总大小不能为空")
    private Long totalSize;

    @NotEmpty(message = "文件MD5不能为空")
    private String md5;

}
