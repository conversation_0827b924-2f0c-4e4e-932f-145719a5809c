package com.chinatelecom.gs.engine.core.entity.api;

import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.core.entity.service.ParamRecognitionService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.ParamRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.ParamRecognitionResponse;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * @USER: pengmc1
 * @DATE: 2025/8/5 11:00
 */

@Slf4j
@RestController
@RequestMapping(Apis.BASE_PREFIX + Apis.RPC_PREFIX + "/paramRecognition")
public class ParamRecognitionRpcApiImpl {

    @Resource
    private ParamRecognitionService paramRecognitionService;

    /**
     * 参数识别
     * @param request
     * @return
     */
    @PostMapping("/predict")
    Result<ParamRecognitionResponse> predict(@Valid @RequestBody ParamRecognitionRequest request){
        return Result.success(paramRecognitionService.predict(request));
    }
}
