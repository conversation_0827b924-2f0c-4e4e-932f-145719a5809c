////java gs-engine-kms/src/main/java/com/chinatelecom/gs/engine/kms/config/PlatformKafkaConfiguration.java
//package com.chinatelecom.gs.engine.kms.config;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.kafka.common.config.SaslConfigs;
//import org.springframework.beans.factory.ObjectProvider;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.autoconfigure.kafka.DefaultKafkaConsumerFactoryCustomizer;
//import org.springframework.boot.autoconfigure.kafka.DefaultKafkaProducerFactoryCustomizer;
//import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.boot.context.properties.PropertyMapper;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
//import org.springframework.kafka.core.*;
//import org.springframework.kafka.security.jaas.KafkaJaasLoginModuleInitializer;
//import org.springframework.kafka.support.LoggingProducerListener;
//import org.springframework.kafka.support.ProducerListener;
//import org.springframework.kafka.support.converter.RecordMessageConverter;
//import org.springframework.kafka.transaction.KafkaTransactionManager;
//
//import java.io.IOException;
//
//@Configuration
//@Slf4j
//@EnableConfigurationProperties({PlatformKafkaProperties.class})
//@ConditionalOnProperty(prefix = "platform.kafka", name = "bootstrap-servers")
//public class PlatformKafkaConfiguration extends KafkaAutoConfiguration {
//
//    private final PlatformKafkaProperties properties;
//
//    public PlatformKafkaConfiguration(PlatformKafkaProperties properties) {
//        super(properties);
//        this.properties = properties;
//        String space = properties.getProperties().get("security.space");
//        if (space != null) {
//            System.setProperty("java.security.auth.login.config", "/usr/src/app/telecom/" + space + "/jaas.config");
//            System.setProperty("java.security.krb5.conf", "/usr/src/app/telecom/" + space + "/krb5.conf");
//            if (System.getProperty("java.security.krb5.conf") != null
//                    && System.getProperty("java.security.auth.login.config") != null) {
//                properties.getProperties().put("java.security.krb5.conf",
//                        System.getProperty("java.security.krb5.conf"));
//                properties.getProperties().put("java.security.auth.login.config",
//                        System.getProperty("java.security.auth.login.config"));
//                properties.getProperties().remove(SaslConfigs.SASL_JAAS_CONFIG);
//                properties.getProperties().remove("security.krb5.conf");
//            }
//        }
//    }
//
//    @Override
//    @Bean("platformKafkaTemplate")
//    @ConditionalOnMissingBean({KafkaTemplate.class})
//    @ConditionalOnProperty(prefix = "platform.kafka", name = "bootstrap-servers")
//    public KafkaTemplate<?, ?> kafkaTemplate(ProducerFactory<Object, Object> kafkaProducerFactory,
//                                             ProducerListener<Object, Object> kafkaProducerListener,
//                                             ObjectProvider<RecordMessageConverter> messageConverter) {
//        PropertyMapper map = PropertyMapper.get().alwaysApplyingWhenNonNull();
//        KafkaTemplate<Object, Object> kafkaTemplate = new KafkaTemplate<>(kafkaProducerFactory);
//        messageConverter.ifUnique(kafkaTemplate::setMessageConverter);
//        map.from(kafkaProducerListener).to(kafkaTemplate::setProducerListener);
//        map.from(this.properties.getTemplate().getDefaultTopic()).to(kafkaTemplate::setDefaultTopic);
//        map.from(this.properties.getTemplate().getTransactionIdPrefix()).to(kafkaTemplate::setTransactionIdPrefix);
//        return kafkaTemplate;
//    }
//
//    @Override
//    @Bean
//    @ConditionalOnMissingBean({ProducerListener.class})
//    public LoggingProducerListener<Object, Object> kafkaProducerListener() {
//        return new LoggingProducerListener();
//    }
//
//    @Override
//    @Bean
//    @ConditionalOnMissingBean({ConsumerFactory.class})
//    public DefaultKafkaConsumerFactory<?, ?> kafkaConsumerFactory(
//            ObjectProvider<DefaultKafkaConsumerFactoryCustomizer> customizers) {
//        DefaultKafkaConsumerFactory<Object, Object> factory =
//                new DefaultKafkaConsumerFactory(this.properties.buildConsumerProperties());
//        customizers.orderedStream().forEach((customizer) -> {
//            customizer.customize(factory);
//        });
//        return factory;
//    }
//
//    @Override
//    @Bean
//    @ConditionalOnMissingBean({ProducerFactory.class})
//    public DefaultKafkaProducerFactory<?, ?> kafkaProducerFactory(
//            ObjectProvider<DefaultKafkaProducerFactoryCustomizer> customizers) {
//        DefaultKafkaProducerFactory<?, ?> factory =
//                new DefaultKafkaProducerFactory(this.properties.buildProducerProperties());
//        String transactionIdPrefix = this.properties.getProducer().getTransactionIdPrefix();
//        if (transactionIdPrefix != null) {
//            factory.setTransactionIdPrefix(transactionIdPrefix);
//        }
//
//        customizers.orderedStream().forEach((customizer) -> {
//            customizer.customize(factory);
//        });
//        return factory;
//    }
//
//    @Override
//    @Bean
//    @ConditionalOnProperty(
//            name = {"spring.kafka.producer.transaction-id-prefix"}
//    )
//    @ConditionalOnMissingBean
//    public KafkaTransactionManager<?, ?> kafkaTransactionManager(ProducerFactory<?, ?> producerFactory) {
//        return new KafkaTransactionManager(producerFactory);
//    }
//
//    @Override
//    @Bean
//    @ConditionalOnProperty(
//            name = {"spring.kafka.jaas.enabled"}
//    )
//    @ConditionalOnMissingBean
//    public KafkaJaasLoginModuleInitializer kafkaJaasInitializer() throws IOException {
//        KafkaJaasLoginModuleInitializer jaas = new KafkaJaasLoginModuleInitializer();
//        PlatformKafkaProperties.Jaas jaasProperties = this.properties.getJaas();
//        if (jaasProperties.getControlFlag() != null) {
//            jaas.setControlFlag(jaasProperties.getControlFlag());
//        }
//
//        if (jaasProperties.getLoginModule() != null) {
//            jaas.setLoginModule(jaasProperties.getLoginModule());
//        }
//
//        jaas.setOptions(jaasProperties.getOptions());
//        return jaas;
//    }
//
//    @Override
//    @Bean
//    @ConditionalOnMissingBean
//    public KafkaAdmin kafkaAdmin() {
//        KafkaAdmin kafkaAdmin = new KafkaAdmin(this.properties.buildAdminProperties());
//        kafkaAdmin.setFatalIfBrokerNotAvailable(this.properties.getAdmin().isFailFast());
//        return kafkaAdmin;
//    }
//
//
//    @Bean("platformKafkaListenerContainerFactory")
//    @ConditionalOnProperty(prefix = "platform.kafka", name = "bootstrap-servers")
//    public ConcurrentKafkaListenerContainerFactory<String, String> platformKafkaListenerContainerFactory() {
//        ConcurrentKafkaListenerContainerFactory<String, String> factory =
//                new ConcurrentKafkaListenerContainerFactory<>();
//        factory.setConsumerFactory(platformConsumerFactory());
//        return factory;
//    }
//
//    @Bean
//    @ConditionalOnProperty(prefix = "platform.kafka", name = "bootstrap-servers")
//    public ConsumerFactory<String, String> platformConsumerFactory() {
//        return new DefaultKafkaConsumerFactory<>(properties.buildConsumerProperties());
//    }
//
//
//}