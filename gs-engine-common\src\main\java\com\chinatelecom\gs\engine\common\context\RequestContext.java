package com.chinatelecom.gs.engine.common.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Getter
public class RequestContext {

    private static final TransmittableThreadLocal<RequestInfo> REQUEST_INFO = new TransmittableThreadLocal<>();

    public static void set(RequestInfo info) {
        REQUEST_INFO.set(info);
    }

    public static RequestInfo get() {
        return REQUEST_INFO.get();
    }

    public static RequestInfo getAndCheck() {
        RequestInfo info = get();
        BizAssert.notNull(info, "A0011", "请求上下文参数信息不能为空");
        return info;
    }


    public static void remove() {
        REQUEST_INFO.remove();
    }

    public static void setAppCode(String appCode) {
        getAndCheck().setAppCode(appCode);
    }

    public static String getUserName() {
        String userName = getAndCheck().getUserName();
        return userName;
    }

    public static String getUserId() {
        String userId = getAndCheck().getUserId();
        return userId;
    }

    public static String getTenantId() {
        String tenantId = getAndCheck().getTenantId();
        return tenantId;
    }

    public static String getThirdUserId() {
        String thirdUserId = getAndCheck().getThirdUserId();
        if (!StringUtils.isEmpty(thirdUserId)) {
            return thirdUserId;
        }
        return getUserId();
    }

    public static AppSourceType getAppSourceType() {
        return getAndCheck().getAppSourceType();
    }

    public static RequestSourceType getRequestSourceType() {
        return getAndCheck().getRequestSourceType();
    }

    public static String getAppCode() {
        return getAndCheck().getAppCode();
    }

    public static List<TeamInfo> getTeam() {
        return getAndCheck().getTeam();
    }

    public static List<String> getTeamCodes() {
        List<TeamInfo> teams = getCheckedTeam();
        if (CollectionUtils.isNotEmpty(teams)) {
            return teams.stream().map(TeamInfo::getTeamCode).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }

    public static String getCheckedTenantId() {
        String tenantId = getTenantId();
        Preconditions.checkNotNull(tenantId, "租户不存在");
        return tenantId;
    }

    public static String getCheckedUserId() {
        String userId = getAndCheck().getUserId();
        Preconditions.checkNotNull(userId, "用户ID不存在");
        return userId;
    }

    public static String getCheckedAppCode() {
        String appCode = getAppCode();
        Preconditions.checkNotNull(appCode, "appCode不存在");
        return appCode;
    }

    public static List<TeamInfo> getCheckedTeam() {
        List<TeamInfo> teams = getTeam();
        if (CollectionUtils.isEmpty(teams)) {
            return Lists.newArrayList();
        }
        return teams;
    }

    public static String getSessionId(){
        RequestInfo info = get();
        if(info == null){
            return null;
        }
        return info.getSessionId();
    }
    public static String getMessageId(){
        RequestInfo info = get();
        if(info == null){
            return null;
        }
        return info.getMessageId();
    }
    public static String getLogId(){
        RequestInfo info = get();
        if(info == null){
            return null;
        }
        return info.getLogId();
    }
    public static String getAgentCode(){
        RequestInfo info = get();
        if(info == null){
            return null;
        }
        return info.getAgentCode();
    }
    public static String getEntry(){
        RequestInfo info = get();
        if(info == null){
            return null;
        }
        return info.getEntry();
    }

    public static String getBizName(){
        RequestInfo info = get();
        if(info == null){
            return null;
        }
        return info.getBizName();
    }

    public static void setThirdUserId(String thirdUserId) {
        getAndCheck().setThirdUserId(thirdUserId);
    }

    public static void setUserId(String userId) {
        getAndCheck().setUserId(userId);
    }

    public static boolean checkRole() {
        RequestInfo info = get();
        if(info == null || info.getCheckRole() == null){
            return true;
        }
        return info.getCheckRole();
    }

    public static void setCheckRole(boolean checkRole) {
        getAndCheck().setCheckRole(checkRole);
    }
}
