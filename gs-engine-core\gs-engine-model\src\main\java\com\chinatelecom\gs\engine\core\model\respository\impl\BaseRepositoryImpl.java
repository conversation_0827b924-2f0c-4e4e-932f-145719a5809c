package com.chinatelecom.gs.engine.core.model.respository.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chinatelecom.gs.engine.common.infra.base.BaseConvertor;
import com.chinatelecom.gs.engine.core.model.respository.BaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * BaseRepositoryImpl
 *
 * <AUTHOR>
 * @date 2023-04-06 16:41
 * <p>
 * <M extends BaseMapper<T>, T>
 */
@Slf4j
public class BaseRepositoryImpl<SE extends IService<PO>, PO, DTO> implements BaseRepository<DTO, PO>, BaseConvertor<DTO, PO> {

    @Autowired
    protected SE baseService;

    protected Class<DTO> dtoClass = currentDtoClass();

    protected Class<PO> poClass = currentPoClass();

    private Class<DTO> currentDtoClass() {
        return (Class<DTO>) ReflectionKit.getSuperClassGenericType(this.getClass(), BaseRepositoryImpl.class, 2);
    }

    private Class<PO> currentPoClass() {
        return (Class<PO>) ReflectionKit.getSuperClassGenericType(this.getClass(), BaseRepositoryImpl.class, 1);
    }

    @Override
    public Class<DTO> getDTOClass() {
        return dtoClass;
    }

    @Override
    public Class<PO> getPOClass() {
        return poClass;
    }

    @Override
    public IPage<DTO> convertToDtoPages(IPage<DTO> dtoPage, IPage<PO> poPage) {
        dtoPage = dtoPage == null? new Page<>() : dtoPage;
        List<DTO> dtoList = convertToDto(poPage.getRecords());
        dtoPage.setPages(poPage.getPages());
        dtoPage.setCurrent(poPage.getCurrent());
        dtoPage.setTotal(poPage.getTotal());
        dtoPage.setSize(poPage.getSize());
        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public IPage<DTO> convertToDtoPages(IPage<PO> poPage) {
        return convertToDtoPages(null, poPage);
    }

    public String generateCode(String key) {
        IdUtil.getSnowflakeNextId();
        // todo 替换发号器
        return IdUtil.fastSimpleUUID();
    }

    @Override
    public boolean save(DTO dto) {
        return baseService.save(convertToPo(dto));
    }

    @Override
    public boolean saveBatch(Collection<DTO> dtoList) {
        return baseService.saveBatch(convertToPo(dtoList));
    }

    @Override
    public boolean saveBatch(Collection<DTO> dtoList, int batchSize) {
        return baseService.saveBatch(convertToPo(dtoList), batchSize);
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<DTO> entityList) {
        log.info("批量保存统计{}", entityList);
        return baseService.saveOrUpdateBatch(convertToPo(entityList));
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<DTO> entityList, int batchSize) {
        return baseService.saveOrUpdateBatch(convertToPo(entityList), batchSize);
    }

    @Override
    public boolean removeById(Serializable id) {
        return baseService.removeById(id);
    }

    @Override
    public boolean removeById(Serializable id, boolean useFill) {
        return baseService.removeById(id, useFill);
    }

    @Override
    public boolean removeById(DTO entity) {
        return baseService.removeById(convertToPo(entity));
    }

    @Override
    public boolean removeByMap(Map<String, Object> columnMap) {
        return baseService.removeByMap(columnMap);
    }

    @Override
    public boolean removeByIds(Collection<?> list) {
        return baseService.removeByIds(list);
    }

    @Override
    public boolean removeByIds(Collection<?> list, boolean useFill) {
        return baseService.removeByIds(list, useFill);
    }

    @Override
    public boolean removeBatchByIds(Collection<?> list) {
        return baseService.removeBatchByIds(list);
    }

    @Override
    public boolean removeBatchByIds(Collection<?> list, boolean useFill) {
        throw new UnsupportedOperationException("不支持的方法!");
    }

    @Override
    public boolean removeBatchByIds(Collection<?> list, int batchSize) {
        throw new UnsupportedOperationException("不支持的方法!");
    }

    @Override
    public boolean removeBatchByIds(Collection<?> list, int batchSize, boolean useFill) {
        throw new UnsupportedOperationException("不支持的方法!");
    }

    @Override
    public boolean updateById(DTO entity) {
        return baseService.updateById(convertToPo(entity));
    }

    @Override
    public boolean updateBatchById(Collection<DTO> entityList) {
        return baseService.updateBatchById(convertToPo(entityList));
    }

    @Override
    public boolean updateBatchById(Collection<DTO> entityList, int batchSize) {
        return baseService.updateBatchById(convertToPo(entityList), batchSize);
    }

    @Override
    public boolean saveOrUpdate(DTO entity) {
        return baseService.saveOrUpdate(convertToPo(entity));
    }

    @Override
    public DTO getById(Serializable id) {
        return convertToDto(baseService.getById(id));
    }

    @Override
    public List<DTO> listByIds(Collection<? extends Serializable> idList) {
        return convertToDto(baseService.listByIds(idList));
    }

    @Override
    public List<DTO> listByMap(Map<String, Object> columnMap) {
        return convertToDto(baseService.listByMap(columnMap));
    }

    @Override
    public <E extends IPage<DTO>> E page(E page) {
        IPage<PO> pagePO = new Page<>();
        baseService.page(pagePO);
        convertToDtoPages(page, pagePO);
        return page;
    }

}
