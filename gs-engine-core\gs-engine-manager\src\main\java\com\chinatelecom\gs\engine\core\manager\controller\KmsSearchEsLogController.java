package com.chinatelecom.gs.engine.core.manager.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.manager.service.SearchKmsLogEsService;
import com.chinatelecom.gs.engine.core.manager.vo.config.base.ESLogDelParam;
import com.chinatelecom.gs.engine.core.manager.vo.config.base.QueryKmsSearchMessageVO;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月28日
 */

@RestController
@Slf4j
@Tag(name = "知识库检索日志管理接口")
@RequestMapping(Apis.BASE_PREFIX + Apis.WEB_API + Apis.CONFIG + "/kms/search")
public class KmsSearchEsLogController {

    @Resource
    private SearchKmsLogEsService searchKmsLogEsService;

    @Operation(summary = "删除es埋点日志(知识库检索)")
    @PlatformRestApi(name = "删除es埋点日志(知识库检索)", groupName = "知识库搜索日志管理")
    @PostMapping("/es/log/delete")
    @AuditLog(businessType = "知识库检索日志管理接口", operType = "删除es埋点日志(知识库检索)", operDesc = "删除es埋点日志(知识库检索)", objId="null")
    public Result<Boolean> delete(@RequestBody ESLogDelParam param) {
        return Result.success(searchKmsLogEsService.deleteLogsBySearchTimeRange(param.getStartTime(), param.getEndTime()));
    }

    @Operation(summary = "通过logId在es查埋点日志(知识库检索)")
    @PlatformRestApi(name = "通过logId在es查埋点日志(知识库检索)", groupName = "知识库搜索日志管理")
    @GetMapping("/es/search/traceId/{logId}")
    @AuditLog(businessType = "知识库检索日志管理接口", operType = "通过logId在es查埋点日志(知识库检索)", operDesc = "通过logId在es查埋点日志(知识库检索)", objId="#logId")
    public Result<List<QueryKmsSearchMessageVO>> searchByTraceId(@PathVariable("logId") String logId) {
        return Result.success(searchKmsLogEsService.searchLogsByLogId(logId));
    }
}
