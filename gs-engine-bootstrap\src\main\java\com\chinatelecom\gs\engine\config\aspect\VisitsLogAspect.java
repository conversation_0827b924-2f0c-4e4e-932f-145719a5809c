package com.chinatelecom.gs.engine.config.aspect;

import com.chinatelecom.gs.engine.common.config.aspect.annotation.VisitsLog;
import com.chinatelecom.gs.engine.common.enums.VisitsDimensionEnum;
import com.chinatelecom.gs.engine.kms.model.vo.VisitsCreateParam;
import com.chinatelecom.gs.engine.kms.service.VisitsAppService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.EvaluationException;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * DebugLog
 * 打印入参和出参
 *
 * <AUTHOR>
 * @date 2022-11-13 15:45
 */
@Slf4j
@Aspect
@Component
@Order(value = 1)
public class VisitsLogAspect {

    /**
     * 返回值参数名称
     */
    public static final String RETURN_NAME = "returnValue";


    @Value("${gs.system.visitsEnabled:true}")
    private Boolean visitsEnabled;

    @Resource
    @Qualifier("defaultPoolExecutor")
    private ExecutorService defaultPoolExecutor;

    @Resource
    private VisitsAppService visitsAppService;


    /**
     * 用于SpEL表达式解析.
     */
    private SpelExpressionParser spelExpressionParser = new SpelExpressionParser();
    /**
     * 用于获取方法参数定义名字.
     */
    private DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();


    @Around("@within(visitsLog) || @annotation(visitsLog)")
    public Object aroundLog(ProceedingJoinPoint point, VisitsLog visitsLog) throws Throwable {
        Object result =  point.proceed();

        if (Boolean.TRUE.equals(visitsEnabled)) {
            if (visitsLog == null) {
                // 获取类上的注解
                visitsLog = point.getTarget().getClass().getDeclaredAnnotation(VisitsLog.class);
            }

            try {
                Map<String, Object> allArgs = new HashMap<>();
                Object[] args = point.getArgs();
                MethodSignature methodSignature = (MethodSignature) point.getSignature();
                String[] paramNames = nameDiscoverer.getParameterNames(methodSignature.getMethod());
                setArgsInfo(allArgs, paramNames, args, result);
                String paramKey = getValBySpEL(visitsLog.paramKey(), allArgs);


                HttpServletRequest request = getCurrentRequest();
                if (request == null) {
                    return result;
                }
                String uri = visitsLog.uri();
                if (StringUtils.isBlank(uri)) {
                    uri = (String) request.getAttribute(
                            org.springframework.web.servlet.HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
                }
                String method = request.getMethod();
                if (StringUtils.isBlank(method)) {
                    method = request.getMethod();
                }

                final String finalMethod = method;
                final String finalUri = uri;
                defaultPoolExecutor.submit(() -> saveRequest(finalMethod, finalUri, paramKey));

            } catch (Throwable e) {
                log.warn("记录用户访问信息异常,错误将忽略", e);
            }
        }

        return result;
    }

    private void setArgsInfo(Map<String, Object> allArgs, String[] paramNames, Object[] args, Object result) {
        for (int i = 0; i < args.length; i++) {
            allArgs.put(paramNames[i], args[i]);
        }
        allArgs.put(RETURN_NAME, result);
    }

    /**
     * 解析spEL表达式
     */
    private String getValBySpEL(String spEL, Map<String, Object> allArgs) {
        if (StringUtils.isBlank(spEL) || MapUtils.isEmpty(allArgs)) {
            return null;
        }
        Expression expression = spelExpressionParser.parseExpression(spEL);
        // spring的表达式上下文对象
        EvaluationContext context = new StandardEvaluationContext();
        for (Map.Entry<String, Object> entry : allArgs.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }

        try {
            Object obj = expression.getValue(context);
            return String.valueOf(obj);
        } catch (EvaluationException e) {
            log.error("表达解析异常", e);
        } catch (Exception e) {
            log.error("计算EL表达式异常", e);
        }
        return null;
    }

    public static HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            return attributes.getRequest();
        }
        return null;
    }

    private void saveRequest(String method, String uri, String requestParam) {
        log.debug("匹配method:{}, MVC路径：{}, param:{}", method, uri, requestParam);
        VisitsCreateParam param = new VisitsCreateParam();
        param.setDimension(VisitsDimensionEnum.USER);
        param.setMethod(method);
        param.setUri(uri);
        param.setParamKey(requestParam);
        visitsAppService.create(param);
    }

}
