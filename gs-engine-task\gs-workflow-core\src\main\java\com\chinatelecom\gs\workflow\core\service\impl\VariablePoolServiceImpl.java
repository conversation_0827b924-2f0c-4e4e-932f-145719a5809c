package com.chinatelecom.gs.workflow.core.service.impl;

import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.common.utils.VariableUtils;
import com.chinatelecom.gs.engine.core.sdk.rpc.GlobalVariableRpcApi;
import com.chinatelecom.gs.engine.core.sdk.vo.GlobalVariableDataDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.rpc.AgentVariableRpcApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.variable.AgentVariableDataDetailVO;
import com.chinatelecom.gs.engine.task.sdk.vo.variable.WorkFlowVariableDataDetailVO;
import com.chinatelecom.gs.workflow.core.service.CacheService;
import com.chinatelecom.gs.workflow.core.service.VariablePoolService;
import com.chinatelecom.gs.workflow.core.service.WorkFlowVariableAppService;
import com.chinatelecom.gs.workflow.core.workflow.core.context.VariableRunContext;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.WriteModeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.utils.CacheKeyUtils;
import com.chinatelelcom.gs.engine.sdk.common.enums.ParamTypeEnum;
import com.chinatelelcom.gs.engine.sdk.common.enums.VariableTypeEnum;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2025/01/17
 */
@Slf4j
@Service
public class VariablePoolServiceImpl implements VariablePoolService {

    @Resource
    private CacheService cacheService;

    @Resource
    private GlobalVariableRpcApi globalVariableRpcApi;

    @Resource
    private AgentVariableRpcApi agentVariableRpcApi;

    @Resource
    private WorkFlowVariableAppService workFlowVariableAppService;

    @Override
    public Object read(String[] selector, VariableRunContext context) {
        //1.session维度存储中，读变量
        String varCacheKey = getSessionVarCacheKey(context.getSessionId());
        String varSelectorKey = getSelectorKey(selector, context);
        String varCacheValueStr = (String) cacheService.hGet(varCacheKey, varSelectorKey);
        Object existValue = parseCacheData(varCacheValueStr);
        if (existValue != null) {
            return existValue;
        }
        //2.不存在，则读默认值
        VariableBaseInfo varBaseInfo = getVariableBaseInfo(selector, context);
        return varBaseInfo != null ? varBaseInfo.getDefaultValue() : null;
    }

    /**
     * 读取会话所有变量
     *
     * @param isTest    boolean
     * @param sessionId String
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> readAll(boolean isTest, String sessionId) {
        //1.session维度存储中，读变量
        String varCacheKey = getSessionVarCacheKey(sessionId);
        Map<String, String> variableMap = cacheService.hGetAll(varCacheKey);
        HashMap<String, Object> result = new HashMap<>();
        if (variableMap != null && !variableMap.isEmpty()) {
            for (Map.Entry<String, String> entry : variableMap.entrySet()) {
                String[] selectorArr = entry.getKey().split("\\.");
                Object existValue = parseCacheData(entry.getValue());
                if (existValue != null) {
                    result.put(entry.getKey(), existValue);
                    continue;
                }
                //2.不存在，则读默认值
                VariableRunContext runContext = new VariableRunContext();
                runContext.setSessionId(sessionId);
                runContext.setTest(isTest);
                VariableBaseInfo varBaseInfo = getVariableBaseInfo(selectorArr, runContext);
                if (varBaseInfo != null) {
                    result.put(entry.getKey(), varBaseInfo.getDefaultValue());
                }
            }
        }
        return result;
    }

    private Object parseCacheData(String cacheValue) {
        VariableCacheData data = JsonUtils.parseObject(cacheValue, VariableCacheData.class);
        if (data == null || data.getValueStr() == null) {
            return null;
        }
        return VariableUtils.parseValue(data.getValueStr(), data.getDataType());
    }

    @Override
    public boolean write(String[] selector, VariableRunContext context, Object varValue, String writeMode) {
        try {
            VariableBaseInfo varBaseInfo = getVariableBaseInfo(selector, context);
            if (varBaseInfo == null) {
                throw new BizException("AA082", "变量写入异常");
            }
            WriteModeEnum modeEnum = WriteModeEnum.getByValue(writeMode);
            switch (modeEnum) {
                case SET:
                    setCacheData(selector, context, varValue, varBaseInfo.getDataType());
                    break;
                case ADD:
                    addCacheData(selector, context, varValue, varBaseInfo.getDataType());
                    break;
                case CLEAR:
                    clearCacheData(selector, context);
                    break;
                default:
                    throw new BizException("AA082", "变量写入方式错误！");
            }
            return true;
        } catch (Exception ex) {
            log.error("变量写入异常, selector:{}", selector, ex);
            throw new BizException("AA082", "变量写入异常", ex);
        }
    }

    private void setCacheData(String[] selector, VariableRunContext context, Object varValue, int dataType) {
        VariableUtils.checkDataType(varValue, dataType);
        String varCacheKey = getSessionVarCacheKey(context.getSessionId());
        String varSelectorKey = getSelectorKey(selector, context);
        VariableCacheData cacheData = new VariableCacheData(dataType, VariableUtils.toJsonString(varValue, dataType));
        cacheService.hPut(varCacheKey, varSelectorKey, JsonUtils.toJsonString(cacheData));
        cacheService.expire(varCacheKey, 30, TimeUnit.MINUTES);
    }

    private void addCacheData(String[] selector, VariableRunContext context, Object varValue, int listDataType) {
        int eleDataType = getElementDataType(listDataType);
        VariableUtils.checkDataType(varValue, eleDataType);
        String varCacheKey = getSessionVarCacheKey(context.getSessionId());
        String varSelectorKey = getSelectorKey(selector, context);
        String cacheDataStr = (String) cacheService.hGet(varCacheKey, varSelectorKey);
        List<Object> targetVarValue = new ArrayList<>();
        if (cacheDataStr != null) {
            Object originVarValue = parseCacheData(cacheDataStr);
            if (originVarValue instanceof List<?> list) {
                targetVarValue.addAll(list);
            }
        }
        targetVarValue.add(varValue);
        VariableCacheData targetVarData = new VariableCacheData(listDataType, JsonUtils.toJsonString(targetVarValue));
        cacheService.hPut(varCacheKey, varSelectorKey, JsonUtils.toJsonString(targetVarData));
        cacheService.expire(varCacheKey, 30, TimeUnit.MINUTES);
    }

    private int getElementDataType(int listDataType) {
        ParamTypeEnum dataTypeEnum = ParamTypeEnum.getByValue(listDataType);
        return switch (dataTypeEnum) {
            case ARRAY_STRING -> ParamTypeEnum.STRING.getCode();
            case ARRAY_INTEGER -> ParamTypeEnum.INTEGER.getCode();
            case ARRAY_NUMBER -> ParamTypeEnum.NUMBER.getCode();
            case ARRAY_BOOLEAN -> ParamTypeEnum.BOOLEAN.getCode();
            case ARRAY_OBJECT -> ParamTypeEnum.OBJECT.getCode();
            default -> -1;
        };
    }

    private void clearCacheData(String[] selector, VariableRunContext context) {
        String varCacheKey = getSessionVarCacheKey(context.getSessionId());
        String varSelectorKey = getSelectorKey(selector, context);
        cacheService.hDelete(varCacheKey, varSelectorKey);
    }

    private String getSessionVarCacheKey(String sessionId) {
        return CacheKeyUtils.sessionVariableCacheKey(sessionId);
    }

    private String getSelectorKey(String[] selector, VariableRunContext context) {
        VariableUtils.validateSelector(selector);
        VariableTypeEnum varType = VariableTypeEnum.getEnumByCode(VariableUtils.getVarType(selector));
        return switch (varType) {
            case GLOBAL -> StringUtils.joinWith(".", selector);
            case BOT -> StringUtils.joinWith(".", selector[0], context.getAgentCode(), selector[2]);
            case WORKFLOW -> StringUtils.joinWith(".", selector[0], context.getWorkFlowId(), selector[2]);
        };
    }

    private VariableBaseInfo getVariableBaseInfo(String[] selector, VariableRunContext context) {
        VariableTypeEnum varType = VariableTypeEnum.getEnumByCode(VariableUtils.getVarType(selector));
        switch (varType) {
            case GLOBAL:
                return getGlobalVariable(selector, context);
            case BOT:
                return getBotVariable(selector, context);
            case WORKFLOW:
                return getWorkFlowVariable(selector, context);
        }
        throw new BizException("AA082", "变量类型错误！");
    }

    private VariableBaseInfo getGlobalVariable(String[] selector, VariableRunContext context) {
        Result<GlobalVariableDataDetailVO> result = globalVariableRpcApi.getDetail(VariableUtils.getVarName(selector));
        if (!result.isSuccess() && result.getData() == null) {
            return null;
        }
        if (result.getData() == null) {
            return null;
        }
        Map<String, Object> globalMap = context.getGlobalMap();
        // 根据变量编码匹配输入值
        GlobalVariableDataDetailVO data = result.getData();
        String variableCode = data.getVariableCode();
        if (globalMap != null && globalMap.containsKey(variableCode)) {
            Object value = globalMap.get(variableCode);
            if (value != null) {
                return new VariableBaseInfo(data.getDataType(), value);
            }
        }

        VariableBaseInfo baseInfo = new VariableBaseInfo();
        baseInfo.setDataType(result.getData().getDataType());
        baseInfo.setDefaultValue(VariableUtils.parseValue(result.getData().getDefaultValue(), result.getData().getDataType()));
        return baseInfo;
    }


    private VariableBaseInfo getBotVariable(String[] selector, VariableRunContext context) {
        Result<AgentVariableDataDetailVO> result = agentVariableRpcApi.getDetail(VariableUtils.getReferId(selector, context.toContextMap()), VariableUtils.getVarName(selector), context.isTest());
        if (!result.isSuccess() && result.getData() == null) {
            return null;
        }
        VariableBaseInfo baseInfo = new VariableBaseInfo();
        baseInfo.setDataType(result.getData().getDataType());
        baseInfo.setDefaultValue(VariableUtils.parseValue(result.getData().getDefaultValue(), result.getData().getDataType()));
        return baseInfo;
    }

    private VariableBaseInfo getWorkFlowVariable(String[] selector, VariableRunContext context) {
        WorkFlowVariableDataDetailVO data = workFlowVariableAppService.getDetailByName(
                VariableUtils.getReferId(selector, context.toContextMap()), VariableUtils.getVarName(selector), context.isTest());
        if (data == null || data.getDefaultValue() == null) {
            return null;
        }
        VariableBaseInfo baseInfo = new VariableBaseInfo();
        baseInfo.setDataType(data.getDataType());
        baseInfo.setDefaultValue(VariableUtils.parseValue(data.getDefaultValue(), data.getDataType()));
        return baseInfo;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class VariableCacheData {
        private int dataType;
        private String valueStr;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class VariableBaseInfo {
        private int dataType;
        private Object defaultValue;
    }
}
