package com.chinatelecom.gs.engine.channel.dao.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/22 10:49
 * @description 渠道配置信息表
 */
@Setter
@Getter
@TableName("channel_config_info")
public class ChannelConfigPO extends BasePO implements Serializable {
    @Serial
    private static final long serialVersionUID = 3522930686823879250L;

    @TableField("channel_id")
    private String channelId;

    @TableField("config_key")
    private String configKey;

    @TableField("config_value")
    private String configValue;
}
