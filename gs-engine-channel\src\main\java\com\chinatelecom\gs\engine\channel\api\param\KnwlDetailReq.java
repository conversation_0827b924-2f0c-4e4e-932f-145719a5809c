package com.chinatelecom.gs.engine.channel.api.param;

import com.chinatelecom.gs.engine.channel.api.vo.CommonAuthParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * @description: 知识详情
 * @author: xktang
 * @date: 2024/5/15 下午3:58
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnwlDetailReq extends CommonAuthParam implements Serializable {
    @NotEmpty(message = "知识编码不能为空")
    private String knwlCode;
}
