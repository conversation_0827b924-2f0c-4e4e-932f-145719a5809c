package com.chinatelecom.gs.engine.core.corekit.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @author: Wei
 * @date: 2025-02-05 10:10
 */
@Schema(description = "敏感词管理")
@Data
public class SensitiveRequest {

    /**
     * 敏感词ID
     */
    @Schema(description = "敏感词ID, 编辑时必传")
    private Long id;

    /**
     * 标准词
     */
    @Schema(description = "标准词")
    @NotBlank(message = "标准词不能为空")
    private String standardWords;

    /**
     * 同义词（使用,分割）
     */
    @Schema(description = "同义词（使用,分割）")
    private String synonymousWords;

}
